"""
Tests for the diagram text recognition service.
"""
import os
import io
import pytest
import tempfile
import numpy as np
from unittest.mock import patch, MagicMock
from PIL import Image, ImageDraw, ImageFont

from image_analysis.diagram_text import (
    DiagramTextRecognitionService, 
    TextElement, 
    TextAlignment,
    DiagramElement
)
from image_analysis.ocr import OCRService


def create_test_diagram(size=(500, 300), bg_color=(255, 255, 255)):
    """Create a test diagram image for testing."""
    # Create a blank image with background color
    img = Image.new("RGB", size, bg_color)
    draw = ImageDraw.Draw(img)
    
    # Try to use a font that's likely to be available
    try:
        # Try to use Arial font if available
        font = ImageFont.truetype("arial.ttf", 12)
    except IOError:
        # Fall back to default font
        font = ImageFont.load_default()
    
    # Draw some shapes to represent components
    draw.rectangle((50, 50, 150, 100), outline=(0, 0, 0), width=2)
    draw.rectangle((300, 50, 400, 100), outline=(0, 0, 0), width=2)
    draw.rectangle((175, 150, 275, 200), outline=(0, 0, 0), width=2)
    
    # Draw some lines to represent connections
    draw.line((150, 75, 300, 75), fill=(0, 0, 0), width=2)
    draw.line((225, 150, 225, 100), fill=(0, 0, 0), width=2)
    
    # Add some text labels
    draw.text((75, 75), "Component A", fill=(0, 0, 0), font=font)
    draw.text((325, 75), "Component B", fill=(0, 0, 0), font=font)
    draw.text((200, 175), "Component C", fill=(0, 0, 0), font=font)
    draw.text((200, 75), "connects", fill=(0, 0, 0), font=font)
    draw.text((230, 125), "connects", fill=(0, 0, 0), font=font)
    draw.text((200, 25), "Architecture Diagram", fill=(0, 0, 0), font=font)
    draw.text((50, 250), "This is an annotation explaining the diagram", fill=(0, 0, 0), font=font)
    
    # Save to a bytes buffer
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format="PNG")
    img_byte_arr.seek(0)
    
    return img, img_byte_arr


class TestDiagramTextRecognitionService:
    """Tests for the DiagramTextRecognitionService class."""
    
    def setup_method(self):
        """Set up test environment."""
        # Create a temporary directory for test images
        self.temp_dir = tempfile.mkdtemp()
        
        # Mock OCR service
        self.mock_ocr_service = MagicMock(spec=OCRService)
        
        # Create diagram text recognition service with mock OCR service
        self.service = DiagramTextRecognitionService(self.mock_ocr_service)
        
        # Create a test diagram
        self.test_img, self.test_img_bytes = create_test_diagram()
        self.test_img_path = os.path.join(self.temp_dir, "test_diagram.png")
        self.test_img.save(self.test_img_path)
    
    def teardown_method(self):
        """Clean up test environment."""
        # Remove temporary files
        if os.path.exists(self.test_img_path):
            os.remove(self.test_img_path)
        
        # Remove temporary directory
        if os.path.exists(self.temp_dir):
            os.rmdir(self.temp_dir)
    
    def test_text_element_initialization(self):
        """Test initialization of TextElement class."""
        # Create a text element
        element = TextElement(
            text="Test Text",
            bbox=(10, 20, 30, 40),
            confidence=0.8,
            alignment=TextAlignment.CENTER
        )
        
        # Check attributes
        assert element.text == "Test Text"
        assert element.bbox == (10, 20, 30, 40)
        assert element.confidence == 0.8
        assert element.alignment == TextAlignment.CENTER
    
    def test_text_element_to_dict(self):
        """Test conversion of TextElement to dictionary."""
        # Create a text element
        element = TextElement(
            text="Test Text",
            bbox=(10, 20, 30, 40),
            confidence=0.8,
            alignment=TextAlignment.CENTER
        )
        
        # Convert to dictionary
        element_dict = element.to_dict()
        
        # Check dictionary
        assert element_dict["text"] == "Test Text"
        assert element_dict["bbox"] == (10, 20, 30, 40)
        assert element_dict["confidence"] == 0.8
        assert element_dict["alignment"] == TextAlignment.CENTER
    
    def test_extract_text_elements(self):
        """Test extraction of text elements from a diagram."""
        # Mock OCR service response
        self.mock_ocr_service.extract_text_with_layout.return_value = {
            "text": "Component A Component B Component C connects Architecture Diagram",
            "blocks": [
                {
                    "text": "Component A",
                    "lines": [
                        {
                            "text": "Component A",
                            "words": [
                                {
                                    "text": "Component",
                                    "bbox": [75, 75, 50, 12],
                                    "confidence": 0.9
                                },
                                {
                                    "text": "A",
                                    "bbox": [130, 75, 10, 12],
                                    "confidence": 0.95
                                }
                            ]
                        }
                    ]
                },
                {
                    "text": "Component B",
                    "lines": [
                        {
                            "text": "Component B",
                            "words": [
                                {
                                    "text": "Component",
                                    "bbox": [325, 75, 50, 12],
                                    "confidence": 0.9
                                },
                                {
                                    "text": "B",
                                    "bbox": [380, 75, 10, 12],
                                    "confidence": 0.95
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        
        # Extract text elements
        elements = self.service.extract_text_elements(self.test_img_path)
        
        # Check result
        assert len(elements) == 4  # 4 words in the mock response
        assert all(isinstance(element, TextElement) for element in elements)
        assert elements[0].text == "Component"
        assert elements[1].text == "A"
    
    def test_extract_labels(self):
        """Test extraction of labels from a diagram."""
        # Mock OCR service response
        self.mock_ocr_service.extract_text_with_layout.return_value = {
            "text": "Component A Component B Component C connects Architecture Diagram",
            "blocks": [
                {
                    "text": "Component A",
                    "lines": [
                        {
                            "text": "Component A",
                            "words": [
                                {
                                    "text": "Component",
                                    "bbox": [75, 75, 50, 12],
                                    "confidence": 0.9
                                },
                                {
                                    "text": "A",
                                    "bbox": [130, 75, 10, 12],
                                    "confidence": 0.95
                                }
                            ]
                        }
                    ]
                },
                {
                    "text": "Architecture Diagram",
                    "lines": [
                        {
                            "text": "Architecture Diagram",
                            "words": [
                                {
                                    "text": "Architecture",
                                    "bbox": [200, 25, 70, 12],
                                    "confidence": 0.9
                                },
                                {
                                    "text": "Diagram",
                                    "bbox": [275, 25, 50, 12],
                                    "confidence": 0.95
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        
        # Mock image loading and processing
        with patch('cv2.imread') as mock_imread, \
             patch('cv2.cvtColor') as mock_cvtColor, \
             patch('cv2.Canny') as mock_canny, \
             patch('cv2.HoughLinesP') as mock_hough_lines:
            
            # Mock image data
            mock_imread.return_value = np.zeros((300, 500, 3), dtype=np.uint8)
            mock_cvtColor.return_value = np.zeros((300, 500), dtype=np.uint8)
            mock_canny.return_value = np.zeros((300, 500), dtype=np.uint8)
            mock_hough_lines.return_value = None
            
            # Extract labels
            labels = self.service.extract_labels(self.test_img_path)
            
            # Check result
            assert "component" in labels
            assert "connection" in labels
            assert "annotation" in labels
            assert "title" in labels
            assert "other" in labels
    
    def test_extract_text_with_positions(self):
        """Test extraction of text with positions from a diagram."""
        # Mock extract_text_elements and extract_labels methods
        with patch.object(self.service, 'extract_text_elements') as mock_extract_elements, \
             patch.object(self.service, 'extract_labels') as mock_extract_labels:
            
            # Mock return values
            mock_extract_elements.return_value = [
                TextElement("Component", (75, 75, 50, 12), 0.9, TextAlignment.CENTER),
                TextElement("A", (130, 75, 10, 12), 0.95, TextAlignment.CENTER)
            ]
            
            mock_extract_labels.return_value = {
                "component": [TextElement("Component A", (75, 75, 65, 12), 0.9, TextAlignment.CENTER)],
                "connection": [],
                "annotation": [],
                "title": [],
                "other": []
            }
            
            # Extract text with positions
            result = self.service.extract_text_with_positions(self.test_img_path)
            
            # Check result
            assert "text_elements" in result
            assert "labels" in result
            assert len(result["text_elements"]) == 2
            assert "component" in result["labels"]
            assert len(result["labels"]["component"]) == 1
    
    def test_determine_alignment(self):
        """Test determination of text alignment."""
        # Mock image loading
        with patch('cv2.imread') as mock_imread:
            # Mock image data
            mock_imread.return_value = np.zeros((300, 500, 3), dtype=np.uint8)
            
            # Test different positions
            left_alignment = self.service._determine_alignment(self.test_img_path, (50, 150, 20, 10))
            center_alignment = self.service._determine_alignment(self.test_img_path, (250, 150, 20, 10))
            right_alignment = self.service._determine_alignment(self.test_img_path, (450, 150, 20, 10))
            
            # Check results
            assert left_alignment == TextAlignment.LEFT
            assert center_alignment == TextAlignment.CENTER
            assert right_alignment == TextAlignment.RIGHT
    
    def test_is_title(self):
        """Test identification of title elements."""
        # Create test elements
        title_element = TextElement("Title", (200, 20, 100, 20), 0.9, TextAlignment.CENTER)
        non_title_element = TextElement("Not Title", (200, 150, 100, 20), 0.9, TextAlignment.CENTER)
        
        # Check results
        assert self.service._is_title(title_element, 500, 300) is True
        assert self.service._is_title(non_title_element, 500, 300) is False
    
    def test_nonexistent_file(self):
        """Test handling of nonexistent files."""
        # Try to extract text elements from a nonexistent file
        elements = self.service.extract_text_elements("nonexistent_file.png")
        
        # Check result
        assert elements == []
    
    def test_svg_file(self):
        """Test handling of SVG files."""
        # Create a test SVG file
        svg_path = os.path.join(self.temp_dir, "test.svg")
        with open(svg_path, "w") as f:
            f.write('<svg width="100" height="100"></svg>')
        
        # Try to extract text elements from the SVG file
        elements = self.service.extract_text_elements(svg_path)
        
        # Check result
        assert elements == []
        
        # Clean up
        os.remove(svg_path)  
    def test_diagram_element_initialization(self):
        """Test initialization of DiagramElement class."""
        # Create a diagram element
        element = DiagramElement(
            element_type="rectangle",
            bbox=(10, 20, 30, 40),
            confidence=0.8
        )
        
        # Check attributes
        assert element.element_type == "rectangle"
        assert element.bbox == (10, 20, 30, 40)
        assert element.confidence == 0.8
        assert element.related_text == []
    
    def test_diagram_element_to_dict(self):
        """Test conversion of DiagramElement to dictionary."""
        # Create a diagram element
        element = DiagramElement(
            element_type="rectangle",
            bbox=(10, 20, 30, 40),
            confidence=0.8
        )
        
        # Add related text
        text_element = TextElement(
            text="Test Text",
            bbox=(15, 25, 20, 10),
            confidence=0.9,
            alignment=TextAlignment.CENTER
        )
        element.related_text.append(text_element)
        
        # Convert to dictionary
        element_dict = element.to_dict()
        
        # Check dictionary
        assert element_dict["element_type"] == "rectangle"
        assert element_dict["bbox"] == (10, 20, 30, 40)
        assert element_dict["confidence"] == 0.8
        assert len(element_dict["related_text"]) == 1
        assert element_dict["related_text"][0]["text"] == "Test Text"
    
    def test_detect_diagram_elements(self):
        """Test detection of diagram elements."""
        # Mock image loading and processing
        with patch('cv2.imread') as mock_imread, \
             patch('cv2.cvtColor') as mock_cvtColor, \
             patch('cv2.GaussianBlur') as mock_blur, \
             patch('cv2.Canny') as mock_canny, \
             patch('cv2.findContours') as mock_find_contours, \
             patch('cv2.HoughLinesP') as mock_hough_lines, \
             patch('cv2.contourArea') as mock_contour_area, \
             patch('cv2.boundingRect') as mock_bound_rect, \
             patch('cv2.arcLength') as mock_arc_length, \
             patch('cv2.approxPolyDP') as mock_approx_poly:
            
            # Mock image data
            mock_imread.return_value = np.zeros((300, 500, 3), dtype=np.uint8)
            mock_cvtColor.return_value = np.zeros((300, 500), dtype=np.uint8)
            mock_blur.return_value = np.zeros((300, 500), dtype=np.uint8)
            mock_canny.return_value = np.zeros((300, 500), dtype=np.uint8)
            
            # Mock contours
            mock_contour1 = np.array([[[50, 50]], [[150, 50]], [[150, 100]], [[50, 100]]])
            mock_contour2 = np.array([[[300, 50]], [[400, 50]], [[400, 100]], [[300, 100]]])
            mock_find_contours.return_value = ([mock_contour1, mock_contour2], None)
            
            # Mock contour properties
            mock_contour_area.side_effect = [5000, 5000]  # Areas above threshold
            mock_bound_rect.side_effect = [(50, 50, 100, 50), (300, 50, 100, 50)]
            mock_arc_length.return_value = 100
            mock_approx_poly.side_effect = [
                np.array([[[50, 50]], [[150, 50]], [[150, 100]], [[50, 100]]]),  # 4 vertices (rectangle)
                np.array([[[300, 50]], [[400, 50]], [[400, 100]], [[300, 100]]])  # 4 vertices (rectangle)
            ]
            
            # Mock lines
            mock_hough_lines.return_value = np.array([[[150, 75, 300, 75]]])
            
            # Detect diagram elements
            elements = self.service.detect_diagram_elements(self.test_img_path)
            
            # Check result
            assert len(elements) == 3  # 2 rectangles + 1 line
            assert elements[0].element_type == "rectangle"
            assert elements[1].element_type == "rectangle"
            assert elements[2].element_type == "line"
    
    def test_associate_text_with_elements(self):
        """Test association of text with diagram elements."""
        # Mock extract_text_elements and detect_diagram_elements methods
        with patch.object(self.service, 'extract_text_elements') as mock_extract_elements, \
             patch.object(self.service, 'detect_diagram_elements') as mock_detect_elements:
            
            # Mock text elements
            text_elements = [
                TextElement("Component A", (75, 75, 65, 12), 0.9, TextAlignment.CENTER),
                TextElement("Component B", (325, 75, 65, 12), 0.9, TextAlignment.CENTER)
            ]
            mock_extract_elements.return_value = text_elements
            
            # Mock diagram elements
            diagram_elements = [
                DiagramElement("rectangle", (50, 50, 100, 50), 0.8),
                DiagramElement("rectangle", (300, 50, 100, 50), 0.8)
            ]
            mock_detect_elements.return_value = diagram_elements
            
            # Associate text with elements
            result = self.service.associate_text_with_elements(self.test_img_path)
            
            # Check result
            assert "elements" in result
            assert "unassociated_text" in result
            assert len(result["elements"]) == 2
            
            # Check that text was associated with the correct elements
            assert len(diagram_elements[0].related_text) == 1
            assert diagram_elements[0].related_text[0].text == "Component A"
            assert len(diagram_elements[1].related_text) == 1
            assert diagram_elements[1].related_text[0].text == "Component B"
    
    def test_get_text_element_relationships(self):
        """Test getting relationships between text elements."""
        # Mock extract_text_elements method
        with patch.object(self.service, 'extract_text_elements') as mock_extract_elements:
            
            # Mock text elements
            text_elements = [
                TextElement("Title", (200, 20, 100, 20), 0.9, TextAlignment.CENTER),
                TextElement("Component A", (75, 75, 65, 12), 0.9, TextAlignment.CENTER),
                TextElement("Component B", (325, 75, 65, 12), 0.9, TextAlignment.CENTER),
                TextElement("  Subcomponent B1", (340, 85, 50, 10), 0.9, TextAlignment.LEFT)
            ]
            mock_extract_elements.return_value = text_elements
            
            # Get text element relationships
            result = self.service.get_text_element_relationships(self.test_img_path)
            
            # Check result
            assert "groups" in result
            assert "hierarchies" in result
            assert len(result["groups"]) > 0
            
            # Check hierarchical relationships
            assert len(result["hierarchies"]) > 0
            assert result["hierarchies"][0]["parent"]["text"] == "Component B"
            assert result["hierarchies"][0]["children"][0]["text"] == "  Subcomponent B1"
    
    def test_combined_alignment(self):
        """Test determination of combined text alignment."""
        # Mock image loading
        with patch('cv2.imread') as mock_imread:
            # Mock image data
            mock_imread.return_value = np.zeros((300, 500, 3), dtype=np.uint8)
            
            # Test corner positions
            top_left = self.service._determine_alignment(self.test_img_path, (50, 50, 20, 10))
            top_right = self.service._determine_alignment(self.test_img_path, (450, 50, 20, 10))
            bottom_left = self.service._determine_alignment(self.test_img_path, (50, 250, 20, 10))
            bottom_right = self.service._determine_alignment(self.test_img_path, (450, 250, 20, 10))
            
            # Check results
            assert top_left == TextAlignment.TOP_LEFT
            assert top_right == TextAlignment.TOP_RIGHT
            assert bottom_left == TextAlignment.BOTTOM_LEFT
            assert bottom_right == TextAlignment.BOTTOM_RIGHT