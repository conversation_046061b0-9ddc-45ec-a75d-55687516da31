#!/usr/bin/env python3
"""
Test script to verify the frontend fix for image analysis.
"""

import sys
import os

# Add the frontend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Frontend'))

def test_user_message_approach():
    """Test that the new AskUserMessage approach works."""
    try:
        # Test the logic used in the fixed code
        choice_responses = ['aws', 'openrouter', 'AWS', 'OPENROUTER', ' aws ', ' openrouter ']

        for response in choice_responses:
            analysis_choice_value = response.lower().strip()
            if analysis_choice_value in ['aws', 'openrouter']:
                use_openrouter = analysis_choice_value == "openrouter"
                print(f"   '{response}' -> use_openrouter: {use_openrouter}")
            else:
                print(f"   '{response}' -> Invalid choice")

        print("✅ User message approach test passed")
        return True

    except Exception as e:
        print(f"❌ User message approach test failed: {e}")
        return False

def test_imports():
    """Test that all required imports work."""
    try:
        import chainlit as cl
        import aiohttp
        import json
        import asyncio
        from typing import Dict, Any, Optional, List
        import os
        import sys
        from dotenv import load_dotenv
        import logging
        import base64
        from io import BytesIO
        from PIL import Image
        
        print("✅ All imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_chainlit_app_syntax():
    """Test that the chainlit app file has valid syntax."""
    try:
        import ast

        with open('Frontend/chainlit_app.py', 'r', encoding='utf-8') as f:
            source_code = f.read()

        # Parse the source code to check for syntax errors
        ast.parse(source_code)

        print("✅ Chainlit app syntax is valid")
        return True

    except SyntaxError as e:
        print(f"❌ Syntax error in chainlit_app.py: {e}")
        print(f"   Line {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ Error checking syntax: {e}")
        return False

def main():
    """Main test function."""
    print("FRONTEND FIX VERIFICATION")
    print("=" * 50)
    
    # Test imports
    imports_ok = test_imports()
    
    # Test syntax
    syntax_ok = test_chainlit_app_syntax()
    
    # Test user message approach
    user_msg_ok = test_user_message_approach()
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"Syntax: {'✅ PASS' if syntax_ok else '❌ FAIL'}")
    print(f"User Message Logic: {'✅ PASS' if user_msg_ok else '❌ FAIL'}")

    if imports_ok and syntax_ok and user_msg_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ The frontend fix should resolve the image analysis error.")
        print("\n📋 Next steps:")
        print("   1. Restart the Chainlit frontend")
        print("   2. Try the /analyze command again")
        print("   3. Upload an image and select an analysis method")
    else:
        print("\n❌ SOME TESTS FAILED")
        print("   Please check the errors above and fix them before proceeding.")

if __name__ == "__main__":
    main()
