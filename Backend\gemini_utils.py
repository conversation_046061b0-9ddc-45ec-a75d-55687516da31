# gemini_utils.py - Direct integration with Google Gemini API for vision models
import os
import json
import base64
import requests
from typing import Dict, Any, Optional
import logging
from PIL import Image
from io import BytesIO
from dotenv import load_dotenv

# Load environment variables from the parent directory
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '.env'))

logger = logging.getLogger(__name__)

def analyze_image_with_gemini(image_bytes: bytes, prompt: str = None) -> Dict[str, Any]:
    """
    Analyze image using Google Gemini API directly.
    
    Args:
        image_bytes: Image file as bytes
        prompt: Optional custom prompt to use for analysis
    
    Returns:
        Dictionary containing analysis results
    """
    try:
        # Get API key from environment variable
        api_key = os.getenv('GOOGLE_GEMINI_API_KEY')
        if not api_key:
            return {
                "status": "error",
                "error": "GOOGLE_GEMINI_API_KEY environment variable not set",
                "analysis": "Failed to analyze image with Google Gemini API."
            }
        
        # Check image size (API may have limits)
        if len(image_bytes) > 20 * 1024 * 1024:  # 20MB limit
            return {
                "status": "error",
                "error": f"Image size ({len(image_bytes) / (1024 * 1024):.2f}MB) exceeds the 20MB limit",
                "analysis": "Image is too large for processing. Please use a smaller image."
            }
            
        # Resize image if it's larger than 5MB to optimize processing
        if len(image_bytes) > 5 * 1024 * 1024:
            try:
                # Open image and resize while maintaining aspect ratio
                with Image.open(BytesIO(image_bytes)) as img:
                    # Calculate new dimensions (maintaining aspect ratio)
                    max_size = 1600  # Maximum dimension in pixels
                    ratio = min(max_size / img.width, max_size / img.height)
                    new_size = (int(img.width * ratio), int(img.height * ratio))
                    
                    # Resize image
                    img = img.resize(new_size, Image.LANCZOS)
                    
                    # Save to BytesIO
                    buffer = BytesIO()
                    img.save(buffer, format="JPEG", quality=85)
                    image_bytes = buffer.getvalue()
                    
                    logger.info(f"Resized image to {len(image_bytes) / (1024 * 1024):.2f}MB")
            except Exception as e:
                logger.warning(f"Failed to resize large image: {e}")
                # Continue with original image
        
        # Convert image to base64
        base64_image = base64.b64encode(image_bytes).decode('utf-8')
        
        # Determine image format
        image_format = "image/jpeg"
        try:
            with Image.open(BytesIO(image_bytes)) as img:
                if img.format:
                    image_format = f"image/{img.format.lower()}"
        except:
            pass  # Default to jpeg
            
        # Default prompt if none provided
        if not prompt:
            prompt = "Please analyze this image in detail. If it's an architecture diagram, explain the components and their relationships. If it's a screenshot containing an error, explain what the error is and suggest possible solutions. Provide a comprehensive analysis of what you see in the image."
        
        # Get model ID from environment variable
        model_id = os.getenv('GOOGLE_GEMINI_MODEL_ID', 'gemini-2.0-flash-exp')
        
        # Prepare the request payload for Gemini API
        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": prompt
                        },
                        {
                            "inline_data": {
                                "mime_type": image_format,
                                "data": base64_image
                            }
                        }
                    ]
                }
            ],
            "generationConfig": {
                "temperature": 0.2,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 2048
            },
            "safetySettings": [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_HATE_SPEECH",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                }
            ]
        }
        
        # Set up headers
        headers = {
            "Content-Type": "application/json"
        }
        
        # Construct the API URL
        api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{model_id}:generateContent?key={api_key}"
        
        # Make the API request
        logger.info(f"Sending request to Google Gemini API with model: {model_id}")
        response = requests.post(
            url=api_url,
            headers=headers,
            data=json.dumps(payload),
            timeout=90  # Increase timeout for image processing
        )
        
        # Log the response status for debugging
        logger.info(f"Google Gemini API response status: {response.status_code}")
        
        # Check if the request was successful
        if response.status_code == 200:
            try:
                response_data = response.json()
                logger.info(f"Google Gemini API response data keys: {response_data.keys()}")
                
                # Extract the analysis text from the response
                if 'candidates' in response_data and len(response_data['candidates']) > 0:
                    candidate = response_data['candidates'][0]
                    logger.info(f"Candidate structure: {candidate.keys()}")

                    if 'content' in candidate and 'parts' in candidate['content']:
                        parts = candidate['content']['parts']
                        logger.info(f"Parts structure: {parts}")

                        if len(parts) > 0 and 'text' in parts[0]:
                            analysis = parts[0]['text']
                            logger.info(f"Extracted analysis length: {len(analysis)}")

                            return {
                                "status": "success",
                                "analysis": analysis,
                                "model_id": model_id
                            }
                        else:
                            logger.error(f"No text in response parts: {parts}")
                            return {
                                "status": "error",
                                "error": "No text content in Gemini API response",
                                "raw_response": str(response_data),
                                "analysis": "Failed to analyze image with Google Gemini API due to unexpected response format."
                            }
                    else:
                        logger.error(f"Unexpected candidate structure: {candidate}")
                        return {
                            "status": "error",
                            "error": "Unexpected response structure from Google Gemini API",
                            "raw_response": str(response_data),
                            "analysis": "Failed to analyze image with Google Gemini API due to unexpected response format."
                        }
                else:
                    logger.error(f"No candidates in response: {response_data}")
                    return {
                        "status": "error",
                        "error": "No content in response from Google Gemini API",
                        "raw_response": str(response_data),
                        "analysis": "Failed to analyze image with Google Gemini API."
                    }
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}")
                return {
                    "status": "error",
                    "error": f"Failed to parse JSON response: {e}",
                    "raw_response": response.text[:1000],  # Include part of the raw response for debugging
                    "analysis": "Failed to analyze image with Google Gemini API due to invalid JSON response."
                }
        elif response.status_code == 429:
            return {
                "status": "error",
                "error": "Rate limit exceeded on Google Gemini API",
                "analysis": "The Google Gemini API rate limit has been exceeded. Please try again later."
            }
        elif response.status_code == 401:
            return {
                "status": "error",
                "error": "Authentication failed with Google Gemini API",
                "analysis": "The Google Gemini API key is invalid or has expired."
            }
        elif response.status_code == 400:
            try:
                error_data = response.json()
                error_message = error_data.get('error', {}).get('message', 'Bad request')
                return {
                    "status": "error",
                    "error": f"Bad request to Google Gemini API: {error_message}",
                    "analysis": "Failed to analyze image with Google Gemini API due to bad request."
                }
            except:
                return {
                    "status": "error",
                    "error": f"Bad request to Google Gemini API: {response.text}",
                    "analysis": "Failed to analyze image with Google Gemini API due to bad request."
                }
        else:
            error_message = f"Google Gemini API request failed with status code {response.status_code}: {response.text}"
            logger.error(error_message)
            return {
                "status": "error",
                "error": error_message,
                "analysis": "Failed to analyze image with Google Gemini API."
            }
    except Exception as e:
        logger.error(f"Image analysis with Google Gemini failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "analysis": "Failed to analyze image with Google Gemini API."
        }

def test_gemini_api_connection() -> Dict[str, Any]:
    """
    Test the Google Gemini API connection with a simple text request.
    
    Returns:
        Dictionary containing test results
    """
    try:
        api_key = os.getenv('GOOGLE_GEMINI_API_KEY')
        if not api_key:
            return {
                "status": "error",
                "error": "GOOGLE_GEMINI_API_KEY environment variable not set"
            }
        
        model_id = os.getenv('GOOGLE_GEMINI_MODEL_ID', 'gemini-2.0-flash-exp')
        
        # Simple test payload
        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": "Hello, can you respond with 'API connection successful'?"
                        }
                    ]
                }
            ]
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{model_id}:generateContent?key={api_key}"
        
        response = requests.post(
            url=api_url,
            headers=headers,
            data=json.dumps(payload),
            timeout=30
        )
        
        if response.status_code == 200:
            response_data = response.json()
            if 'candidates' in response_data and len(response_data['candidates']) > 0:
                return {
                    "status": "success",
                    "message": "Google Gemini API connection successful",
                    "model_id": model_id
                }
        
        return {
            "status": "error",
            "error": f"API test failed with status {response.status_code}: {response.text}"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": f"API test failed: {str(e)}"
        }
