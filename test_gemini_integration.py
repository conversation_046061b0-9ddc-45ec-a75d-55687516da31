#!/usr/bin/env python3
"""
Test script for Google Gemini API integration.
"""

import os
import sys
import requests
import json
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
from dotenv import load_dotenv

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Backend'))

def create_test_image():
    """Create a simple test image with text for testing."""
    img = Image.new('RGB', (400, 300), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        font = ImageFont.load_default()
    
    draw.text((50, 50), "AWS Architecture Diagram", fill='black', font=font)
    draw.rectangle([50, 100, 150, 150], outline='blue', width=2)
    draw.text((60, 115), "EC2", fill='blue', font=font)
    
    draw.rectangle([200, 100, 300, 150], outline='green', width=2)
    draw.text((210, 115), "RDS", fill='green', font=font)
    
    draw.line([150, 125, 200, 125], fill='red', width=3)
    draw.polygon([(195, 120), (200, 125), (195, 130)], fill='red')
    
    draw.text((50, 200), "Test Architecture", fill='black', font=font)
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def test_gemini_api_direct():
    """Test the Gemini API directly using the gemini_utils module."""
    print("=" * 60)
    print("TESTING GOOGLE GEMINI API DIRECTLY")
    print("=" * 60)
    
    try:
        from gemini_utils import analyze_image_with_gemini, test_gemini_api_connection
        
        # First test API connection
        print("Testing API connection...")
        connection_result = test_gemini_api_connection()
        print(f"Connection test: {connection_result.get('status', 'unknown')}")
        
        if connection_result.get('status') != 'success':
            print(f"❌ API connection failed: {connection_result.get('error', 'Unknown error')}")
            return False
        
        print("✅ API connection successful")
        
        # Test image analysis
        print("\nTesting image analysis...")
        image_bytes = create_test_image()
        result = analyze_image_with_gemini(image_bytes)
        
        print(f"Status: {result.get('status', 'unknown')}")
        if result.get('status') == 'success':
            print(f"Model ID: {result.get('model_id', 'unknown')}")
            print(f"Analysis: {result.get('analysis', 'No analysis')[:200]}...")
            return True
        else:
            print(f"❌ Analysis failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Direct API test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backend_health():
    """Test backend health."""
    try:
        response = requests.get('http://localhost:8888/health', timeout=10)
        if response.status_code == 200:
            print("✅ Backend is healthy")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend health check error: {e}")
        return False

def test_gemini_endpoint():
    """Test the new /analyze/image/gemini endpoint."""
    print("\n" + "=" * 60)
    print("TESTING /analyze/image/gemini ENDPOINT")
    print("=" * 60)
    
    try:
        image_bytes = create_test_image()
        files = {'file': ('test_image.png', image_bytes, 'image/png')}
        data = {'prompt': 'Analyze this AWS architecture diagram and explain the components.'}
        
        response = requests.post(
            'http://localhost:8888/analyze/image/gemini',
            files=files,
            data=data,
            timeout=90
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                print("✅ Gemini endpoint working")
                print(f"   Model: {result.get('model_id', 'unknown')}")
                print(f"   Analysis: {result.get('analysis', 'None')[:200]}...")
                return True
            else:
                print(f"❌ Gemini endpoint failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ Gemini endpoint HTTP error: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Gemini endpoint error: {e}")
        return False

def test_backward_compatibility():
    """Test the backward compatibility /analyze/image/openrouter endpoint."""
    print("\n" + "=" * 60)
    print("TESTING BACKWARD COMPATIBILITY (/analyze/image/openrouter)")
    print("=" * 60)
    
    try:
        image_bytes = create_test_image()
        files = {'file': ('test_image.png', image_bytes, 'image/png')}
        data = {'prompt': 'Analyze this AWS architecture diagram.'}
        
        response = requests.post(
            'http://localhost:8888/analyze/image/openrouter',
            files=files,
            data=data,
            timeout=90
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                print("✅ Backward compatibility working")
                print(f"   Model: {result.get('model_id', 'unknown')}")
                print(f"   Analysis: {result.get('analysis', 'None')[:200]}...")
                return True
            else:
                print(f"❌ Backward compatibility failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ Backward compatibility HTTP error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backward compatibility error: {e}")
        return False

def test_configuration():
    """Test the configuration setup."""
    print("\n" + "=" * 60)
    print("TESTING CONFIGURATION")
    print("=" * 60)
    
    load_dotenv()
    
    api_key = os.getenv('GOOGLE_GEMINI_API_KEY')
    model_id = os.getenv('GOOGLE_GEMINI_MODEL_ID')
    
    print(f"Google Gemini API Key: {'Set' if api_key else 'Not set'}")
    print(f"Google Gemini Model ID: {model_id or 'Not set (will use default)'}")
    
    if not api_key:
        print("❌ GOOGLE_GEMINI_API_KEY is not set!")
        print("   Please set your Google Gemini API key in the .env file")
        return False
    
    if api_key == "YOUR_GOOGLE_GEMINI_API_KEY_HERE":
        print("❌ GOOGLE_GEMINI_API_KEY is set to placeholder value!")
        print("   Please replace with your actual Google Gemini API key")
        return False
    
    print("✅ Configuration looks good")
    return True

def main():
    """Main test function."""
    print("GOOGLE GEMINI INTEGRATION TEST")
    print("=" * 60)
    
    # Test configuration first
    config_ok = test_configuration()
    
    if not config_ok:
        print("\n❌ Configuration issues found. Please fix them before proceeding.")
        return
    
    # Test direct API
    direct_api_ok = test_gemini_api_direct()
    
    # Test backend health
    backend_ok = test_backend_health()
    
    if not backend_ok:
        print("\n❌ Backend is not running. Please start the backend first.")
        return
    
    # Test endpoints
    gemini_endpoint_ok = test_gemini_endpoint()
    backward_compat_ok = test_backward_compatibility()
    
    # Summary
    print("\n" + "=" * 60)
    print("GEMINI INTEGRATION TEST SUMMARY")
    print("=" * 60)
    print(f"Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"Direct API: {'✅ PASS' if direct_api_ok else '❌ FAIL'}")
    print(f"Backend Health: {'✅ PASS' if backend_ok else '❌ FAIL'}")
    print(f"Gemini Endpoint: {'✅ PASS' if gemini_endpoint_ok else '❌ FAIL'}")
    print(f"Backward Compatibility: {'✅ PASS' if backward_compat_ok else '❌ FAIL'}")
    
    if all([config_ok, direct_api_ok, backend_ok, gemini_endpoint_ok, backward_compat_ok]):
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Google Gemini integration is fully functional!")
        print("\n📋 Ready for use:")
        print("   • Frontend: Type 'gemini' when prompted for analysis method")
        print("   • API: Use /analyze/image/gemini endpoint")
        print("   • Backward compatibility: /analyze/image/openrouter still works")
    else:
        print("\n❌ SOME TESTS FAILED")
        print("   Please check the errors above and fix them before proceeding.")

if __name__ == "__main__":
    main()
