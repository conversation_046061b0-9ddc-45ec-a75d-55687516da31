# Complete RAG Application Deployment Guide for EC2

This guide provides step-by-step instructions to deploy your RAG application on EC2 and make it accessible to anyone through your public IP.

## Prerequisites

### EC2 Instance Requirements
- **Instance Type**: t3.medium (2 vCPU, 4GB RAM) - handles 20-30 concurrent users
- **OS**: Ubuntu 20.04 LTS or Amazon Linux 2
- **Storage**: 20GB+ SSD
- **Security Group**: Configure as shown below

### Security Group Configuration
```
Inbound Rules:
- Port 22 (SSH): Your IP only (for management)
- Port 80 (HTTP): 0.0.0.0/0 (for public frontend access)
- Port 8080 (Custom): 0.0.0.0/0 (for API access, optional)

Outbound Rules:
- All traffic: 0.0.0.0/0 (default)
```

## Phase 1: Initial EC2 Setup

### 1. Connect to EC2 Instance
```bash
ssh -i your-key.pem ubuntu@your-ec2-public-ip
```

### 2. Update System
```bash
sudo apt update && sudo apt upgrade -y
```

### 3. Install System Dependencies
```bash
# Ubuntu/Debian
sudo apt install -y python3 python3-pip python3-venv python3-dev \
    git curl wget unzip build-essential \
    libmagic-dev poppler-utils tesseract-ocr \
    libssl-dev libffi-dev

# Amazon Linux (alternative)
sudo yum install -y python3 python3-pip python3-devel \
    git curl wget unzip gcc gcc-c++ \
    file-devel poppler-utils tesseract \
    openssl-devel libffi-devel
```

### 4. Create Application User
```bash
sudo useradd -r -s /bin/bash -d /opt/chainlit_rag raguser
sudo mkdir -p /opt/chainlit_rag
sudo chown raguser:raguser /opt/chainlit_rag
```

## Phase 2: Deploy Application

### 5. Upload Your Application
```bash
# Option A: Using SCP from your local machine
scp -i your-key.pem -r /path/to/your/chainlit_rag ubuntu@your-ec2-ip:/tmp/
sudo mv /tmp/chainlit_rag/* /opt/chainlit_rag/
sudo chown -R raguser:raguser /opt/chainlit_rag

# Option B: Using Git (if your code is in a repository)
cd /opt
sudo git clone https://github.com/yourusername/your-repo.git chainlit_rag
sudo chown -R raguser:raguser /opt/chainlit_rag
```

### 6. Set Up Python Environment
```bash
cd /opt/chainlit_rag
sudo -u raguser python3 -m venv venv
sudo -u raguser venv/bin/pip install --upgrade pip
sudo -u raguser venv/bin/pip install -r requirements.txt
sudo -u raguser venv/bin/pip install gunicorn
```

## Phase 3: Configure Application

### 7. Create Environment Configuration
```bash
sudo -u raguser tee /opt/chainlit_rag/.env << 'EOF'
# AWS Configuration - UPDATE THESE VALUES
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-actual-access-key
AWS_SECRET_ACCESS_KEY=your-actual-secret-key
BEDROCK_EMBEDDING_MODEL_ID=amazon.titan-embed-text-v1
BEDROCK_LLM_PROFILE_ARN=your-actual-bedrock-profile-arn

# S3 Configuration - UPDATE THIS VALUE
S3_BUCKET_NAME=your-actual-s3-bucket-name

# Local Qdrant Configuration
QDRANT_PATH=./vector_store

# Application Configuration
RAG_API_HOST=0.0.0.0
RAG_API_PORT=8080
API_BASE_URL=http://localhost:8080
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=80
EOF
```

### 8. Update Environment with Your Credentials
```bash
sudo nano /opt/chainlit_rag/.env
# Replace all "your-actual-*" values with real AWS credentials
```

## Phase 4: Install and Start Services

### 9. Install Systemd Services
```bash
cd /opt/chainlit_rag
sudo systemd-services/install-services.sh install
```

### 10. Start RAG Services
```bash
sudo systemd-services/install-services.sh start
```

### 11. Verify Services are Running
```bash
# Check service status
sudo systemctl status rag-api rag-frontend

# Check ports are listening
sudo netstat -tulpn | grep -E ':(8080|80)'

# Test local access
curl -f http://localhost:8080/health
curl -f http://localhost/
```

## Phase 5: Configure Public Access

### 12. Update Environment for Public Access
```bash
# Get your EC2 public IP
PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)
echo "Your EC2 Public IP: $PUBLIC_IP"

# Update environment file
sudo nano /opt/chainlit_rag/.env

# Update the API_BASE_URL to use your public IP:
API_BASE_URL=http://$PUBLIC_IP:8080
```

### 13. Get Your Public IP
```bash
curl -s http://***************/latest/meta-data/public-ipv4
```

## Phase 6: Test Public Access

### 14. Test from Browser
Open your web browser and navigate to:
```
http://YOUR-EC2-PUBLIC-IP/
```

You should see the Chainlit interface for your RAG application.

### 15. Test API Endpoints
```bash
# Health check
curl -f http://YOUR-EC2-PUBLIC-IP:8080/health

# Should return: {"status":"healthy"}
```

### 16. Test RAG Functionality
1. Open `http://YOUR-EC2-PUBLIC-IP/` in your browser
2. Try asking a question in the chat interface
3. Verify you get responses from your RAG system

## Phase 7: Enable Auto-Start

### 17. Enable Services to Start on Boot
```bash
sudo systemctl enable rag-api rag-frontend
```

### 18. Test Reboot (Optional)
```bash
sudo reboot
# Wait for instance to restart, then test access again
```

## Verification Checklist

✅ **Services Running**
```bash
sudo systemctl status rag-api rag-frontend
# All should show "active (running)"
```

✅ **Ports Listening**
```bash
sudo netstat -tulpn | grep -E ':(80|8080)'
# Should show:
# :80 (chainlit frontend)
# :8080 (fastapi backend)
```

✅ **Public Access Working**
- Frontend: `http://YOUR-EC2-PUBLIC-IP/`
- API Health: `http://YOUR-EC2-PUBLIC-IP:8080/health`

✅ **RAG Functionality**
- Can submit queries through web interface
- Receives responses from the system
- Document ingestion works (if applicable)

## Troubleshooting

### Service Issues
```bash
# Check service logs
sudo journalctl -u rag-api -f
sudo journalctl -u rag-frontend -f
sudo journalctl -u nginx -f

# Restart services
sudo systemctl restart rag-api rag-frontend
```

### Network Issues
```bash
# Check firewall (Ubuntu)
sudo ufw status

# Check Security Group in AWS Console
# Ensure port 80 is open to 0.0.0.0/0
```

### Permission Issues
```bash
# Fix ownership
sudo chown -R raguser:raguser /opt/chainlit_rag
sudo chown -R raguser:raguser /var/log/rag-api
sudo chown -R raguser:raguser /var/log/rag-frontend
```

## Management Commands

### Daily Operations
```bash
# Check status
sudo systemd-services/install-services.sh status

# Restart services
sudo systemd-services/install-services.sh restart

# View logs
sudo journalctl -u rag-api -f
sudo journalctl -u rag-frontend -f

# Monitor Nginx
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### Updates
```bash
# Update application code
cd /opt/chainlit_rag
sudo -u raguser git pull origin main
sudo -u raguser venv/bin/pip install -r requirements.txt
sudo systemctl restart rag-api rag-frontend
```

## Security Considerations

### 1. Firewall
```bash
# Ubuntu with UFW
sudo ufw enable
sudo ufw allow 22/tcp  # SSH
sudo ufw allow 80/tcp  # HTTP
sudo ufw allow 443/tcp # HTTPS
```

### 2. SSL/HTTPS (Recommended for Production)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate (requires domain name)
sudo certbot --nginx -d your-domain.com
```

### 3. Rate Limiting
The Nginx configuration includes basic rate limiting. For production, consider:
- CloudFlare for DDoS protection
- AWS WAF for application-level protection
- Additional rate limiting rules

## Performance Monitoring

### Resource Usage
```bash
# Check memory usage
free -h

# Check CPU usage
htop

# Check disk usage
df -h

# Monitor application performance
sudo systemd-services/validate-services.sh
```

### Scaling Considerations
- **Current setup**: 20-30 concurrent users on t3.medium
- **Scale up**: t3.large (50-100 users) or t3.xlarge (100-200 users)
- **Scale out**: Multiple instances behind a load balancer

## Success Indicators

🎉 **Your RAG application is successfully deployed when:**

1. ✅ All services show "active (running)"
2. ✅ Public IP access works in browser
3. ✅ Can submit queries and get responses
4. ✅ Health check returns {"status":"healthy"}
5. ✅ No errors in service logs
6. ✅ Services restart automatically after reboot

**Your RAG application is now live and accessible to anyone with your EC2 public IP!**
