"""
Tests for the error recognition service.
"""
import os
import io
import pytest
from unittest.mock import MagicMock, patch
import numpy as np
from PIL import Image, ImageDraw
import cv2

from image_analysis.error_recognition import (
    ErrorRecognitionService,
    ErrorSeverity,
    ErrorCategory,
    ErrorVisualIndicator
)


def create_test_error_image():
    """Create a simple error-like test image."""
    img = Image.new("RGB", (400, 300), (255, 255, 255))
    draw = ImageDraw.Draw(img)
    
    # Draw a red error box
    draw.rectangle((50, 50, 350, 100), fill=(255, 0, 0))
    draw.text((100, 70), "Error: Connection failed", fill=(255, 255, 255))
    
    # Draw a yellow warning box
    draw.rectangle((50, 150, 350, 200), fill=(255, 255, 0))
    draw.text((100, 170), "Warning: Low disk space", fill=(0, 0, 0))
    
    # Draw an error icon
    draw.ellipse((50, 220, 80, 250), fill=(255, 0, 0))
    draw.text((65, 230), "!", fill=(255, 255, 255))
    
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format="PNG")
    img_byte_arr.seek(0)
    return img_byte_arr


def save_test_error_image():
    """Save test error image to disk for testing."""
    os.makedirs("test_data", exist_ok=True)
    
    # Save error image
    with open("test_data/test_error_image.png", "wb") as f:
        f.write(create_test_error_image().getvalue())


@pytest.fixture(scope="module", autouse=True)
def setup_test_error_image():
    """Set up test error image for all tests."""
    save_test_error_image()
    yield
    # Cleanup can be added here if needed


def test_init():
    """Test initialization of the service."""
    service = ErrorRecognitionService()
    assert service.compiled_patterns is not None
    assert len(service.compiled_patterns) > 0


def test_identify_errors():
    """Test error identification in text."""
    service = ErrorRecognitionService()
    
    # Test with error text
    error_text = """
    Error: Connection failed. The server returned an invalid response.
    Warning: Low disk space. Please free up some space.
    Critical: Database connection timeout.
    """
    
    errors = service.identify_errors(error_text)
    
    # Check that errors were identified
    assert len(errors) >= 3
    
    # Check error properties
    for error in errors:
        assert "text" in error
        assert "line_number" in error
        assert "category" in error
        assert "severity" in error
        assert "confidence" in error
        assert "description" in error
        
        # Check confidence range
        assert 0 <= error["confidence"] <= 1
    
    # Test with non-error text
    non_error_text = "This is a normal text without any errors or warnings."
    non_errors = service.identify_errors(non_error_text)
    assert len(non_errors) == 0


def test_determine_severity():
    """Test severity determination."""
    service = ErrorRecognitionService()
    
    # Test different severity levels
    assert service._determine_severity("Critical error: System crash") == ErrorSeverity.CRITICAL
    assert service._determine_severity("Error: Invalid input") == ErrorSeverity.HIGH
    assert service._determine_severity("Warning: Disk space low") == ErrorSeverity.MEDIUM
    assert service._determine_severity("Info: Operation completed") == ErrorSeverity.LOW
    assert service._determine_severity("Debug: Variable value is 5") == ErrorSeverity.INFO
    
    # Test default severity
    assert service._determine_severity("Something happened") == ErrorSeverity.MEDIUM


def test_calculate_confidence():
    """Test confidence calculation."""
    service = ErrorRecognitionService()
    
    # Test with different inputs
    high_confidence = service._calculate_confidence(
        "Critical error: Database connection failed with exception",
        ErrorCategory.DATABASE,
        ErrorSeverity.CRITICAL
    )
    
    medium_confidence = service._calculate_confidence(
        "Warning: Low disk space",
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM
    )
    
    low_confidence = service._calculate_confidence(
        "x",  # Very short text
        ErrorCategory.UNKNOWN,
        ErrorSeverity.LOW
    )
    
    # Check confidence ranges
    assert high_confidence > medium_confidence > low_confidence
    assert 0 <= high_confidence <= 1
    assert 0 <= medium_confidence <= 1
    assert 0 <= low_confidence <= 1


def test_generate_error_description():
    """Test error description generation."""
    service = ErrorRecognitionService()
    
    # Test descriptions for different categories
    for category in ErrorCategory:
        description = service._generate_error_description(
            "Test error",
            category,
            ErrorSeverity.MEDIUM
        )
        
        # Check that description is non-empty
        assert description
        assert len(description) > 10


def test_suggest_solutions():
    """Test solution suggestion."""
    service = ErrorRecognitionService()
    
    # Test with error text containing multiple categories
    error_text = """
    Error: Connection failed. The server returned an invalid response.
    Warning: Low disk space. Please free up some space.
    Error: Invalid username or password.
    """
    
    solutions = service.suggest_solutions(error_text)
    
    # Check that solutions were generated
    assert len(solutions) > 0
    
    # Check solution properties
    for solution in solutions:
        assert "category" in solution
        assert "solution" in solution
        assert "confidence" in solution
        assert "related_errors" in solution
        
        # Check confidence range
        assert 0 <= solution["confidence"] <= 1
        
        # Check that related errors are included
        assert len(solution["related_errors"]) > 0


def test_severity_to_numeric():
    """Test conversion of severity to numeric value."""
    service = ErrorRecognitionService()
    
    # Test all severity levels
    assert service._severity_to_numeric(ErrorSeverity.CRITICAL) == 5
    assert service._severity_to_numeric(ErrorSeverity.HIGH) == 4
    assert service._severity_to_numeric(ErrorSeverity.MEDIUM) == 3
    assert service._severity_to_numeric(ErrorSeverity.LOW) == 2
    assert service._severity_to_numeric(ErrorSeverity.INFO) == 1
    assert service._severity_to_numeric(ErrorSeverity.UNKNOWN) == 0


def test_generate_solution():
    """Test solution generation for a category of errors."""
    service = ErrorRecognitionService()
    
    # Create test errors
    errors = [
        {
            "text": "Error: Connection failed",
            "line_number": 1,
            "category": ErrorCategory.NETWORK,
            "severity": ErrorSeverity.HIGH,
            "confidence": 0.8,
            "description": "Network error"
        },
        {
            "text": "Error: Unable to connect to server",
            "line_number": 2,
            "category": ErrorCategory.NETWORK,
            "severity": ErrorSeverity.MEDIUM,
            "confidence": 0.7,
            "description": "Network error"
        }
    ]
    
    # Generate solution
    solution = service._generate_solution(ErrorCategory.NETWORK, errors)
    
    # Check solution properties
    assert solution["category"] == ErrorCategory.NETWORK
    assert "solution" in solution
    assert solution["confidence"] == 0.8  # Should match primary error confidence
    assert len(solution["related_errors"]) == 2


def test_classify_error_type():
    """Test error type classification."""
    service = ErrorRecognitionService()
    
    # Test with error text containing multiple categories and severities
    error_text = """
    Error: Connection failed. The server returned an invalid response.
    Warning: Low disk space. Please free up some space.
    Critical: Database connection timeout.
    """
    
    severity, category, confidence = service.classify_error_type(error_text)
    
    # Check that classification was performed
    assert severity != ErrorSeverity.UNKNOWN
    assert category != ErrorCategory.UNKNOWN
    assert 0 <= confidence <= 1
    
    # Test with empty text
    empty_severity, empty_category, empty_confidence = service.classify_error_type("")
    assert empty_severity == ErrorSeverity.UNKNOWN
    assert empty_category == ErrorCategory.UNKNOWN
    assert empty_confidence == 0.5@patch
("image_analysis.error_recognition.cv2.imread")
def test_detect_visual_indicators_image_error(mock_imread):
    """Test handling of image loading errors."""
    # Mock image loading failure
    mock_imread.return_value = None
    
    service = ErrorRecognitionService()
    indicators = service.detect_visual_indicators("nonexistent.png")
    
    assert indicators == []


@patch("image_analysis.error_recognition.cv2.findContours")
@patch("image_analysis.error_recognition.cv2.inRange")
@patch("image_analysis.error_recognition.cv2.cvtColor")
@patch("image_analysis.error_recognition.cv2.imread")
def test_detect_red_regions(mock_imread, mock_cvtcolor, mock_inrange, mock_findcontours):
    """Test detection of red regions."""
    # Mock image
    mock_img = np.zeros((300, 400, 3), dtype=np.uint8)
    mock_imread.return_value = mock_img
    
    # Mock HSV conversion
    mock_hsv = np.zeros((300, 400, 3), dtype=np.uint8)
    mock_cvtcolor.return_value = mock_hsv
    
    # Mock mask
    mock_mask = np.zeros((300, 400), dtype=np.uint8)
    mock_inrange.return_value = mock_mask
    
    # Mock contours
    mock_contour = np.array([[[50, 50]], [[150, 50]], [[150, 100]], [[50, 100]]])
    mock_findcontours.return_value = ([mock_contour], None)
    
    service = ErrorRecognitionService()
    
    # Mock contour area to be large enough
    with patch("image_analysis.error_recognition.cv2.contourArea", return_value=1000):
        # Mock bounding rect
        with patch("image_analysis.error_recognition.cv2.boundingRect", return_value=(50, 50, 100, 50)):
            indicators = service._detect_red_regions(mock_img, mock_hsv)
            
            # Check that indicators were detected
            assert len(indicators) == 1
            
            # Check indicator properties
            indicator = indicators[0]
            assert indicator["type"] in [ErrorVisualIndicator.RED_BACKGROUND, ErrorVisualIndicator.RED_TEXT]
            assert "bounding_box" in indicator
            assert "confidence" in indicator
            assert "area" in indicator


@patch("image_analysis.error_recognition.cv2.findContours")
@patch("image_analysis.error_recognition.cv2.inRange")
@patch("image_analysis.error_recognition.cv2.cvtColor")
@patch("image_analysis.error_recognition.cv2.imread")
def test_detect_yellow_regions(mock_imread, mock_cvtcolor, mock_inrange, mock_findcontours):
    """Test detection of yellow regions."""
    # Mock image
    mock_img = np.zeros((300, 400, 3), dtype=np.uint8)
    mock_imread.return_value = mock_img
    
    # Mock HSV conversion
    mock_hsv = np.zeros((300, 400, 3), dtype=np.uint8)
    mock_cvtcolor.return_value = mock_hsv
    
    # Mock mask
    mock_mask = np.zeros((300, 400), dtype=np.uint8)
    mock_inrange.return_value = mock_mask
    
    # Mock contours
    mock_contour = np.array([[[50, 50]], [[150, 50]], [[150, 100]], [[50, 100]]])
    mock_findcontours.return_value = ([mock_contour], None)
    
    service = ErrorRecognitionService()
    
    # Mock contour area to be large enough
    with patch("image_analysis.error_recognition.cv2.contourArea", return_value=1000):
        # Mock bounding rect
        with patch("image_analysis.error_recognition.cv2.boundingRect", return_value=(50, 50, 100, 50)):
            indicators = service._detect_yellow_regions(mock_img, mock_hsv)
            
            # Check that indicators were detected
            assert len(indicators) == 1
            
            # Check indicator properties
            indicator = indicators[0]
            assert indicator["type"] in [ErrorVisualIndicator.YELLOW_BACKGROUND, ErrorVisualIndicator.WARNING_ICON]
            assert "bounding_box" in indicator
            assert "confidence" in indicator
            assert "area" in indicator@patch(
"image_analysis.error_recognition.cv2.findContours")
@patch("image_analysis.error_recognition.cv2.adaptiveThreshold")
@patch("image_analysis.error_recognition.cv2.cvtColor")
@patch("image_analysis.error_recognition.cv2.imread")
def test_detect_error_icons(mock_imread, mock_cvtcolor, mock_threshold, mock_findcontours):
    """Test detection of error icons."""
    # Mock image
    mock_img = np.zeros((300, 400, 3), dtype=np.uint8)
    mock_imread.return_value = mock_img
    
    # Mock grayscale conversion
    mock_gray = np.zeros((300, 400), dtype=np.uint8)
    mock_cvtcolor.return_value = mock_gray
    
    # Mock threshold
    mock_thresh = np.zeros((300, 400), dtype=np.uint8)
    mock_threshold.return_value = mock_thresh
    
    # Mock contours
    mock_contour = np.array([[[50, 50]], [[80, 50]], [[80, 80]], [[50, 80]]])
    mock_findcontours.return_value = ([mock_contour], None)
    
    service = ErrorRecognitionService()
    
    # Mock contour area to be in the right range
    with patch("image_analysis.error_recognition.cv2.contourArea", return_value=900):
        # Mock bounding rect
        with patch("image_analysis.error_recognition.cv2.boundingRect", return_value=(50, 50, 30, 30)):
            # Mock arc length
            with patch("image_analysis.error_recognition.cv2.arcLength", return_value=120):
                # Mock approx poly DP
                with patch("image_analysis.error_recognition.cv2.approxPolyDP", return_value=np.array([[[50, 50]], [[80, 50]], [[80, 80]], [[50, 80]]])):
                    indicators = service._detect_error_icons(mock_img, mock_gray)
                    
                    # Check that indicators were detected
                    assert len(indicators) == 1
                    
                    # Check indicator properties
                    indicator = indicators[0]
                    assert indicator["type"] in [
                        ErrorVisualIndicator.ERROR_ICON,
                        ErrorVisualIndicator.WARNING_ICON,
                        ErrorVisualIndicator.EXCLAMATION_MARK,
                        ErrorVisualIndicator.ERROR_SYMBOL
                    ]
                    assert "bounding_box" in indicator
                    assert "confidence" in indicator
                    assert "area" in indicator


@patch("image_analysis.error_recognition.cv2.findContours")
@patch("image_analysis.error_recognition.cv2.dilate")
@patch("image_analysis.error_recognition.cv2.Canny")
@patch("image_analysis.error_recognition.cv2.cvtColor")
@patch("image_analysis.error_recognition.cv2.imread")
def test_detect_alert_dialogs(mock_imread, mock_cvtcolor, mock_canny, mock_dilate, mock_findcontours):
    """Test detection of alert dialogs."""
    # Mock image
    mock_img = np.zeros((300, 400, 3), dtype=np.uint8)
    mock_imread.return_value = mock_img
    
    # Mock grayscale conversion
    mock_gray = np.zeros((300, 400), dtype=np.uint8)
    mock_cvtcolor.return_value = mock_gray
    
    # Mock edges
    mock_edges = np.zeros((300, 400), dtype=np.uint8)
    mock_canny.return_value = mock_edges
    
    # Mock dilated edges
    mock_dilated = np.zeros((300, 400), dtype=np.uint8)
    mock_dilate.return_value = mock_dilated
    
    # Mock contours
    mock_contour = np.array([[[50, 50]], [[350, 50]], [[350, 150]], [[50, 150]]])
    mock_findcontours.return_value = ([mock_contour], None)
    
    service = ErrorRecognitionService()
    
    # Mock contour area to be in the right range
    with patch("image_analysis.error_recognition.cv2.contourArea", return_value=30000):
        # Mock arc length
        with patch("image_analysis.error_recognition.cv2.arcLength", return_value=800):
            # Mock approx poly DP for rectangular dialog
            with patch("image_analysis.error_recognition.cv2.approxPolyDP", return_value=np.array([[[50, 50]], [[350, 50]], [[350, 150]], [[50, 150]]])):
                # Mock bounding rect
                with patch("image_analysis.error_recognition.cv2.boundingRect", return_value=(50, 50, 300, 100)):
                    indicators = service._detect_alert_dialogs(mock_img, mock_gray)
                    
                    # Check that indicators were detected
                    assert len(indicators) == 1
                    
                    # Check indicator properties
                    indicator = indicators[0]
                    assert indicator["type"] in [ErrorVisualIndicator.MODAL_DIALOG, ErrorVisualIndicator.ALERT_BOX]
                    assert "bounding_box" in indicator
                    assert "confidence" in indicator
                    assert "area" in indicator


@patch("image_analysis.error_recognition.ErrorRecognitionService.detect_visual_indicators")
@patch("image_analysis.error_recognition.ErrorRecognitionService.classify_error_type")
@patch("image_analysis.error_recognition.ErrorRecognitionService.suggest_solutions")
@patch("image_analysis.error_recognition.ErrorRecognitionService.identify_errors")
def test_analyze_error_screenshot(mock_identify, mock_suggest, mock_classify, mock_detect):
    """Test error screenshot analysis."""
    # Mock identify_errors
    mock_identify.return_value = [
        {
            "text": "Error: Connection failed",
            "line_number": 1,
            "category": ErrorCategory.NETWORK,
            "severity": ErrorSeverity.HIGH,
            "confidence": 0.8,
            "description": "Network error"
        }
    ]
    
    # Mock classify_error_type
    mock_classify.return_value = (ErrorSeverity.HIGH, ErrorCategory.NETWORK, 0.8)
    
    # Mock suggest_solutions
    mock_suggest.return_value = [
        {
            "category": ErrorCategory.NETWORK,
            "solution": "Check network connectivity",
            "confidence": 0.8,
            "related_errors": ["Error: Connection failed"]
        }
    ]
    
    # Mock detect_visual_indicators
    mock_detect.return_value = [
        {
            "type": ErrorVisualIndicator.RED_BACKGROUND,
            "bounding_box": {
                "x": 50,
                "y": 50,
                "width": 300,
                "height": 50
            },
            "confidence": 0.9,
            "area": 15000
        }
    ]
    
    service = ErrorRecognitionService()
    result = service.analyze_error_screenshot("test_data/test_error_image.png", "Error: Connection failed")
    
    # Check result properties
    assert result["severity"] == ErrorSeverity.HIGH
    assert result["category"] == ErrorCategory.NETWORK
    assert result["confidence"] == 0.8
    assert len(result["text_errors"]) == 1
    assert len(result["visual_indicators"]) == 1
    assert len(result["solutions"]) == 1


@pytest.mark.skipif(not os.path.exists("test_data/test_error_image.png"),
                   reason="Test error image not available")
def test_integration():
    """Integration test with actual test image."""
    service = ErrorRecognitionService()
    
    # Test with error text
    error_text = """
    Error: Connection failed. The server returned an invalid response.
    Warning: Low disk space. Please free up some space.
    """
    
    # Test error identification
    errors = service.identify_errors(error_text)
    assert len(errors) >= 2
    
    # Test solution suggestion
    solutions = service.suggest_solutions(error_text)
    assert len(solutions) >= 1
    
    # Test error type classification
    severity, category, confidence = service.classify_error_type(error_text)
    assert severity != ErrorSeverity.UNKNOWN
    assert category != ErrorCategory.UNKNOWN
    
    # Test visual indicator detection
    indicators = service.detect_visual_indicators("test_data/test_error_image.png")
    assert len(indicators) > 0
    
    # Test complete analysis
    analysis = service.analyze_error_screenshot("test_data/test_error_image.png", error_text)
    assert analysis["severity"] != ErrorSeverity.UNKNOWN
    assert analysis["category"] != ErrorCategory.UNKNOWN
    assert len(analysis["text_errors"]) >= 2
    assert len(analysis["visual_indicators"]) > 0
    assert len(analysis["solutions"]) >= 1