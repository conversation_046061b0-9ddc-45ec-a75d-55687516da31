#!/usr/bin/env python3
"""
Simple test to check the Gemini endpoint response.
"""

import requests
import json
from PIL import Image, ImageDraw
from io import Bytes<PERSON>

def create_simple_image():
    """Create a very simple test image."""
    img = Image.new('RGB', (100, 100), color='white')
    draw = ImageDraw.Draw(img)
    draw.text((10, 10), "Test", fill='black')
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def main():
    print("Testing Gemini endpoint...")
    
    image_bytes = create_simple_image()
    files = {'file': ('test.png', image_bytes, 'image/png')}
    data = {'prompt': 'What do you see?'}
    
    try:
        response = requests.post(
            'http://localhost:8888/analyze/image/gemini',
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("SUCCESS!")
            print(f"Keys: {list(result.keys())}")
            print(f"Status: {result.get('status')}")
            print(f"Model: {result.get('model_id')}")
            
            analysis = result.get('analysis', '')
            if analysis:
                print(f"Analysis length: {len(analysis)}")
                print(f"Analysis: {analysis[:200]}...")
            else:
                print("No analysis field!")
                
        else:
            print(f"FAILED: {response.text}")
            
    except Exception as e:
        print(f"ERROR: {e}")

if __name__ == "__main__":
    main()
