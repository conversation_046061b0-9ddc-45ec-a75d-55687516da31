#!/usr/bin/env python3
"""
Test the Gemini endpoint without a custom prompt to see if that's the issue.
"""

import requests
from PIL import Image, ImageDraw
from io import BytesIO

def create_test_image():
    """Create a test image."""
    img = Image.new('RGB', (200, 150), color='white')
    draw = ImageDraw.Draw(img)
    draw.text((20, 20), "AWS Test", fill='black')
    draw.rectangle([20, 50, 80, 90], outline='blue', width=2)
    draw.text((25, 65), "EC2", fill='blue')
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def test_with_prompt():
    """Test with a custom prompt."""
    print("Testing WITH custom prompt...")
    
    image_bytes = create_test_image()
    files = {'file': ('test.png', image_bytes, 'image/png')}
    data = {'prompt': 'Analyze this AWS diagram.'}
    
    try:
        response = requests.post(
            'http://localhost:8888/analyze/image/gemini',
            files=files,
            data=data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            analysis = result.get('analysis', '')
            print(f"✅ WITH prompt - Analysis length: {len(analysis)}")
            print(f"   Preview: {analysis[:100]}...")
        else:
            print(f"❌ WITH prompt - Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ WITH prompt - Exception: {e}")

def test_without_prompt():
    """Test without a custom prompt."""
    print("\nTesting WITHOUT custom prompt...")
    
    image_bytes = create_test_image()
    files = {'file': ('test.png', image_bytes, 'image/png')}
    # No data field with prompt
    
    try:
        response = requests.post(
            'http://localhost:8888/analyze/image/gemini',
            files=files,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            analysis = result.get('analysis', '')
            print(f"✅ WITHOUT prompt - Analysis length: {len(analysis)}")
            print(f"   Preview: {analysis[:100]}...")
        else:
            print(f"❌ WITHOUT prompt - Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ WITHOUT prompt - Exception: {e}")

def main():
    print("Testing Gemini endpoint with and without custom prompt...")
    test_with_prompt()
    test_without_prompt()

if __name__ == "__main__":
    main()
