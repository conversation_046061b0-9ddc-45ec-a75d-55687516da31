"""
API endpoints for the Image Analysis feature.
"""
import os
import io
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, BackgroundTasks, Query, Form
from fastapi.responses import JSONResponse, FileResponse, StreamingResponse
from pydantic import BaseModel

from .models import ImageAnalysisRequest, ImageAnalysisResult, ImageType, AnalysisStatus
from .validators import ImageValidator
from .storage import TemporaryImageStore, AnalysisResultStore
from .processor import ImageProcessor


# Initialize router
router = APIRouter(prefix="/api/image-analysis", tags=["image-analysis"])

# Initialize services
image_store = TemporaryImageStore()
result_store = AnalysisResultStore()
image_processor = ImageProcessor(image_store, result_store)

# Define request models
class BatchAnalysisRequest(BaseModel):
    """Request model for batch analysis."""
    image_urls: List[str]


@router.post("/")
async def upload_image(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
) -> Dict[str, Any]:
    """
    Upload an image for analysis.
    
    Args:
        background_tasks: FastAPI background tasks
        file: The image file to analyze
        
    Returns:
        Dict[str, Any]: Analysis job information
    """
    # Validate the image
    ImageValidator.validate_image(file)
    
    # Store the image
    file_path = await image_store.store_image(file)
    
    # Create and store the analysis request
    request = ImageAnalysisRequest(
        original_filename=file.filename or "unknown",
        content_type=file.content_type or "application/octet-stream",
        file_size=os.path.getsize(file_path),
        storage_path=file_path,
    )
    request_id = result_store.store_request(request)
    
    # Add background task to process the image
    background_tasks.add_task(image_processor.process_image, request_id)
    
    return {
        "id": request_id,
        "status": request.status,
        "message": "Image uploaded successfully and queued for analysis"
    }


@router.get("/{request_id}")
async def get_analysis_result(request_id: str) -> Dict[str, Any]:
    """
    Get the analysis result for a request.
    
    Args:
        request_id: ID of the analysis request
        
    Returns:
        Dict[str, Any]: Analysis result or status
    """
    # Get the request
    request = result_store.get_request(request_id)
    if not request:
        raise HTTPException(status_code=404, detail="Analysis request not found")
    
    # Check if analysis is complete
    if request.status == AnalysisStatus.COMPLETED:
        # Get the result
        result = result_store.get_result_by_request(request_id)
        if result:
            return {
                "status": request.status,
                "result": result.dict()
            }
    
    # Return status if not complete
    return {
        "status": request.status,
        "message": f"Analysis is {request.status}"
    }


@router.delete("/{request_id}")
async def delete_analysis(request_id: str) -> Dict[str, Any]:
    """
    Delete an analysis request and its associated data.
    
    Args:
        request_id: ID of the analysis request
        
    Returns:
        Dict[str, Any]: Deletion status
    """
    # Get the request
    request = result_store.get_request(request_id)
    if not request:
        raise HTTPException(status_code=404, detail="Analysis request not found")
    
    # Delete the image
    image_store.delete_image(request.storage_path)
    
    # Delete the request and result
    result_store.delete_request_and_result(request_id)
    
    return {
        "message": "Analysis data deleted successfully"
    }


@router.get("/image/{request_id}")
async def get_image(request_id: str) -> FileResponse:
    """
    Get the image associated with an analysis request.
    
    Args:
        request_id: ID of the analysis request
        
    Returns:
        FileResponse: The image file
    """
    # Get the request
    request = result_store.get_request(request_id)
    if not request:
        raise HTTPException(status_code=404, detail="Analysis request not found")
    
    # Check if the image exists
    if not os.path.exists(request.storage_path):
        raise HTTPException(status_code=404, detail="Image not found")
    
    # Return the image
    return FileResponse(
        request.storage_path,
        media_type=request.content_type,
        filename=request.original_filename
    )


@router.get("/secure-image")
async def get_secure_image(token: str) -> FileResponse:
    """
    Get an image using a secure token.
    
    Args:
        token: Secure token for the image
        
    Returns:
        FileResponse: The image file
    """
    # Check if secure storage is enabled
    if not hasattr(image_store, "secure_handler") or not image_store.secure_handler:
        raise HTTPException(status_code=400, detail="Secure image access is not enabled")
    
    # Get the image path from the token
    image_path = image_store.get_image_by_token(token)
    if not image_path or not os.path.exists(image_path):
        raise HTTPException(status_code=404, detail="Image not found or token is invalid")
    
    # Return the image
    return FileResponse(
        image_path,
        media_type="image/png",  # Default to PNG for decrypted images
        filename="secure_image.png"
    )


@router.post("/batch")
async def batch_analyze(
    background_tasks: BackgroundTasks,
    request: BatchAnalysisRequest
) -> Dict[str, Any]:
    """
    Submit multiple images for batch analysis.
    
    Args:
        background_tasks: FastAPI background tasks
        request: Batch analysis request with image URLs
        
    Returns:
        Dict[str, Any]: Batch job information
    """
    if not request.image_urls:
        raise HTTPException(status_code=400, detail="No image URLs provided")
    
    # Process each image URL
    request_ids = []
    for url in request.image_urls:
        try:
            # Create a request for the image
            analysis_request = ImageAnalysisRequest(
                original_filename=os.path.basename(url),
                content_type="image/unknown",  # Will be determined when fetched
                file_size=0,  # Will be determined when fetched
                storage_path=url,
            )
            request_id = result_store.store_request(analysis_request)
            request_ids.append(request_id)
            
            # Add background task to process the image
            background_tasks.add_task(image_processor.process_image, request_id)
        except Exception as e:
            logger.error(f"Error processing URL {url}: {str(e)}")
    
    return {
        "batch_size": len(request.image_urls),
        "processed": len(request_ids),
        "request_ids": request_ids,
        "message": f"Batch job submitted with {len(request_ids)} images"
    }


@router.get("/status")
async def get_service_status() -> Dict[str, Any]:
    """
    Get the status of the image analysis service.
    
    Returns:
        Dict[str, Any]: Service status information
    """
    # Count requests by status
    status_counts = {status.value: 0 for status in AnalysisStatus}
    for request in result_store.requests.values():
        status_counts[request.status.value] += 1
    
    # Count results by type
    type_counts = {type.value: 0 for type in ImageType}
    for result in result_store.results.values():
        type_counts[result.analysis_type.value] += 1
    
    return {
        "status": "healthy",
        "requests": {
            "total": len(result_store.requests),
            "by_status": status_counts
        },
        "results": {
            "total": len(result_store.results),
            "by_type": type_counts
        }
    }