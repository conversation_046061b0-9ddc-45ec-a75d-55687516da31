#!/usr/bin/env python3
"""
Integration test script for Frontend-Backend connectivity.

This script tests the integration between the enhanced Chainlit frontend
and the FastAPI backend to ensure all endpoints are properly connected.
"""

import asyncio
import aiohttp
import json
import os
import sys
import tempfile
from PIL import Image, ImageDraw, ImageFont
from typing import Dict, Any, Optional

# Configuration
BACKEND_URL = os.getenv("API_BASE_URL", "http://localhost:8888")
TIMEOUT = aiohttp.ClientTimeout(total=60)

class IntegrationTester:
    """Test class for frontend-backend integration."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
    
    async def test_backend_health(self):
        """Test backend health endpoint."""
        try:
            async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
                async with session.get(f"{self.base_url}/health") as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("status") == "healthy":
                            self.log_test("Backend Health Check", True, "Backend is healthy")
                            return True
                        else:
                            self.log_test("Backend Health Check", False, f"Unexpected response: {data}")
                            return False
                    else:
                        self.log_test("Backend Health Check", False, f"HTTP {response.status}")
                        return False
        except Exception as e:
            self.log_test("Backend Health Check", False, f"Connection error: {e}")
            return False
    
    async def test_advanced_query(self):
        """Test advanced query endpoint."""
        try:
            payload = {
                "question": "What is AWS Lambda?",
                "retrieval_config": {"k": 3}
            }
            
            async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
                async with session.post(
                    f"{self.base_url}/query/advanced",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if "answer" in data:
                            self.log_test("Advanced Query", True, "Query executed successfully")
                            return True
                        else:
                            self.log_test("Advanced Query", False, f"Missing answer in response: {data}")
                            return False
                    else:
                        error_text = await response.text()
                        self.log_test("Advanced Query", False, f"HTTP {response.status}: {error_text}")
                        return False
        except Exception as e:
            self.log_test("Advanced Query", False, f"Error: {e}")
            return False
    
    def create_test_image(self) -> bytes:
        """Create a test image with text for OCR testing."""
        img = Image.new('RGB', (400, 300), color='white')
        draw = ImageDraw.Draw(img)
        
        try:
            font = ImageFont.load_default()
        except:
            font = None
        
        draw.text((50, 50), "Test Architecture Diagram", fill='black', font=font)
        draw.text((50, 100), "Database -> API -> Frontend", fill='black', font=font)
        draw.text((50, 150), "Error: Connection timeout", fill='red', font=font)
        
        # Save to bytes
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
            img.save(temp_file.name, 'PNG')
            with open(temp_file.name, 'rb') as f:
                image_bytes = f.read()
            os.unlink(temp_file.name)
            return image_bytes
    
    async def test_image_ocr_query(self):
        """Test image OCR + retrieval endpoint."""
        try:
            image_bytes = self.create_test_image()
            
            data = aiohttp.FormData()
            data.add_field('file', image_bytes, filename='test.png', content_type='image/png')
            data.add_field('retrieval_config', json.dumps({"k": 3}))
            
            async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
                async with session.post(f"{self.base_url}/query/image", data=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "extracted_text" in result and "answer" in result:
                            self.log_test("Image OCR Query", True, "OCR and retrieval successful")
                            return True
                        else:
                            self.log_test("Image OCR Query", False, f"Missing fields in response: {result}")
                            return False
                    else:
                        error_text = await response.text()
                        self.log_test("Image OCR Query", False, f"HTTP {response.status}: {error_text}")
                        return False
        except Exception as e:
            self.log_test("Image OCR Query", False, f"Error: {e}")
            return False
    
    async def test_image_analysis_aws(self):
        """Test AWS Bedrock image analysis endpoint."""
        try:
            image_bytes = self.create_test_image()
            
            data = aiohttp.FormData()
            data.add_field('file', image_bytes, filename='test.png', content_type='image/png')
            
            async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
                async with session.post(f"{self.base_url}/analyze/image", data=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "status" in result and "analysis" in result:
                            status = result.get("status")
                            if status == "success":
                                self.log_test("AWS Image Analysis", True, "Analysis completed successfully")
                            elif status == "error" and "AWS credentials" in result.get("error", ""):
                                self.log_test("AWS Image Analysis", True, "Proper error handling for missing credentials")
                            else:
                                self.log_test("AWS Image Analysis", False, f"Unexpected status: {status}")
                            return True
                        else:
                            self.log_test("AWS Image Analysis", False, f"Missing fields in response: {result}")
                            return False
                    else:
                        error_text = await response.text()
                        self.log_test("AWS Image Analysis", False, f"HTTP {response.status}: {error_text}")
                        return False
        except Exception as e:
            self.log_test("AWS Image Analysis", False, f"Error: {e}")
            return False
    
    async def test_image_analysis_openrouter(self):
        """Test OpenRouter image analysis endpoint with custom prompt."""
        try:
            image_bytes = self.create_test_image()
            
            data = aiohttp.FormData()
            data.add_field('file', image_bytes, filename='test.png', content_type='image/png')
            data.add_field('prompt', 'Analyze this architecture diagram for any errors or issues')
            
            async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
                async with session.post(f"{self.base_url}/analyze/image/openrouter", data=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "status" in result and "analysis" in result:
                            status = result.get("status")
                            if status == "success":
                                self.log_test("OpenRouter Image Analysis", True, "Analysis with custom prompt successful")
                            elif status == "error" and "API key" in result.get("error", ""):
                                self.log_test("OpenRouter Image Analysis", True, "Proper error handling for missing API key")
                            else:
                                self.log_test("OpenRouter Image Analysis", False, f"Unexpected status: {status}")
                            return True
                        else:
                            self.log_test("OpenRouter Image Analysis", False, f"Missing fields in response: {result}")
                            return False
                    else:
                        error_text = await response.text()
                        self.log_test("OpenRouter Image Analysis", False, f"HTTP {response.status}: {error_text}")
                        return False
        except Exception as e:
            self.log_test("OpenRouter Image Analysis", False, f"Error: {e}")
            return False
    
    async def test_document_ingestion(self):
        """Test document ingestion endpoint."""
        try:
            async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
                async with session.post(
                    f"{self.base_url}/ingest",
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "status" in result and "message" in result:
                            self.log_test("Document Ingestion", True, "Ingestion endpoint accessible")
                            return True
                        else:
                            self.log_test("Document Ingestion", False, f"Unexpected response: {result}")
                            return False
                    else:
                        error_text = await response.text()
                        # Ingestion might fail due to S3 config, but endpoint should be accessible
                        if response.status == 500:
                            self.log_test("Document Ingestion", True, "Endpoint accessible (S3 config may be missing)")
                            return True
                        else:
                            self.log_test("Document Ingestion", False, f"HTTP {response.status}: {error_text}")
                            return False
        except Exception as e:
            self.log_test("Document Ingestion", False, f"Error: {e}")
            return False
    
    async def test_error_handling(self):
        """Test error handling for invalid requests."""
        try:
            # Test invalid file type
            text_content = b"This is not an image"
            data = aiohttp.FormData()
            data.add_field('file', text_content, filename='test.txt', content_type='text/plain')
            
            async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
                async with session.post(f"{self.base_url}/query/image", data=data) as response:
                    if response.status == 400:
                        self.log_test("Error Handling", True, "Proper 400 error for invalid file type")
                        return True
                    else:
                        error_text = await response.text()
                        self.log_test("Error Handling", False, f"Expected 400, got {response.status}: {error_text}")
                        return False
        except Exception as e:
            self.log_test("Error Handling", False, f"Error: {e}")
            return False
    
    async def run_all_tests(self):
        """Run all integration tests."""
        print("=" * 60)
        print("FRONTEND-BACKEND INTEGRATION TESTS")
        print("=" * 60)
        print(f"Testing backend at: {self.base_url}")
        print()
        
        # Test backend connectivity first
        backend_healthy = await self.test_backend_health()
        if not backend_healthy:
            print("\n❌ Backend is not accessible. Please start the backend first.")
            return False
        
        print()
        
        # Run all endpoint tests
        await self.test_advanced_query()
        await self.test_image_ocr_query()
        await self.test_image_analysis_aws()
        await self.test_image_analysis_openrouter()
        await self.test_document_ingestion()
        await self.test_error_handling()
        
        # Generate summary
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        print("\n🎯 Integration Status:")
        if passed_tests == total_tests:
            print("✅ All integrations working perfectly!")
        elif passed_tests >= total_tests * 0.8:
            print("🟡 Most integrations working (some API keys may be missing)")
        else:
            print("❌ Multiple integration issues detected")
        
        return passed_tests >= total_tests * 0.8

async def main():
    """Main function to run integration tests."""
    tester = IntegrationTester(BACKEND_URL)
    success = await tester.run_all_tests()
    
    if success:
        print("\n✅ Frontend-Backend integration is ready!")
        print("\n🚀 You can now start both services:")
        print("  Backend: cd Backend && python main.py")
        print("  Frontend: cd Frontend && chainlit run chainlit_app.py")
        return 0
    else:
        print("\n❌ Integration issues detected. Please check the backend configuration.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
