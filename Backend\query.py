from typing import Dict, Any
from langchain_aws import BedrockEmbeddings, ChatBedrock
from vectorstore_utils import load_vectorstore, load_vectorstore_client
from prompts import AWSPromptSelector, AWSResponseParser, validate_prompt_inputs, extract_aws_services
from advanced_retrieval import HybridAWSRetriever, ConfigurableRetriever, FusionRetriever, MultiVectorRetriever
from token_utils import get_token_tracker, extract_token_usage, TokenUsage
from langchain.schema import Document
from embedding_utils import MultiModalEmbeddings
import os
import logging
import networkx as nx

# Get logger
logger = logging.getLogger(__name__)

def compress_document(doc: Document, query: str, threshold: float = 0.3) -> Document:
    """
    Compress a document by keeping only the most relevant parts to the query.
    
    Args:
        doc: The document to compress
        query: The query to compare relevance against
        threshold: Minimum relevance score to keep content
        
    Returns:
        A new document with compressed content
    """
    try:
        # For short documents, don't bother compressing
        if len(doc.page_content) < 500:
            return doc
            
        # Split the document into paragraphs
        paragraphs = doc.page_content.split('\n\n')
        if len(paragraphs) <= 1:
            # Try splitting by newline if no paragraphs
            paragraphs = doc.page_content.split('\n')
            
        # If still no good splits, return the original
        if len(paragraphs) <= 1:
            return doc
            
        # Calculate relevance for each paragraph
        relevant_paragraphs = []
        
        # Simple keyword matching for relevance
        query_keywords = set(query.lower().split())
        # Remove common stop words
        stop_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"}
        query_keywords = query_keywords - stop_words
        
        for paragraph in paragraphs:
            if not paragraph.strip():
                continue
                
            # Count keyword matches
            paragraph_lower = paragraph.lower()
            matches = sum(1 for keyword in query_keywords if keyword in paragraph_lower)
            
            # Calculate a simple relevance score
            relevance = matches / max(1, len(query_keywords))
            
            # Keep paragraphs above threshold
            if relevance >= threshold:
                relevant_paragraphs.append(paragraph)
                
        # If no paragraphs were relevant enough, keep the original
        if not relevant_paragraphs:
            return doc
            
        # Create a new document with compressed content
        compressed_content = '\n\n'.join(relevant_paragraphs)
        
        # Create a new document with the compressed content
        compressed_doc = Document(
            page_content=compressed_content,
            metadata={**doc.metadata, "compressed": True, "original_length": len(doc.page_content)}
        )
        
        return compressed_doc
    except Exception as e:
        logger.warning(f"Document compression failed: {e}")
        return doc

class QueryEngine:
    def __init__(self):
        self.vector_store = load_vectorstore()
        self.client = load_vectorstore_client() if self.vector_store else None

        self.text_embeddings = BedrockEmbeddings(
            model_id=os.getenv("BEDROCK_EMBEDDING_MODEL_ID"),
            region_name=os.getenv("AWS_REGION"),
            aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
            aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
        )
        self.image_embeddings = MultiModalEmbeddings()

        self.prompt_selector = AWSPromptSelector()
        self.response_parser = AWSResponseParser()

        # Initialize token tracker
        self.token_tracker = get_token_tracker()

        # Try to initialize the LLM with proper error handling
        try:
            profile_arn = os.getenv("BEDROCK_LLM_PROFILE_ARN")
            
            if profile_arn:
                logger.info(f"Using Bedrock LLM profile ARN: {profile_arn}")
                llm_kwargs = dict(
                    model_id=profile_arn,
                    region_name=os.getenv("AWS_REGION"),
                    aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                    aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                    provider="amazon",
                    model_kwargs={"max_tokens": 1500, "temperature": 0.4}
                )
            else:
                model_id = os.getenv("BEDROCK_LLM_MODEL_ID", "anthropic.claude-3-sonnet-20240229-v1:0")
                logger.info(f"Using Bedrock LLM model ID: {model_id}")
                llm_kwargs = dict(
                    model_id=model_id,
                    region_name=os.getenv("AWS_REGION"),
                    aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                    aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                    model_kwargs={"max_tokens": 1500, "temperature": 0.4}
                )
            
            self.llm = ChatBedrock(**llm_kwargs)
            
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock LLM: {e}")
            # Create a simple fallback LLM that returns a fixed response
            from langchain.llms.fake import FakeListLLM
            self.llm = FakeListLLM(responses=["I'm sorry, but I couldn't access the AWS Bedrock model. Please check your AWS credentials and model access permissions."])
            logger.warning("Using fallback LLM due to initialization error")
        self._initialize_advanced_retrieval()

    def _initialize_advanced_retrieval(self):
        self.hybrid_retriever = HybridAWSRetriever(
            vectorstore=self.vector_store,
            weights=[0.7, 0.3],
            k=8
        )
        self.fusion_retriever = FusionRetriever(
            vectorstore=self.vector_store,
            k=8
        )
        aws_config = {
            "similarity_threshold": 0.3,
            "max_results": 8,
            "use_hybrid": True,
            "hybrid_weights": [0.7, 0.3],
            "use_mmr": False,
            "mmr_lambda": 0.5,
            "aws_boost": True,
            "filter_duplicates": True
        }
        self.configurable_retriever = ConfigurableRetriever(
            vectorstore=self.vector_store,
            llm=self.llm,
            config=aws_config
        )
        self.multi_vector_retriever = MultiVectorRetriever(
            vectorstore=self.vector_store,
            text_embedding_model=self.text_embeddings,
            image_embedding_model=self.image_embeddings
        )

    def get_architecture_diagrams(self, question: str = None) -> Dict[str, Any]:
        """
        Retrieve architecture diagrams from the knowledge base.
        
        Args:
            question: Optional question to contextualize the search
            
        Returns:
            Dict containing image data and metadata
        """
        # Define search queries for architecture diagrams
        base_query = "architecture diagram"
        if question:
            query = f"{question} {base_query}"
        else:
            query = base_query
            
        # Filter for documents that are images and contain architecture diagrams
        documents = self.vector_store.similarity_search(
            query, 
            k=10,
            filter={"is_image": True}
        )
        
        # Extract image data from the documents
        images = []
        for doc in documents:
            if doc.metadata.get("is_image", False) and "base64_image" in doc.metadata:
                images.append({
                    "id": doc.metadata.get("source", "unknown"),
                    "base64": doc.metadata.get("base64_image"),
                    "format": doc.metadata.get("image_format", "PNG"),
                    "caption": doc.metadata.get("image_caption", "Architecture Diagram"),
                    "source_document": doc.metadata.get("file_path", "unknown"),
                    "page_number": doc.metadata.get("page_number")
                })
        
        return {
            "images": images,
            "count": len(images),
            "query": query
        }

    def query_advanced(self, question: str, retrieval_config: Dict[str, Any] = None, image_data: str = None) -> Dict[str, Any]:
        if not self.vector_store:
            logger.error("Vector store not initialized. Please run ingestion first.")
            return {"answer": "Vector store not initialized. Please run ingestion first.", "sources": []}

        # Check if this is a request for architecture diagrams
        is_diagram_request = any(term in question.lower() for term in [
            "diagram", "architecture diagram", "visual", "picture", "image", "show me"
        ])
        
        # Process normally first
        validation_result = validate_prompt_inputs(question, "")
        if not validation_result["is_valid"]:
            return {
                "answer": f"Invalid input: {', '.join(validation_result['errors'])}",
                "sources": [],
                "validation_errors": validation_result["errors"]
            }

        sanitized_question = validation_result["sanitized_question"]

        # Allow switching retriever for testing
        retriever_type = (retrieval_config or {}).get("retriever_type", "multi_vector")
        logger.info(f"Using retriever type: {retriever_type}")
        
        try:
            if retriever_type == "fusion":
                logger.info("Using fusion retriever")
                documents = self.fusion_retriever.retrieve(sanitized_question)
            elif retriever_type == "hybrid":
                logger.info("Using hybrid retriever")
                documents = self.hybrid_retriever.get_relevant_documents(sanitized_question)
            elif retriever_type == "multi_vector":
                logger.info("Using multi-vector retriever")
                documents = self.multi_vector_retriever.retrieve(sanitized_question)
            elif self.configurable_retriever:
                logger.info("Using configurable retriever")
                documents = self.configurable_retriever.retrieve(
                    query=sanitized_question,
                    **(retrieval_config or {})
                )
            else:
                logger.info("Using default similarity search")
                documents = self.vector_store.similarity_search(sanitized_question, k=8)
                
            logger.info(f"Retrieved {len(documents)} documents from vector store")
        except Exception as e:
            logger.error(f"Error retrieving documents: {e}")
            documents = []

        # After retrieving documents, apply contextual compression
        compression_enabled = (retrieval_config or {}).get("use_compression", True)
        compression_threshold = (retrieval_config or {}).get("compression_threshold", 0.3)
        if compression_enabled:
            compressed_documents = []
            for doc in documents:
                try:
                    compressed_doc = compress_document(doc, sanitized_question, threshold=compression_threshold)
                    compressed_documents.append(compressed_doc)
                except Exception as e:
                    # Fallback to original doc if compression fails
                    compressed_documents.append(doc)
            documents = compressed_documents

        if not documents:
            return {
                "answer": "No relevant information found in the knowledge base.",
                "sources": [],
                "query_type": "no_results"
            }

        context_chunks = []
        sources = []
        
        # Check for images in the documents
        images = []
        for doc in documents:
            if doc.metadata.get("is_image", False) and "base64_image" in doc.metadata:
                images.append({
                    "id": doc.metadata.get("source", "unknown"),
                    "base64": doc.metadata.get("base64_image"),
                    "format": doc.metadata.get("image_format", "PNG"),
                    "caption": doc.metadata.get("image_caption", "Image"),
                    "source_document": doc.metadata.get("file_path", "unknown"),
                    "page_number": doc.metadata.get("page_number")
                })
            
            context_chunks.append(doc.page_content)
            sources.append({
                "source": doc.metadata.get("source", "unknown"),
                "score": doc.metadata.get("score", 0.0),
                "content_preview": doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content,
                "id": doc.metadata.get("id", "unknown"),
                "bm25_score": doc.metadata.get("bm25_score"),
                "aws_boost": doc.metadata.get("aws_boost")
            })

        # Always search for relevant images, especially for diagram requests
        # or if the query mentions images or diagrams
        if is_diagram_request or any(term in sanitized_question.lower() for term in ["image", "picture", "screenshot", "diagram"]):
            diagram_results = self.get_architecture_diagrams(sanitized_question)
            # Add any new images that weren't already found
            existing_ids = {img["id"] for img in images}
            for img in diagram_results.get("images", []):
                if img["id"] not in existing_ids:
                    images.append(img)
                    existing_ids.add(img["id"])

        context = "\n---\n".join(context_chunks)

        # Validate context and update validation result
        context_validation = validate_prompt_inputs(sanitized_question, context)

        # Prepare for multi-modal prompt
        prompt_messages = []
        if images:
            selected_template = self.prompt_selector.select_template(sanitized_question, context, has_image=True)
            # This is a simplified way to create a multi-modal prompt.
            # A more robust solution would use a dedicated Message class for images.
            prompt_messages.append({"role": "user", "content": sanitized_question})
            prompt_messages.append({"role": "user", "content": "Context:\n" + context})
            for img in images:
                prompt_messages.append({
                    "role": "user",
                    "content": [
                        {"type": "text", "text": f"Image: {img.get('caption', 'No caption')}"},
                        {"type": "image", "source": {"type": "base64", "media_type": "image/png", "data": img['base64']}}
                    ]
                })
            formatted_prompt = prompt_messages
        else:
            selected_template = self.prompt_selector.select_template(sanitized_question, context)
            formatted_prompt = selected_template.format_messages(
                question=sanitized_question,
                context=context
            )

        # Invoke LLM and track token usage
        try:
            response = self.llm.invoke(formatted_prompt)
            raw_answer = response.content if hasattr(response, 'content') else str(response)
            
            # Extract token usage from the response
            model_name = getattr(self.llm, 'model_id', 'unknown')
            token_usage = extract_token_usage(response, model_name)
        except Exception as e:
            logger.error(f"Error invoking LLM: {e}")
            # Provide a fallback response
            raw_answer = "I'm sorry, but I couldn't access the AWS Bedrock model. The system found relevant documents in the knowledge base, but couldn't generate a response using the LLM. Please check your AWS credentials and model access permissions."
            token_usage = TokenUsage(input_tokens=0, output_tokens=0, total_tokens=0, model_name="fallback")

        # If no token usage metadata is available, estimate from text
        if token_usage.input_tokens == 0 and token_usage.output_tokens == 0:
            # Estimate input tokens from the formatted prompt
            prompt_text = ""
            if hasattr(formatted_prompt, '__iter__'):
                for msg in formatted_prompt:
                    if hasattr(msg, 'content'):
                        prompt_text += msg.content + " "
            else:
                prompt_text = str(formatted_prompt)
                
            # Get model name safely
            model_name = getattr(self.llm, 'model_id', 'unknown')

            token_usage = self.token_tracker.create_token_usage_from_text(
                input_text=prompt_text,
                output_text=raw_answer,
                model_name=model_name
            )

        parsed_response = self.response_parser.parse(raw_answer)
        query_type = self._classify_query_type(sanitized_question)

        # Extract AWS services mentioned
        aws_services = extract_aws_services(context + " " + raw_answer)

        # Determine query characteristics
        query_characteristics = self._analyze_query_characteristics(sanitized_question, context)

        # For diagram requests, modify the answer to mention the images
        if is_diagram_request and images:
            image_info = f"\n\nI've found {len(images)} architecture diagram(s) in the documents that might be relevant to your query."
            parsed_response["answer"] += image_info

        return {
            "answer": parsed_response["answer"],
            "sources": sources,
            "query_type": query_type,
            "confidence": parsed_response.get("confidence"),
            "key_findings": parsed_response.get("key_findings", []),
            "has_aws_sop_content": parsed_response.get("has_aws_sop_content", False),
            "has_aws_content": parsed_response.get("has_aws_content", False),
            "aws_services_mentioned": aws_services,
            "query_characteristics": query_characteristics,
            "validation_warnings": validation_result.get("warnings", []) + context_validation.get("warnings", []),
            "retrieval_method": "advanced_configurable",
            "template_used": query_type,
            "retrieval_config": retrieval_config or {},
            "context_quality": self._assess_context_quality(context, sanitized_question),
            "images": images,
            "has_images": len(images) > 0,
            "token_usage": token_usage.to_dict()
        }

    def _classify_query_type(self, question: str) -> str:
        """
        Classify the query type based on keywords and content.

        Args:
            question: User question to classify

        Returns:
            str: Query type classification
        """
        question_lower = question.lower()

        # Define keyword patterns for different query types
        evaluation_keywords = ["evaluate", "assess", "analyze", "review", "compare", "architecture"]
        service_keywords = ["configure", "setup", "install", "deploy", "ec2", "s3", "rds", "lambda"]
        comparison_keywords = ["compare", "versus", "vs", "difference", "better", "choose"]
        general_keywords = ["how", "what", "why", "when", "procedure", "process", "sop"]

        # Score each category
        evaluation_score = sum(1 for keyword in evaluation_keywords if keyword in question_lower)
        service_score = sum(1 for keyword in service_keywords if keyword in question_lower)
        comparison_score = sum(1 for keyword in comparison_keywords if keyword in question_lower)
        general_score = sum(1 for keyword in general_keywords if keyword in question_lower)

        # Determine query type based on highest score
        scores = {
            "architecture_evaluation": evaluation_score,
            "service_analysis": service_score,
            "service_comparison": comparison_score,
            "general_aws": general_score
        }

        # Return the type with highest score, default to general_aws
        max_score = max(scores.values())
        if max_score == 0:
            return "general_aws"

        return max(scores, key=scores.get)

    def _analyze_query_characteristics(self, question: str, context: str) -> Dict[str, Any]:
        """
        Analyze query characteristics for enhanced insights.

        Args:
            question: User question
            context: Retrieved context

        Returns:
            Dict containing query analysis
        """
        question_lower = question.lower()
        context_lower = context.lower()

        characteristics = {
            "is_security_focused": any(term in question_lower for term in [
                "security", "iam", "permission", "access", "encrypt", "mfa", "auth"
            ]),
            "is_configuration_focused": any(term in question_lower for term in [
                "configure", "setup", "install", "deploy", "config"
            ]),
            "is_cost_focused": any(term in question_lower for term in [
                "cost", "billing", "price", "budget", "optimize", "save"
            ]),
            "is_compliance_focused": any(term in question_lower for term in [
                "compliance", "audit", "regulation", "standard", "policy"
            ]),
            "is_troubleshooting": any(term in question_lower for term in [
                "error", "issue", "problem", "troubleshoot", "debug", "fix"
            ]),
            "has_procedural_context": any(term in context_lower for term in [
                "step", "procedure", "process", "workflow", "guideline"
            ]),
            "complexity_level": self._assess_complexity(question)
        }

        return characteristics

    def _assess_complexity(self, question: str) -> str:
        """
        Assess the complexity level of a question.

        Args:
            question: User question

        Returns:
            str: Complexity level (low, medium, high)
        """
        question_lower = question.lower()

        # High complexity indicators
        high_complexity_terms = [
            "architecture", "multi-account", "enterprise", "migration", "integration",
            "disaster recovery", "high availability", "scalability", "performance"
        ]

        # Medium complexity indicators
        medium_complexity_terms = [
            "configure", "setup", "deploy", "security group", "vpc", "subnet",
            "load balancer", "auto scaling", "monitoring"
        ]

        if any(term in question_lower for term in high_complexity_terms):
            return "high"
        elif any(term in question_lower for term in medium_complexity_terms):
            return "medium"
        else:
            return "low"

    def _assess_context_quality(self, context: str, question: str) -> Dict[str, Any]:
        """Assess the quality and relevance of retrieved context."""
        aws_services = extract_aws_services(context)

        quality_metrics = {
            "context_length": len(context),
            "has_aws_services": len(aws_services) > 0,
            "aws_services_count": len(aws_services),
            "has_procedural_content": any(term in context.lower() for term in [
                "step", "procedure", "process", "workflow", "guideline"
            ]),
            "relevance_score": self._calculate_relevance_score(context, question),
            "completeness_score": min(len(context) / 5000, 1.0)  # Normalized to 1.0
        }

        return quality_metrics

    def _calculate_relevance_score(self, context: str, question: str) -> float:
        """
        Calculate a simple relevance score between context and question.

        Args:
            context: Retrieved context
            question: User question

        Returns:
            float: Relevance score between 0 and 1
        """
        if not context or not question:
            return 0.0

        # Simple keyword overlap scoring
        question_words = set(question.lower().split())
        context_words = set(context.lower().split())

        # Remove common stop words
        stop_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"}
        question_words = question_words - stop_words
        context_words = context_words - stop_words

        if not question_words:
            return 0.0

        # Calculate overlap ratio
        overlap = len(question_words.intersection(context_words))
        return min(overlap / len(question_words), 1.0)

    def query_diagram_graph(self, graph_data: Dict[str, Any], question: str) -> Dict[str, Any]:
        """
        Query a diagram graph to answer questions about its structure.
        
        Args:
            graph_data: The diagram graph in node-link data format
            question: The user's question about the diagram
            
        Returns:
            A dictionary containing the answer to the question
        """
        try:
            G = nx.node_link_graph(graph_data)
            question_lower = question.lower()
            
            # Example query: "What is connected to the database?"
            if "connected to" in question_lower:
                parts = question_lower.split("connected to")
                target_node_text = parts[1].strip().replace("?", "")
                
                # Find the target node
                target_node_id = None
                for node, data in G.nodes(data=True):
                    if data.get("text") and target_node_text in data["text"].lower():
                        target_node_id = node
                        break
                
                if target_node_id:
                    connected_nodes = list(G.predecessors(target_node_id)) + list(G.successors(target_node_id))
                    connected_node_names = [G.nodes[node_id].get("text", "Unnamed Component") for node_id in connected_nodes]
                    
                    if connected_node_names:
                        answer = f"The following components are connected to {target_node_text}: {', '.join(connected_node_names)}."
                    else:
                        answer = f"No components are directly connected to {target_node_text} in the diagram."
                else:
                    answer = f"Could not find a component named '{target_node_text}' in the diagram."
                    
                return {"answer": answer}

            # Add more query patterns here...
            
            return {"answer": "I can't answer that question about the diagram yet. Please try asking about connections between components."}
            
        except Exception as e:
            logger.error(f"Error querying diagram graph: {e}")
            return {"answer": "I'm sorry, but I encountered an error while analyzing the diagram's structure."}