#!/usr/bin/env python3
"""
Test the actual FastAPI endpoints for image analysis.
"""

import requests
import base64
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
import json

def create_test_image():
    """Create a simple test image with text for testing."""
    img = Image.new('RGB', (400, 300), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        font = ImageFont.load_default()
    
    draw.text((50, 50), "AWS Architecture Diagram", fill='black', font=font)
    draw.rectangle([50, 100, 150, 150], outline='blue', width=2)
    draw.text((60, 115), "EC2", fill='blue', font=font)
    
    draw.rectangle([200, 100, 300, 150], outline='green', width=2)
    draw.text((210, 115), "RDS", fill='green', font=font)
    
    draw.line([150, 125, 200, 125], fill='red', width=3)
    draw.polygon([(195, 120), (200, 125), (195, 130)], fill='red')
    
    draw.text((50, 200), "Test Architecture", fill='black', font=font)
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def test_bedrock_endpoint():
    """Test the /analyze/image endpoint (AWS Bedrock)."""
    print("=" * 60)
    print("TESTING /analyze/image ENDPOINT (AWS BEDROCK)")
    print("=" * 60)
    
    try:
        # Create test image
        image_bytes = create_test_image()
        
        # Prepare the file for upload
        files = {
            'file': ('test_image.png', image_bytes, 'image/png')
        }
        
        # Make the request
        response = requests.post(
            'http://localhost:8888/analyze/image',
            files=files,
            timeout=60
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ SUCCESS")
            print(f"Status: {result.get('status', 'unknown')}")
            print(f"Model ID: {result.get('model_id', 'unknown')}")
            print(f"Extracted Text: {result.get('extracted_text', 'None')[:100]}...")
            print(f"Analysis: {result.get('analysis', 'None')[:200]}...")
            return True
        else:
            print(f"❌ FAILED")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_openrouter_endpoint():
    """Test the /analyze/image/openrouter endpoint."""
    print("\n" + "=" * 60)
    print("TESTING /analyze/image/openrouter ENDPOINT")
    print("=" * 60)
    
    try:
        # Create test image
        image_bytes = create_test_image()
        
        # Prepare the file and form data for upload
        files = {
            'file': ('test_image.png', image_bytes, 'image/png')
        }
        data = {
            'prompt': 'Analyze this AWS architecture diagram and explain the components and their relationships.'
        }
        
        # Make the request
        response = requests.post(
            'http://localhost:8888/analyze/image/openrouter',
            files=files,
            data=data,
            timeout=60
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ SUCCESS")
            print(f"Status: {result.get('status', 'unknown')}")
            print(f"Model ID: {result.get('model_id', 'unknown')}")
            print(f"Extracted Text: {result.get('extracted_text', 'None')[:100]}...")
            print(f"Analysis: {result.get('analysis', 'None')[:200]}...")
            return True
        else:
            print(f"❌ FAILED")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_health_endpoint():
    """Test the health endpoint."""
    print("\n" + "=" * 60)
    print("TESTING /health ENDPOINT")
    print("=" * 60)
    
    try:
        response = requests.get('http://localhost:8888/health', timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ SUCCESS: {result}")
            return True
        else:
            print(f"❌ FAILED: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Main test function."""
    print("RAG QUADRANT - API ENDPOINT TESTS")
    print("=" * 60)
    
    # Test health endpoint first
    health_ok = test_health_endpoint()
    
    if not health_ok:
        print("\n❌ Backend is not healthy. Please start the backend first.")
        return
    
    # Test image analysis endpoints
    bedrock_ok = test_bedrock_endpoint()
    openrouter_ok = test_openrouter_endpoint()
    
    # Summary
    print("\n" + "=" * 60)
    print("API ENDPOINT TEST SUMMARY")
    print("=" * 60)
    print(f"Health Endpoint: {'✅ PASS' if health_ok else '❌ FAIL'}")
    print(f"AWS Bedrock Endpoint: {'✅ PASS' if bedrock_ok else '❌ FAIL'}")
    print(f"OpenRouter Endpoint: {'✅ PASS' if openrouter_ok else '❌ FAIL'}")
    
    if bedrock_ok and openrouter_ok:
        print("\n🎉 ALL IMAGE ANALYSIS ENDPOINTS ARE WORKING!")
        print("✅ The image analysis feature is fully functional!")
    elif bedrock_ok or openrouter_ok:
        print("\n⚠️ PARTIAL SUCCESS - At least one endpoint is working")
    else:
        print("\n❌ ALL ENDPOINTS FAILED")

if __name__ == "__main__":
    main()
