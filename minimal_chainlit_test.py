#!/usr/bin/env python3
"""
Minimal chainlit test to isolate the issue.
"""

import chainlit as cl
import aiohttp
from PIL import Image, ImageDraw
from io import BytesIO
from typing import Dict, Any, Optional

# Same settings as the main frontend
TIMEOUT = aiohttp.ClientTimeout(total=120)
API_BASE_URL = "http://localhost:8888"

def create_test_image():
    """Create a test image."""
    img = Image.new('RGB', (200, 150), color='white')
    draw = ImageDraw.Draw(img)
    draw.text((20, 20), "AWS Test", fill='black')
    draw.rectangle([20, 50, 80, 90], outline='blue', width=2)
    draw.text((25, 65), "EC2", fill='blue')
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

async def analyze_image_with_ai_minimal(image_bytes: bytes, use_gemini: bool = False, custom_prompt: Optional[str] = None) -> Dict[str, Any]:
    """Minimal version of the analyze function."""
    
    # Choose the appropriate endpoint based on the use_gemini flag
    if use_gemini:
        url = f"{API_BASE_URL}/analyze/image/gemini"
    else:
        url = f"{API_BASE_URL}/analyze/image"
        
    data = aiohttp.FormData()
    data.add_field('file', image_bytes, filename='upload.png', content_type='image/png')
    
    # Add custom prompt for Gemini
    if use_gemini and custom_prompt:
        data.add_field('prompt', custom_prompt)
    
    async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
        async with session.post(url, data=data) as response:
            print(f"MINIMAL DEBUG: Response status: {response.status}")
            if response.status == 200:
                result = await response.json()
                print(f"MINIMAL DEBUG: Raw result keys: {list(result.keys())}")
                print(f"MINIMAL DEBUG: Raw analysis type: {type(result.get('analysis'))}")
                print(f"MINIMAL DEBUG: Raw analysis length: {len(result.get('analysis', ''))}")
                print(f"MINIMAL DEBUG: Raw analysis preview: {repr(result.get('analysis', '')[:100])}")
                return result
            else:
                error_text = await response.text()
                raise Exception(f"Image analysis failed: {error_text}")

@cl.on_message
async def main(message: cl.Message):
    """Handle incoming messages."""
    
    if message.content.lower() == "test":
        await cl.Message(content="🔧 Starting minimal test...").send()
        
        # Create test image
        image_bytes = create_test_image()
        await cl.Message(content=f"🔧 Created image: {len(image_bytes)} bytes").send()
        
        # Test with Gemini
        try:
            await cl.Message(content="🔧 Calling analyze_image_with_ai...").send()
            analysis_result = await analyze_image_with_ai_minimal(image_bytes, use_gemini=True)
            
            await cl.Message(content=f"🔧 Got result with keys: {list(analysis_result.keys()) if analysis_result else 'None'}").send()
            
            if analysis_result:
                await cl.Message(content=f"🔧 Status: {analysis_result.get('status')}, Analysis length: {len(analysis_result.get('analysis', ''))}").send()
                
                analysis = analysis_result.get("analysis", "No analysis available")
                await cl.Message(content=f"🔧 Analysis type: {type(analysis)}, length: {len(analysis)}").send()
                
                if analysis and len(analysis) > 0:
                    await cl.Message(content=f"✅ SUCCESS! Analysis preview: {analysis[:200]}...").send()
                else:
                    await cl.Message(content="❌ FAILED! Analysis is empty").send()
            else:
                await cl.Message(content="❌ FAILED! No result returned").send()
                
        except Exception as e:
            await cl.Message(content=f"❌ EXCEPTION: {str(e)}").send()
            
    else:
        await cl.Message(content="Type 'test' to run the minimal test.").send()

if __name__ == "__main__":
    cl.run()
