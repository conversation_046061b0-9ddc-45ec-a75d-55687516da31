"""
Unit tests for the image analysis orchestrator.
"""
import os
import pytest
import asyncio
from unittest.mock import MagicMock, patch
from datetime import datetime

from image_analysis.models import (
    ImageAnalysisRequest, 
    ImageAnalysisResult, 
    ImageType, 
    AnalysisStatus, 
    Analysis,
    AnalysisDetail,
    Recommendation,
    BoundingBox
)
from image_analysis.orchestrator import AnalysisOrchestrator, ProcessingStep


class TestAnalysisOrchestrator:
    """Test cases for the AnalysisOrchestrator class."""
    
    # Constants for testing
    TEST_REQUEST_ID = "test-request-id"
    TEST_IMAGE_PATH = "/tmp/test.png"
    
    @pytest.fixture
    def mock_services(self):
        """Create mock services for testing."""
        image_store = MagicMock()
        result_store = MagicMock()
        ocr_service = MagicMock()
        error_recognition_service = MagicMock()
        diagram_text_service = MagicMock()
        image_classifier = MagicMock()
        diagram_component_service = MagicMock()
        
        # Configure mock image_store
        image_store.image_exists.return_value = True
        
        # Configure mock result_store
        mock_request = ImageAnalysisRequest(
            id="test-request-id",
            original_filename="test.png",
            content_type="image/png",
            file_size=1024,
            storage_path="/tmp/test.png"
        )
        result_store.get_request.return_value = mock_request
        
        # Configure mock ocr_service
        ocr_service.extract_text.return_value = "Sample text content"
        ocr_service.extract_text_with_layout.return_value = {
            "text": "Sample text content",
            "blocks": []
        }
        ocr_service.extract_tables.return_value = []
        
        # Configure mock image_classifier
        image_classifier.classify_image.return_value = (
            ImageType.ARCHITECTURE,
            0.85,
            {"feature1": 0.9, "feature2": 0.8}
        )
        
        # Configure mock diagram_component_service
        diagram_component_service.recognize_components.return_value = ([], [])
        
        # Configure mock diagram_text_service
        diagram_text_service.extract_labels.return_value = {}
        
        # Configure mock error_recognition_service
        error_recognition_service.identify_errors.return_value = []
        error_recognition_service.suggest_solutions.return_value = []
        error_recognition_service.classify_error_type.return_value = (None, None, 0.0)
        
        return {
            "image_store": image_store,
            "result_store": result_store,
            "ocr_service": ocr_service,
            "error_recognition_service": error_recognition_service,
            "diagram_text_service": diagram_text_service,
            "image_classifier": image_classifier,
            "diagram_component_service": diagram_component_service
        }
    
    @pytest.fixture
    def orchestrator(self, mock_services):
        """Create an AnalysisOrchestrator instance for testing."""
        return AnalysisOrchestrator(
            image_store=mock_services["image_store"],
            result_store=mock_services["result_store"],
            ocr_service=mock_services["ocr_service"],
            error_recognition_service=mock_services["error_recognition_service"],
            diagram_text_service=mock_services["diagram_text_service"],
            image_classifier=mock_services["image_classifier"],
            diagram_component_service=mock_services["diagram_component_service"]
        )
    
    @pytest.mark.asyncio
    async def test_process_request_success(self, orchestrator, mock_services):
        """Test successful processing of a request."""
        # Arrange
        request_id = "test-request-id"
        
        # Act
        result = await orchestrator.process_request(request_id)
        
        # Assert
        assert result is True
        mock_services["result_store"].update_request.assert_called()
        mock_services["result_store"].store_result.assert_called()
        
        # Verify the request status was updated to COMPLETED
        request = mock_services["result_store"].get_request.return_value
        assert request.status == AnalysisStatus.COMPLETED
    
    @pytest.mark.asyncio
    async def test_process_request_missing_request(self, orchestrator, mock_services):
        """Test processing when the request is not found."""
        # Arrange
        request_id = "missing-request-id"
        mock_services["result_store"].get_request.return_value = None
        
        # Act
        result = await orchestrator.process_request(request_id)
        
        # Assert
        assert result is False
        mock_services["result_store"].update_request.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_process_request_missing_image(self, orchestrator, mock_services):
        """Test processing when the image file is not found."""
        # Arrange
        request_id = "test-request-id"
        mock_services["image_store"].image_exists.return_value = False
        
        # Act
        result = await orchestrator.process_request(request_id)
        
        # Assert
        assert result is False
        
        # Verify the request status was updated to FAILED
        request = mock_services["result_store"].get_request.return_value
        assert request.status == AnalysisStatus.FAILED
    
    @pytest.mark.asyncio
    async def test_process_request_text_extraction_error(self, orchestrator, mock_services):
        """Test processing when text extraction fails."""
        # Arrange
        request_id = "test-request-id"
        mock_services["ocr_service"].extract_text.side_effect = Exception("OCR error")
        
        # Act
        result = await orchestrator.process_request(request_id)
        
        # Assert
        assert result is False
        
        # Verify the request status was updated to FAILED
        request = mock_services["result_store"].get_request.return_value
        assert request.status == AnalysisStatus.FAILED
    
    @pytest.mark.asyncio
    async def test_process_request_classification_error(self, orchestrator, mock_services):
        """Test processing when image classification fails."""
        # Arrange
        request_id = "test-request-id"
        mock_services["image_classifier"].classify_image.side_effect = Exception("Classification error")
        
        # Act
        result = await orchestrator.process_request(request_id)
        
        # Assert
        assert result is False
        
        # Verify the request status was updated to FAILED
        request = mock_services["result_store"].get_request.return_value
        assert request.status == AnalysisStatus.FAILED
    
    @pytest.mark.asyncio
    async def test_process_request_feature_extraction_error(self, orchestrator, mock_services):
        """Test processing when feature extraction fails."""
        # Arrange
        request_id = "test-request-id"
        mock_services["diagram_component_service"].recognize_components.side_effect = Exception("Feature extraction error")
        
        # Act
        result = await orchestrator.process_request(request_id)
        
        # Assert
        assert result is False
        
        # Verify the request status was updated to FAILED
        request = mock_services["result_store"].get_request.return_value
        assert request.status == AnalysisStatus.FAILED
    
    @pytest.mark.asyncio
    async def test_validate_request(self, orchestrator, mock_services):
        """Test the _validate_request method."""
        # Arrange
        context = {
            "request": mock_services["result_store"].get_request.return_value,
            "image_path": "/tmp/test.png"
        }
        
        # Act
        success, result = await orchestrator._validate_request(context)
        
        # Assert
        assert success is True
        assert result["valid"] is True
        mock_services["image_store"].image_exists.assert_called_with("/tmp/test.png")
    
    @pytest.mark.asyncio
    async def test_extract_text(self, orchestrator, mock_services):
        """Test the _extract_text method."""
        # Arrange
        context = {
            "image_path": "/tmp/test.png"
        }
        
        # Act
        success, result = await orchestrator._extract_text(context)
        
        # Assert
        assert success is True
        assert result["text_content"] == "Sample text content"
        mock_services["ocr_service"].extract_text.assert_called_with("/tmp/test.png")
        mock_services["ocr_service"].extract_text_with_layout.assert_called_with("/tmp/test.png")
        mock_services["ocr_service"].extract_tables.assert_called_with("/tmp/test.png")
    
    @pytest.mark.asyncio
    async def test_classify_image(self, orchestrator, mock_services):
        """Test the _classify_image method."""
        # Arrange
        context = {
            "image_path": "/tmp/test.png",
            "step_results": {
                ProcessingStep.TEXT_EXTRACTION.value: {
                    "text_content": "Sample text content"
                }
            }
        }
        
        # Act
        success, result = await orchestrator._classify_image(context)
        
        # Assert
        assert success is True
        assert result["image_type"] == ImageType.ARCHITECTURE
        assert result["confidence"] == 0.85
        assert "features" in result
        mock_services["image_classifier"].classify_image.assert_called_with(
            "/tmp/test.png", "Sample text content"
        )
    
    @pytest.mark.asyncio
    async def test_extract_features_architecture(self, orchestrator, mock_services):
        """Test the _extract_features method for architecture images."""
        # Arrange
        context = {
            "image_path": "/tmp/test.png",
            "step_results": {
                ProcessingStep.CLASSIFICATION.value: {
                    "image_type": ImageType.ARCHITECTURE
                },
                ProcessingStep.TEXT_EXTRACTION.value: {
                    "text_content": "Sample text content"
                }
            }
        }
        
        # Act
        success, result = await orchestrator._extract_features(context)
        
        # Assert
        assert success is True
        assert "components" in result
        assert "connections" in result
        assert "labels" in result
        mock_services["diagram_component_service"].recognize_components.assert_called_with("/tmp/test.png")
        mock_services["diagram_text_service"].extract_labels.assert_called_with("/tmp/test.png")
    
    @pytest.mark.asyncio
    async def test_extract_features_error(self, orchestrator, mock_services):
        """Test the _extract_features method for error images."""
        # Arrange
        context = {
            "image_path": "/tmp/test.png",
            "step_results": {
                ProcessingStep.CLASSIFICATION.value: {
                    "image_type": ImageType.ERROR
                },
                ProcessingStep.TEXT_EXTRACTION.value: {
                    "text_content": "Sample text content"
                }
            }
        }
        
        # Act
        success, result = await orchestrator._extract_features(context)
        
        # Assert
        assert success is True
        assert "identified_errors" in result
        assert "solutions" in result
        assert "severity" in result
        assert "category" in result
        mock_services["error_recognition_service"].identify_errors.assert_called_with("Sample text content")
        mock_services["error_recognition_service"].suggest_solutions.assert_called_with("Sample text content")
        mock_services["error_recognition_service"].classify_error_type.assert_called_with("Sample text content")
    
    @pytest.mark.asyncio
    async def test_analyze_content_architecture(self, orchestrator):
        """Test the _analyze_content method for architecture images."""
        # Arrange
        context = {
            "step_results": {
                ProcessingStep.CLASSIFICATION.value: {
                    "image_type": ImageType.ARCHITECTURE,
                    "confidence": 0.85
                },
                ProcessingStep.FEATURE_EXTRACTION.value: {
                    "components": [],
                    "connections": [],
                    "labels": {}
                },
                ProcessingStep.TEXT_EXTRACTION.value: {
                    "text_content": "Sample text content"
                }
            }
        }
        
        # Mock the _analyze_architecture method
        orchestrator._analyze_architecture = MagicMock(return_value={
            "summary": "Architecture analysis",
            "details": [],
            "recommendations": []
        })
        
        # Act
        success, result = await orchestrator._analyze_content(context)
        
        # Assert
        assert success is True
        assert "summary" in result
        assert "metadata" in result
        orchestrator._analyze_architecture.assert_called_with(context["step_results"][ProcessingStep.FEATURE_EXTRACTION.value])
    
    @pytest.mark.asyncio
    async def test_analyze_content_error(self, orchestrator):
        """Test the _analyze_content method for error images."""
        # Arrange
        context = {
            "step_results": {
                ProcessingStep.CLASSIFICATION.value: {
                    "image_type": ImageType.ERROR,
                    "confidence": 0.85
                },
                ProcessingStep.FEATURE_EXTRACTION.value: {
                    "identified_errors": [],
                    "solutions": [],
                    "severity": None,
                    "category": None
                },
                ProcessingStep.TEXT_EXTRACTION.value: {
                    "text_content": "Sample error text"
                }
            }
        }
        
        # Mock the _analyze_error method
        orchestrator._analyze_error = MagicMock(return_value={
            "summary": "Error analysis",
            "details": [],
            "recommendations": []
        })
        
        # Act
        success, result = await orchestrator._analyze_content(context)
        
        # Assert
        assert success is True
        assert "summary" in result
        assert "metadata" in result
        orchestrator._analyze_error.assert_called_with(context["step_results"][ProcessingStep.FEATURE_EXTRACTION.value])
    
    @pytest.mark.asyncio
    async def test_generate_result(self, orchestrator, mock_services):
        """Test the _generate_result method."""
        # Arrange
        context = {
            "request_id": "test-request-id",
            "image_path": "/tmp/test.png",
            "step_results": {
                ProcessingStep.CLASSIFICATION.value: {
                    "image_type": ImageType.ARCHITECTURE,
                    "confidence": 0.85
                },
                ProcessingStep.TEXT_EXTRACTION.value: {
                    "text_content": "Sample text content"
                },
                ProcessingStep.ANALYSIS.value: {
                    "summary": "Architecture analysis",
                    "details": [
                        {
                            "type": "component",
                            "content": "Component 1",
                            "confidence": 0.9
                        }
                    ],
                    "recommendations": [
                        {
                            "type": "suggestion",
                            "content": "Recommendation 1",
                            "priority": 1
                        }
                    ]
                }
            }
        }
        
        # Act
        success, result = await orchestrator._generate_result(context)
        
        # Assert
        assert success is True
        assert "result_id" in result
        mock_services["result_store"].store_result.assert_called()
        
        # Verify the result object
        result_obj = mock_services["result_store"].store_result.call_args[0][0]
        assert result_obj.request_id == "test-request-id"
        assert result_obj.analysis_type == ImageType.ARCHITECTURE
        assert result_obj.confidence == 0.85
        assert result_obj.image_url == "/tmp/test.png"
        assert result_obj.text_content == "Sample text content"
        assert result_obj.analysis.summary == "Architecture analysis"
        assert len(result_obj.analysis.details) == 1
        assert len(result_obj.analysis.recommendations) == 1
        
    @pytest.mark.asyncio
    async def test_process_request_with_retries_success(self, orchestrator, mock_services):
        """Test successful processing of a request with retries."""
        # Arrange
        request_id = self.TEST_REQUEST_ID
        
        # Act
        result = await orchestrator.process_request_with_retries(request_id)
        
        # Assert
        assert result is True
        mock_services["result_store"].update_request.assert_called()
        mock_services["result_store"].store_result.assert_called()
        
        # Verify the request status was updated to COMPLETED
        request = mock_services["result_store"].get_request.return_value
        assert request.status == AnalysisStatus.COMPLETED
    
    @pytest.mark.asyncio
    async def test_execute_step_with_retries_success(self, orchestrator, mock_services):
        """Test successful execution of a step with retries."""
        # Arrange
        context = {
            "request_id": self.TEST_REQUEST_ID,
            "image_path": self.TEST_IMAGE_PATH,
            "step_results": {
                ProcessingStep.TEXT_EXTRACTION.value: {
                    "text_content": "Sample text content"
                }
            },
            "step_status": {step: "pending" for step in orchestrator.processing_steps},
            "metrics": {
                "step_durations": {},
                "step_retries": {},
                "errors": [],
                "fallbacks_used": []
            }
        }
        
        step = ProcessingStep.CLASSIFICATION
        process_func = orchestrator._classify_image
        
        # Act
        result = await orchestrator._execute_step_with_retries(step, process_func, context)
        
        # Assert
        assert result is True
        assert context["step_status"][step] == "completed"
        assert step.value in context["step_results"]
        assert context["metrics"]["step_retries"][step.value] == 0
    
    @pytest.mark.asyncio
    async def test_execute_step_with_retries_failure_and_fallback(self, orchestrator, mock_services):
        """Test execution of a step that fails but succeeds with fallback."""
        # Arrange
        context = {
            "request_id": self.TEST_REQUEST_ID,
            "image_path": self.TEST_IMAGE_PATH,
            "step_results": {
                ProcessingStep.TEXT_EXTRACTION.value: {
                    "text_content": "Sample text content"
                }
            },
            "step_status": {step: "pending" for step in orchestrator.processing_steps},
            "metrics": {
                "step_durations": {},
                "step_retries": {},
                "errors": [],
                "fallbacks_used": []
            }
        }
        
        step = ProcessingStep.CLASSIFICATION
        
        # Create a failing process function that will be retried
        async def failing_process_func(ctx):
            raise Exception("Simulated failure")
        
        # Mock the fallback function to succeed
        orchestrator._fallback_classification = MagicMock(return_value=(True, {
            "image_type": ImageType.ARCHITECTURE,
            "confidence": 0.5,
            "features": {},
            "is_fallback": True
        }))
        
        # Set the fallback strategy for the step
        orchestrator.fallback_strategies[step] = orchestrator._fallback_classification
        
        # Set max retries to 1 for faster testing
        orchestrator.MAX_RETRIES = 1
        
        # Act
        result = await orchestrator._execute_step_with_retries(step, failing_process_func, context)
        
        # Assert
        assert result is True
        assert context["step_status"][step] == "completed"
        assert step.value in context["step_results"]
        assert context["step_results"][step.value]["is_fallback"] is True
        assert context["metrics"]["step_retries"][step.value] == 2  # Initial attempt + 1 retry
        assert step.value in context["metrics"]["fallbacks_used"]
        orchestrator._fallback_classification.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_fallback_success(self, orchestrator):
        """Test successful execution of a fallback strategy."""
        # Arrange
        context = {
            "request_id": self.TEST_REQUEST_ID,
            "image_path": self.TEST_IMAGE_PATH,
            "step_results": {
                ProcessingStep.TEXT_EXTRACTION.value: {
                    "text_content": "Sample text content"
                }
            },
            "step_status": {step: "pending" for step in orchestrator.processing_steps},
            "metrics": {
                "step_durations": {},
                "step_retries": {},
                "errors": [],
                "fallbacks_used": []
            }
        }
        
        step = ProcessingStep.CLASSIFICATION
        
        # Mock the fallback function
        orchestrator._fallback_classification = MagicMock(return_value=(True, {
            "image_type": ImageType.ARCHITECTURE,
            "confidence": 0.5,
            "features": {},
            "is_fallback": True
        }))
        
        # Set the fallback strategy for the step
        orchestrator.fallback_strategies[step] = orchestrator._fallback_classification
        
        # Act
        result = await orchestrator._execute_fallback(step, context)
        
        # Assert
        assert result is True
        assert context["step_status"][step] == "completed"
        assert step.value in context["step_results"]
        assert context["step_results"][step.value]["is_fallback"] is True
        assert step.value in context["metrics"]["fallbacks_used"]
        orchestrator._fallback_classification.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_fallback_failure(self, orchestrator):
        """Test failed execution of a fallback strategy."""
        # Arrange
        context = {
            "request_id": self.TEST_REQUEST_ID,
            "image_path": self.TEST_IMAGE_PATH,
            "step_results": {
                ProcessingStep.TEXT_EXTRACTION.value: {
                    "text_content": "Sample text content"
                }
            },
            "step_status": {step: "pending" for step in orchestrator.processing_steps},
            "metrics": {
                "step_durations": {},
                "step_retries": {},
                "errors": [],
                "fallbacks_used": []
            }
        }
        
        step = ProcessingStep.CLASSIFICATION
        
        # Mock the fallback function to fail
        orchestrator._fallback_classification = MagicMock(return_value=(False, {
            "error": "Fallback failed"
        }))
        
        # Set the fallback strategy for the step
        orchestrator.fallback_strategies[step] = orchestrator._fallback_classification
        
        # Act
        result = await orchestrator._execute_fallback(step, context)
        
        # Assert
        assert result is False
        assert context["step_status"][step] == "failed"
        assert step.value in context["metrics"]["fallbacks_used"]
        orchestrator._fallback_classification.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_fallback_text_extraction(self, orchestrator, mock_services):
        """Test the fallback strategy for text extraction."""
        # Arrange
        context = {
            "request_id": self.TEST_REQUEST_ID,
            "image_path": self.TEST_IMAGE_PATH
        }
        
        # Act
        success, result = await orchestrator._fallback_text_extraction(context)
        
        # Assert
        assert success is True
        assert "text_content" in result
        assert "is_fallback" in result
        assert result["is_fallback"] is True
    
    @pytest.mark.asyncio
    async def test_fallback_classification(self, orchestrator):
        """Test the fallback strategy for image classification."""
        # Arrange
        context = {
            "step_results": {
                ProcessingStep.TEXT_EXTRACTION.value: {
                    "text_content": "Sample architecture diagram with components and services"
                }
            }
        }
        
        # Act
        success, result = await orchestrator._fallback_classification(context)
        
        # Assert
        assert success is True
        assert "image_type" in result
        assert "confidence" in result
        assert "is_fallback" in result
        assert result["is_fallback"] is True
        assert result["image_type"] == ImageType.ARCHITECTURE  # Based on keywords in the sample text
    
    @pytest.mark.asyncio
    async def test_fallback_feature_extraction(self, orchestrator):
        """Test the fallback strategy for feature extraction."""
        # Arrange
        context = {
            "step_results": {
                ProcessingStep.CLASSIFICATION.value: {
                    "image_type": ImageType.ARCHITECTURE
                },
                ProcessingStep.TEXT_EXTRACTION.value: {
                    "text_content": "Sample text content"
                }
            }
        }
        
        # Act
        success, result = await orchestrator._fallback_feature_extraction(context)
        
        # Assert
        assert success is True
        assert "is_fallback" in result
        assert result["is_fallback"] is True
        
        # For architecture images
        assert "components" in result
        assert "connections" in result
        assert "labels" in result
        
        # Change image type and test again
        context["step_results"][ProcessingStep.CLASSIFICATION.value]["image_type"] = ImageType.ERROR
        success, result = await orchestrator._fallback_feature_extraction(context)
        
        # For error images
        assert "identified_errors" in result
        assert "solutions" in result
    
    @pytest.mark.asyncio
    async def test_fallback_analysis(self, orchestrator):
        """Test the fallback strategy for content analysis."""
        # Arrange
        context = {
            "step_results": {
                ProcessingStep.CLASSIFICATION.value: {
                    "image_type": ImageType.ARCHITECTURE
                },
                ProcessingStep.TEXT_EXTRACTION.value: {
                    "text_content": "Sample text content"
                }
            }
        }
        
        # Act
        success, result = await orchestrator._fallback_analysis(context)
        
        # Assert
        assert success is True
        assert "summary" in result
        assert "details" in result
        assert "recommendations" in result
        assert "is_fallback" in result
        assert result["is_fallback"] is True
        
        # Change image type and test again
        context["step_results"][ProcessingStep.CLASSIFICATION.value]["image_type"] = ImageType.ERROR
        success, result = await orchestrator._fallback_analysis(context)
        
        assert "summary" in result
        assert "details" in result
        assert "recommendations" in result
        assert result["is_fallback"] is True
    
    @pytest.mark.asyncio
    async def test_aggregate_results(self, orchestrator):
        """Test the result aggregation logic."""
        # Arrange
        context = {
            "request_id": self.TEST_REQUEST_ID,
            "metrics": {
                "total_duration": 1.5,
                "fallbacks_used": [ProcessingStep.TEXT_EXTRACTION.value],
                "errors": [{"step": ProcessingStep.CLASSIFICATION.value, "error": "Test error", "time": "2023-01-01T00:00:00"}]
            },
            "step_results": {
                ProcessingStep.VALIDATION.value: {"valid": True},
                ProcessingStep.TEXT_EXTRACTION.value: {"text_content": "Sample text", "is_fallback": True},
                ProcessingStep.CLASSIFICATION.value: {"image_type": ImageType.ARCHITECTURE, "confidence": 0.85},
                ProcessingStep.ANALYSIS.value: {
                    "summary": "Test summary",
                    "details": [{"type": "component", "content": "Test component"}],
                    "recommendations": [{"type": "suggestion", "content": "Test recommendation", "priority": 1}]
                }
            },
            "step_status": {
                ProcessingStep.VALIDATION: "completed",
                ProcessingStep.TEXT_EXTRACTION: "completed",
                ProcessingStep.CLASSIFICATION: "completed",
                ProcessingStep.FEATURE_EXTRACTION: "completed",
                ProcessingStep.ANALYSIS: "completed",
                ProcessingStep.RESULT_GENERATION: "completed"
            }
        }
        
        # Act
        result = await orchestrator.aggregate_results(context)
        
        # Assert
        assert result["request_id"] == self.TEST_REQUEST_ID
        assert result["processing_time"] == 1.5
        assert result["steps_completed"] == 6
        assert result["steps_failed"] == 0
        assert result["steps_skipped"] == 0
        assert len(result["fallbacks_used"]) == 1
        assert len(result["errors"]) == 1
        assert "summary" in result
        assert result["summary"] == "Test summary"
        assert "image_type" in result
        assert result["image_type"] == ImageType.ARCHITECTURE.value
        assert "confidence" in result
        assert result["confidence"] == 0.85