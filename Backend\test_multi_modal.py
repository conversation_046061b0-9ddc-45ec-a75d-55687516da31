import pytest
from fastapi.testclient import TestClient
from main import app
import base64
import os

client = TestClient(app)

@pytest.fixture
def sample_image_bytes():
    # Create a dummy image for testing
    from PIL import Image
    import io
    
    image = Image.new('RGB', (100, 100), color = 'red')
    buf = io.BytesIO()
    image.save(buf, format='PNG')
    return buf.getvalue()

def test_image_summarization(sample_image_bytes):
    # This is a simplified test. In a real application, you would mock the Bedrock API.
    from aws_utils import summarize_image_with_bedrock
    
    summary = summarize_image_with_bedrock(sample_image_bytes)
    assert isinstance(summary, str)
    assert len(summary) > 0

def test_multi_modal_query(sample_image_bytes):
    # This is a simplified test. In a real application, you would mock the Bedrock API.
    from query import QueryEngine
    
    engine = QueryEngine()
    
    # Base64 encode the image
    image_data = base64.b64encode(sample_image_bytes).decode('utf-8')
    
    result = engine.query_advanced(
        question="What is in this image?",
        image_data=image_data
    )
    
    assert "answer" in result
    assert isinstance(result["answer"], str)
    assert len(result["answer"]) > 0

def test_diagram_graph_query():
    # This is a simplified test. In a real application, you would use a real diagram graph.
    from query import QueryEngine
    
    engine = QueryEngine()
    
    # Create a dummy graph
    graph_data = {
        "nodes": [
            {"id": "node1", "text": "Database"},
            {"id": "node2", "text": "API Server"}
        ],
        "links": [
            {"source": "node2", "target": "node1"}
        ]
    }
    
    result = engine.query_diagram_graph(
        graph_data=graph_data,
        question="What is connected to the database?"
    )
    
    assert "answer" in result
    assert "API Server" in result["answer"]