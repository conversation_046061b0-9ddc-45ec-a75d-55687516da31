#!/usr/bin/env python
"""
Unit tests for the /query/advanced endpoint.
"""

import unittest
import requests
import json
import os
import logging
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
import sys

# Add the parent directory to the path so we can import from main.py
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from Backend.main import app

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("test_query_advanced.log")
    ]
)

logger = logging.getLogger(__name__)

class TestQueryAdvanced(unittest.TestCase):
    """Unit tests for the /query/advanced endpoint."""
    
    def setUp(self):
        """Set up the test client."""
        self.client = TestClient(app)
        
    def test_valid_query(self):
        """Test the endpoint with a valid query."""
        # Mock the QueryEngine.query_advanced method
        with patch('Backend.main.get_query_engine') as mock_get_query_engine:
            # Create a mock query engine
            mock_engine = MagicMock()
            mock_engine.query_advanced.return_value = {
                "answer": "This is a test answer",
                "sources": [
                    {
                        "source": "test_source.txt",
                        "score": 0.95,
                        "content_preview": "Test content"
                    }
                ],
                "query_type": "general_aws"
            }
            mock_get_query_engine.return_value = mock_engine
            
            # Make the request
            response = self.client.post(
                "/query/advanced",
                json={"question": "What is AWS EC2?"}
            )
            
            # Check the response
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json()["answer"], "This is a test answer")
            self.assertEqual(len(response.json()["sources"]), 1)
            self.assertEqual(response.json()["query_type"], "general_aws")
            
            # Check that the query engine was called with the correct parameters
            mock_engine.query_advanced.assert_called_once_with(
                question="What is AWS EC2?",
                retrieval_config=None
            )
    
    def test_empty_query(self):
        """Test the endpoint with an empty query."""
        # Make the request
        response = self.client.post(
            "/query/advanced",
            json={"question": ""}
        )
        
        # Check the response
        self.assertEqual(response.status_code, 500)
        self.assertIn("Query failed", response.json()["detail"])
    
    def test_retrieval_config(self):
        """Test the endpoint with a custom retrieval configuration."""
        # Mock the QueryEngine.query_advanced method
        with patch('Backend.main.get_query_engine') as mock_get_query_engine:
            # Create a mock query engine
            mock_engine = MagicMock()
            mock_engine.query_advanced.return_value = {
                "answer": "This is a test answer with custom retrieval config",
                "sources": [
                    {
                        "source": "test_source.txt",
                        "score": 0.95,
                        "content_preview": "Test content"
                    }
                ],
                "query_type": "general_aws",
                "retrieval_config": {
                    "retriever_type": "hybrid",
                    "use_mmr": True,
                    "mmr_lambda": 0.7
                }
            }
            mock_get_query_engine.return_value = mock_engine
            
            # Make the request
            response = self.client.post(
                "/query/advanced",
                json={
                    "question": "What is AWS EC2?",
                    "retrieval_config": {
                        "retriever_type": "hybrid",
                        "use_mmr": True,
                        "mmr_lambda": 0.7
                    }
                }
            )
            
            # Check the response
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json()["answer"], "This is a test answer with custom retrieval config")
            
            # Check that the query engine was called with the correct parameters
            mock_engine.query_advanced.assert_called_once_with(
                question="What is AWS EC2?",
                retrieval_config={
                    "retriever_type": "hybrid",
                    "use_mmr": True,
                    "mmr_lambda": 0.7
                }
            )
    
    def test_error_handling(self):
        """Test the endpoint's error handling."""
        # Mock the QueryEngine.query_advanced method to raise an exception
        with patch('Backend.main.get_query_engine') as mock_get_query_engine:
            # Create a mock query engine
            mock_engine = MagicMock()
            mock_engine.query_advanced.side_effect = Exception("Test error")
            mock_get_query_engine.return_value = mock_engine
            
            # Make the request
            response = self.client.post(
                "/query/advanced",
                json={"question": "What is AWS EC2?"}
            )
            
            # Check the response
            self.assertEqual(response.status_code, 500)
            self.assertIn("Test error", response.json()["detail"])

if __name__ == "__main__":
    unittest.main()