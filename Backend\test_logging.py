#!/usr/bin/env python
"""
Test script for logging functionality in the RAG application.
This script verifies that the logger is properly configured and works as expected.
"""

import logging
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def setup_logging(log_level=None):
    """
    Set up logging with the specified log level.
    
    Args:
        log_level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    # Get log level from environment variable or parameter
    level_name = log_level or os.getenv("LOG_LEVEL", "INFO")
    level = getattr(logging, level_name.upper(), logging.INFO)
    
    # Configure logging
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("test_logging.log")
        ]
    )
    
    return logging.getLogger(__name__)

def test_logging_levels(logger):
    """
    Test all logging levels.
    
    Args:
        logger: Logger instance
    """
    logger.debug("This is a DEBUG message")
    logger.info("This is an INFO message")
    logger.warning("This is a WARNING message")
    logger.error("This is an ERROR message")
    logger.critical("This is a CRITICAL message")

def test_exception_logging(logger):
    """
    Test exception logging.
    
    Args:
        logger: Logger instance
    """
    try:
        # Deliberately cause an exception
        x = 1 / 0
    except Exception as e:
        logger.error(f"Caught an exception: {e}")
        logger.exception("Exception details:")

def test_structured_logging(logger):
    """
    Test structured logging with extra fields.
    
    Args:
        logger: Logger instance
    """
    logger.info("Structured log message", extra={
        "user_id": "test_user",
        "action": "login",
        "status": "success"
    })

def simulate_endpoint_call(logger, endpoint, success=True):
    """
    Simulate an endpoint call and log it.
    
    Args:
        logger: Logger instance
        endpoint: Endpoint name
        success: Whether the call was successful
    """
    logger.info(f"Request received for endpoint: {endpoint}")
    
    if success:
        logger.info(f"Request to {endpoint} processed successfully")
        return {"status": "success", "message": f"{endpoint} request successful"}
    else:
        error_message = f"Error processing request to {endpoint}"
        logger.error(error_message)
        return {"status": "error", "message": error_message}

def main():
    """Main function to run the logging tests."""
    # Set up logging
    logger = setup_logging()
    logger.info("Starting logging tests")
    
    # Test basic logging levels
    logger.info("Testing basic logging levels")
    test_logging_levels(logger)
    
    # Test exception logging
    logger.info("Testing exception logging")
    test_exception_logging(logger)
    
    # Test structured logging
    logger.info("Testing structured logging")
    test_structured_logging(logger)
    
    # Simulate endpoint calls
    logger.info("Simulating endpoint calls")
    simulate_endpoint_call(logger, "/query/advanced")
    simulate_endpoint_call(logger, "/query/image", success=False)
    simulate_endpoint_call(logger, "/analyze/image")
    simulate_endpoint_call(logger, "/analyze/image/openrouter", success=False)
    
    logger.info("Logging tests completed")
    
    return 0

if __name__ == "__main__":
    exit(main())