#!/usr/bin/env python3
"""
Test the exact same image that was uploaded through the frontend.
"""

import requests
import time

def test_with_frontend_image():
    """Test with the AWS_Architecture_1.png image that was uploaded in the frontend."""
    print("Testing with the AWS_Architecture_1.png image...")
    
    # Try to find the image file
    image_paths = [
        "AWS_Architecture_1.png",
        "Frontend/AWS_Architecture_1.png",
        "Backend/temp_images/AWS_Architecture_1.png",
        "Backend/test_data/AWS_Architecture_1.png",
        "Backend/test_data/test_diagram.png",
        "Backend/test_data/diagram_image.png"
    ]
    
    image_bytes = None
    used_path = None
    
    for path in image_paths:
        try:
            with open(path, 'rb') as f:
                image_bytes = f.read()
                used_path = path
                print(f"✅ Found image at: {path}")
                break
        except FileNotFoundError:
            continue
    
    if not image_bytes:
        print("❌ Could not find AWS_Architecture_1.png image file")
        print("Available files in current directory:")
        import os
        for file in os.listdir('.'):
            if file.endswith(('.png', '.jpg', '.jpeg')):
                print(f"  - {file}")
        return
    
    print(f"Image size: {len(image_bytes)} bytes")
    
    # Test the Gemini endpoint
    files = {'file': ('AWS_Architecture_1.png', image_bytes, 'image/png')}
    
    try:
        print("Sending request to Gemini endpoint...")
        response = requests.post(
            'http://localhost:8888/analyze/image/gemini',
            files=files,
            timeout=60
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response keys: {list(result.keys())}")
            print(f"Status: {result.get('status')}")
            
            analysis = result.get('analysis', '')
            print(f"Analysis type: {type(analysis)}")
            print(f"Analysis length: {len(analysis)}")
            print(f"Analysis empty: {not analysis}")
            
            if analysis:
                print(f"Analysis preview: {analysis[:200]}...")
            else:
                print("❌ Analysis is empty!")
                print(f"Full response: {result}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def wait_for_backend():
    """Wait for backend to be ready."""
    print("Waiting for backend to be ready...")
    for i in range(30):
        try:
            response = requests.get('http://localhost:8888/health', timeout=5)
            if response.status_code == 200:
                print("✅ Backend is ready!")
                return True
        except:
            pass
        time.sleep(1)
        print(f"  Waiting... ({i+1}/30)")
    
    print("❌ Backend not ready after 30 seconds")
    return False

def main():
    if wait_for_backend():
        test_with_frontend_image()
    else:
        print("Cannot test - backend not available")

if __name__ == "__main__":
    main()
