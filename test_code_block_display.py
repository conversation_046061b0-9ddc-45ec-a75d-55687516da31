#!/usr/bin/env python3
"""
Test displaying analysis in a code block format.
"""

import chainlit as cl

@cl.on_message
async def main(message: cl.Message):
    """Handle incoming messages."""
    
    if message.content.lower() == "test":
        # Sample analysis with markdown (similar to what <PERSON> returns)
        analysis = """Of course. Here is a detailed analysis of the provided image.

### Overall Description

The image is a very simple, minimalist architecture diagram. It is not a screenshot of an error. The purpose of the diagram, as indicated by the title "AWS Test," is likely to represent a basic test setup or a foundational concept within the Amazon Web Services (AWS) cloud platform.

### Component Analysis

The diagram consists of two primary elements:

1.  **Title: "AWS Test"**
    *   **AWS:** This stands for Amazon Web Services, the world's leading cloud computing platform.
    *   **Test:** This word suggests that the architecture is not for a production environment.

2.  **Component Box: "EC2"**
    *   **Representation:** The blue rectangle is a standard way to represent a service.
    *   **EC2:** This is an acronym for **Amazon Elastic Compute Cloud**."""
        
        # Test different display methods
        
        # Method 1: Direct markdown (might conflict)
        await cl.Message(content=f"**Method 1 - Direct Markdown:**\n\n{analysis}").send()
        
        # Method 2: Code block (should work)
        code_block_content = f"""**Method 2 - Code Block:**

```
{analysis}
```"""
        await cl.Message(content=code_block_content).send()
        
        # Method 3: Escaped markdown
        analysis_escaped = analysis.replace('*', '\\*').replace('#', '\\#')
        await cl.Message(content=f"**Method 3 - Escaped:**\n\n{analysis_escaped}").send()
        
    else:
        await cl.Message(content="Type 'test' to see different display methods.").send()

if __name__ == "__main__":
    cl.run()
