import os
from typing import List
from dotenv import load_dotenv
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_aws import BedrockEmbeddings
from langchain.schema import Document
from aws_utils import AWSClient, summarize_image_with_bedrock
from vectorstore_utils import build_vectorstore, load_vectorstore
from embedding_utils import MultiModalEmbeddings
import glob
import uuid
import logging
import base64
from langchain_experimental.text_splitter import SemanticChunker as SemanticTextSplitter
from image_analysis.processor import ImageProcessor
from image_analysis.storage import TemporaryImageStore, AnalysisResultStore
from io import BytesIO
from PIL import Image

# Get logger
logger = logging.getLogger(__name__)

load_dotenv()

class DocumentWithID(Document):
    @property
    def id(self):
        return self.metadata.get('id')

class DocumentIngester:
    def __init__(self):
        self.aws_client = AWSClient()
        self.text_embeddings = BedrockEmbeddings(
            region_name=os.getenv('AWS_REGION'),
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            model_id=os.getenv('BEDROCK_EMBEDDING_MODEL_ID')
        )
        self.image_embeddings = MultiModalEmbeddings()
        self.text_splitter = SemanticTextSplitter(self.text_embeddings)
        
        self.vector_store = None
        # Initialize image analysis components
        self.image_store = TemporaryImageStore()
        self.result_store = AnalysisResultStore()
        self.image_processor = ImageProcessor(self.image_store, self.result_store)
        # Initialize image analysis components
        self.image_store = TemporaryImageStore()
        self.result_store = AnalysisResultStore()
        self.image_processor = ImageProcessor(self.image_store, self.result_store)

    def process_s3_documents(self) -> dict:
        try:
            bucket_name = os.getenv('S3_BUCKET_NAME')
            if not bucket_name:
                logger.error("S3_BUCKET_NAME environment variable is not set")
                return {"status": "error", "message": "S3_BUCKET_NAME environment variable is not set", "chunks": "0"}
                
            logger.info(f"Listing files in S3 bucket: {bucket_name}")
            files = self.aws_client.list_s3_files(bucket_name)
            
            if not files:
                logger.info("No documents found in S3 bucket")
                return {"status": "success", "message": "No documents found in S3 bucket", "chunks": "0"}
            
            logger.info(f"Found {len(files)} files in S3 bucket")
            documents: List[Document] = []
            image_documents: List[Document] = []
            
            for file_key in files:
                logger.info(f"Processing file: {file_key}")
                try:
                    content_dict = self.aws_client.read_s3_file(bucket_name, file_key)
                    
                    if not content_dict:
                        logger.warning(f"No content extracted from file: {file_key}")
                        continue
                except Exception as e:
                    logger.error(f"Error processing file {file_key}: {e}")
                    continue
            
                text_content = content_dict.get("text", "")
                images = content_dict.get("images", [])
                
                if text_content and text_content.strip():
                    short_source = os.path.basename(file_key)
                    file_extension = os.path.splitext(file_key)[1].lower()
                    
                    # Add metadata to help with source attribution
                    metadata = {
                        "source": short_source,
                        "file_type": file_extension.replace('.', ''),
                        "file_path": file_key
                    }
                    
                    doc = Document(page_content=text_content.strip(), metadata=metadata)
                    documents.append(doc)
                
                # Process any extracted images
                for img_data in images:
                    # Generate a detailed summary of the image
                    image_summary = summarize_image_with_bedrock(base64.b64decode(img_data["base64"]))
                    
                    img_metadata = {
                        "source": img_data["id"],
                        "file_type": "image",
                        "image_format": img_data.get("format", "PNG"),
                        "file_path": file_key,
                        "is_image": True,
                        "base64_image": img_data["base64"],
                        "image_caption": img_data["metadata"].get("caption", "Image"),
                        "page_number": img_data["metadata"].get("page_number"),
                        "image_summary": image_summary,
                    }
                    
                    # Create a document for the image summary
                    summary_doc_content = f"Image Caption: {img_metadata['image_caption']}\n\nImage Summary:\n{image_summary}"
                    summary_doc = Document(page_content=summary_doc_content, metadata=img_metadata)
                    image_documents.append(summary_doc)

                    # Analyze the image and add the analysis to the metadata
                    try:
                        # The ImageProcessor expects a file path, so we save the image temporarily
                        image = Image.open(BytesIO(base64.b64decode(img_data["base64"])))
                        temp_image_path = f"temp_{img_data['id']}.png"
                        image.save(temp_image_path)
                        
                        text_content = self.image_processor.extract_text(temp_image_path)
                        image_type = self.image_processor.classify_image_type(temp_image_path, text_content)
                        analysis = self.image_processor.analyze_image(temp_image_path, text_content, image_type)
                        
                        # Add analysis to metadata
                        img_metadata["analysis"] = analysis.dict()

                        # Clean up temporary image file
                        os.remove(temp_image_path)
                    except Exception as e:
                        logger.error(f"Error analyzing image {img_data['id']}: {e}")

                    # Create a document for the raw image vector
                    # The content is empty as the vector represents the image itself
                    image_vector_doc = Document(page_content="", metadata=img_metadata)
                    image_documents.append(image_vector_doc)
            
            all_documents = documents + image_documents
            
            if not all_documents:
                logger.warning("No valid documents found to process")
                return {"status": "success", "message": "No valid documents found to process", "chunks": "0"}
            
            # Process documents into chunks
            logger.info(f"Splitting {len(documents)} text documents and {len(image_documents)} images into chunks...")
            
            try:
                # Split and enrich each document with contextual headers
                enriched_chunks = self.text_splitter.split_documents(all_documents)
    
                # Add unique IDs to each chunk
                for chunk in enriched_chunks:
                    chunk.metadata['id'] = str(uuid.uuid4())
                    # Keep track of chunk position for better context
                    if 'chunk_id' not in chunk.metadata:
                        chunk.metadata['chunk_id'] = str(uuid.uuid4())
    
                chunks_with_id = [DocumentWithID(page_content=chunk.page_content, metadata=chunk.metadata) for chunk in enriched_chunks]
    
                logger.info(f"Building vector store with {len(chunks_with_id)} chunks...")
                self.vector_store = build_vectorstore(chunks_with_id)
    
                logger.info(f"Successfully processed {len(documents)} text documents and {len(image_documents)} images into {len(enriched_chunks)} chunks")
                return {
                    "status": "success",
                    "message": f"Processed {len(documents)} text documents and {len(image_documents)} images into {len(enriched_chunks)} chunks",
                    "chunks": str(len(enriched_chunks))
                }
            except Exception as e:
                logger.error(f"Error processing documents: {e}")
                import traceback
                logger.error(f"Stack trace: {traceback.format_exc()}")
                return {
                    "status": "error",
                    "message": f"Error processing documents: {e}",
                    "chunks": "0"
                }
        except Exception as e:
            logger.error(f"Error in process_s3_documents: {e}")
            import traceback
            logger.error(f"Stack trace: {traceback.format_exc()}")
            return {
                "status": "error",
                "message": f"Error in document ingestion: {e}",
                "chunks": "0"
            }

    def get_vector_store(self):
        if not self.vector_store:
            self.vector_store = load_vectorstore()
        return self.vector_store

    def get_vector_store_client(self):
        from vectorstore_utils import load_vectorstore_client
        return load_vectorstore_client()

if __name__ == "__main__":
    # Configure logging for script execution
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    
    if os.getenv("INGEST_S3_DOCUMENTS", "false").lower() == "true":
        logger.info("--- S3 Document Ingestion Started ---")
        try:
            ingester = DocumentIngester()
            s3_result = ingester.process_s3_documents()
            logger.info(f"Ingestion result: {s3_result}")
            
            if s3_result.get("status") == "success":
                logger.info("Document ingestion completed successfully")
            else:
                logger.error(f"Document ingestion failed: {s3_result.get('message')}")
        except Exception as e:
            import traceback
            logger.error(f"Unhandled exception during document ingestion: {e}")
            logger.error(f"Stack trace: {traceback.format_exc()}")
    else:
        logger.info("S3 document ingestion is skipped. Set INGEST_S3_DOCUMENTS=true to enable it.")
    