#!/usr/bin/env python3
"""
Inspect the exact analysis content to see if there are any problematic characters.
"""

import requests
import json
from PIL import Image, ImageDraw
from io import BytesIO

def create_test_image():
    """Create a test image."""
    img = Image.new('RGB', (200, 150), color='white')
    draw = ImageDraw.Draw(img)
    draw.text((20, 20), "AWS Test", fill='black')
    draw.rectangle([20, 50, 80, 90], outline='blue', width=2)
    draw.text((25, 65), "EC2", fill='blue')
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def inspect_analysis():
    """Get analysis and inspect its content."""
    print("Getting analysis from backend...")
    
    image_bytes = create_test_image()
    files = {'file': ('test.png', image_bytes, 'image/png')}
    
    try:
        response = requests.post(
            'http://localhost:8888/analyze/image/gemini',
            files=files,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            analysis = result.get('analysis', '')
            
            print(f"Analysis type: {type(analysis)}")
            print(f"Analysis length: {len(analysis)}")
            print(f"Analysis is empty: {not analysis}")
            print(f"Analysis is string: {isinstance(analysis, str)}")
            
            if analysis:
                print("\nFirst 200 characters:")
                print(repr(analysis[:200]))
                
                print("\nLast 200 characters:")
                print(repr(analysis[-200:]))
                
                print("\nCharacter analysis:")
                print(f"Contains \\r: {chr(13) in analysis}")
                print(f"Contains \\n: {chr(10) in analysis}")
                print(f"Contains \\t: {chr(9) in analysis}")
                print(f"Contains special chars: {any(ord(c) > 127 for c in analysis)}")
                
                # Check for problematic markdown characters
                problematic_chars = ['*', '#', '`', '[', ']', '(', ')', '_', '~']
                for char in problematic_chars:
                    count = analysis.count(char)
                    if count > 0:
                        print(f"Contains '{char}': {count} times")
                
                print("\nFull analysis:")
                print("=" * 50)
                print(analysis)
                print("=" * 50)
                
                # Test what the frontend would do
                analysis_clean = analysis.replace('\r\n', '\n').replace('\r', '\n')
                result_content = f"Analysis Results:\n\n{analysis_clean}\n\nModel: gemini-2.5-pro"
                
                print(f"\nFinal content length: {len(result_content)}")
                print("Final content preview:")
                print(result_content[:300] + "..." if len(result_content) > 300 else result_content)
                
            else:
                print("❌ Analysis is empty!")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    inspect_analysis()
