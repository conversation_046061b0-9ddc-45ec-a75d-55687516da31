<mxfile host="app.diagrams.net" agent="5.0" version="24.7.17">
  <diagram name="RAG Architecture" id="RAG-ARCH-002">
    <mxGraphModel dx="1200" dy="800" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <!-- Knowledge Base Creation Section -->
        <mxCell id="kb_bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="900" height="220" as="geometry"/>
        </mxCell>
        <mxCell id="kb_label" value="Knowledge Base Creation" style="text;html=1;strokeColor=none;fillColor=none;fontSize=18;fontStyle=1;fontColor=#1f1f1f;" vertex="1" parent="1">
          <mxGeometry x="60" y="50" width="300" height="30" as="geometry"/>
        </mxCell>
        
        <!-- User Upload -->
        <mxCell id="user_upload" value="User&#xa;(Upload)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#232F3D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.user;" vertex="1" parent="1">
          <mxGeometry x="80" y="120" width="60" height="60" as="geometry"/>
        </mxCell>
        
        <!-- S3 -->
        <mxCell id="s3" value="S3&#xa;(AWS S3 Bucket)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8FD2;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.s3;" vertex="1" parent="1">
          <mxGeometry x="200" y="120" width="80" height="80" as="geometry"/>
        </mxCell>
        
        <!-- Unstructured.io -->
        <mxCell id="unstructured" value="Unstructured.io&#xa;(Doc Parser)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#ED7100;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.ec2;" vertex="1" parent="1">
          <mxGeometry x="340" y="120" width="80" height="80" as="geometry"/>
        </mxCell>
        
        <!-- Bedrock Embedding -->
        <mxCell id="bedrock_embed" value="Bedrock&#xa;(Embedding Model)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#F78E04;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.bedrock;" vertex="1" parent="1">
          <mxGeometry x="480" y="120" width="80" height="80" as="geometry"/>
        </mxCell>
        
        <!-- Qdrant -->
        <mxCell id="qdrant" value="Qdrant&#xa;(Local VectorDB)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3334B9;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.rds;" vertex="1" parent="1">
          <mxGeometry x="620" y="120" width="80" height="80" as="geometry"/>
        </mxCell>
        
        <!-- Knowledge Base Creation Arrows -->
        <mxCell id="arrow1" value="1. Upload documents" style="endArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=2;fontColor=#000000;" edge="1" parent="1" source="user_upload" target="s3">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="arrow2" value="2. Parse documents" style="endArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=2;fontColor=#000000;" edge="1" parent="1" source="s3" target="unstructured">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="arrow3" value="3. Create embeddings" style="endArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=2;fontColor=#000000;" edge="1" parent="1" source="unstructured" target="bedrock_embed">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="arrow4" value="4. Store embeddings" style="endArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=2;fontColor=#000000;" edge="1" parent="1" source="bedrock_embed" target="qdrant">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        
        <!-- RAG Pipeline Section -->
        <mxCell id="rag_bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="300" width="1040" height="260" as="geometry"/>
        </mxCell>
        <mxCell id="rag_label" value="RAG Pipeline" style="text;html=1;strokeColor=none;fillColor=none;fontSize=18;fontStyle=1;fontColor=#1f1f1f;" vertex="1" parent="1">
          <mxGeometry x="60" y="310" width="300" height="30" as="geometry"/>
        </mxCell>
        
        <!-- User Query -->
        <mxCell id="user_query" value="User&#xa;(Query)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#232F3D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.user;" vertex="1" parent="1">
          <mxGeometry x="80" y="400" width="60" height="60" as="geometry"/>
        </mxCell>
        
        <!-- Chainlit UI -->
        <mxCell id="chainlit" value="Chainlit UI" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#8C4FFF;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.cloudfront;" vertex="1" parent="1">
          <mxGeometry x="200" y="400" width="80" height="80" as="geometry"/>
        </mxCell>
        
        <!-- FastAPI Backend -->
        <mxCell id="backend" value="FastAPI Backend" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#ED7100;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.lambda;" vertex="1" parent="1">
          <mxGeometry x="340" y="400" width="80" height="80" as="geometry"/>
        </mxCell>
        
        <!-- Bedrock Query Embedding -->
        <mxCell id="bedrock_query" value="Bedrock&#xa;(Embedding Model)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#F78E04;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.bedrock;" vertex="1" parent="1">
          <mxGeometry x="480" y="400" width="80" height="80" as="geometry"/>
        </mxCell>
        
        <!-- Qdrant Vector Search -->
        <mxCell id="qdrant2" value="Qdrant&#xa;(Local VectorDB)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3334B9;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.rds;" vertex="1" parent="1">
          <mxGeometry x="620" y="400" width="80" height="80" as="geometry"/>
        </mxCell>
        
        <!-- Bedrock LLM -->
        <mxCell id="bedrock_llm" value="Bedrock&#xa;(LLM)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#F78E04;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.bedrock;" vertex="1" parent="1">
          <mxGeometry x="760" y="400" width="80" height="80" as="geometry"/>
        </mxCell>
        
        <!-- RAG Pipeline Arrows -->
        <mxCell id="arrow5" value="1. Query" style="endArrow=classic;html=1;rounded=0;strokeColor=#1f1f1f;strokeWidth=2;fontColor=#1f1f1f;" edge="1" parent="1" source="user_query" target="chainlit">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="arrow6" value="2. Process" style="endArrow=classic;html=1;rounded=0;strokeColor=#1f1f1f;strokeWidth=2;fontColor=#1f1f1f;" edge="1" parent="1" source="chainlit" target="backend">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="arrow7" value="3. Embed query" style="endArrow=classic;html=1;rounded=0;strokeColor=#1f1f1f;strokeWidth=2;fontColor=#1f1f1f;" edge="1" parent="1" source="backend" target="bedrock_query">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="arrow8" value="4. Vector search" style="endArrow=classic;html=1;rounded=0;strokeColor=#1f1f1f;strokeWidth=2;fontColor=#1f1f1f;" edge="1" parent="1" source="bedrock_query" target="qdrant2">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="arrow9" value="5. Retrieve context" style="endArrow=classic;html=1;rounded=0;strokeColor=#1f1f1f;strokeWidth=2;fontColor=#1f1f1f;" edge="1" parent="1" source="qdrant2" target="backend">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="600" y="380" as="targetPoint"/>
            <Array as="points">
              <mxPoint x="660" y="380"/>
              <mxPoint x="380" y="380"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow10" value="6. Query + context" style="endArrow=classic;html=1;rounded=0;strokeColor=#1f1f1f;strokeWidth=2;fontColor=#1f1f1f;" edge="1" parent="1" source="backend" target="bedrock_llm">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="380" y="520"/>
              <mxPoint x="800" y="520"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow11" value="7. Response" style="endArrow=classic;html=1;rounded=0;strokeColor=#1f1f1f;strokeWidth=2;fontColor=#1f1f1f;" edge="1" parent="1" source="bedrock_llm" target="chainlit">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="800" y="360"/>
              <mxPoint x="240" y="360"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow12" value="8. Display result" style="endArrow=classic;html=1;rounded=0;strokeColor=#1f1f1f;strokeWidth=2;fontColor=#1f1f1f;" edge="1" parent="1" source="chainlit" target="user_query">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="240" y="340"/>
              <mxPoint x="110" y="340"/>
            </Array>
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>