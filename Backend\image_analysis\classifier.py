"""
Image type classifier for distinguishing between diagram and error screenshots.
"""
import os
import logging
import numpy as np
from typing import Dict, Tuple, List, Optional
from PIL import Image, ImageStat
import cv2
from enum import Enum

from .models import ImageType
from .ocr import OCRService, OCRPreprocessing

# Configure logging
logger = logging.getLogger(__name__)


class ImageFeature(str, Enum):
    """Enum for image features used in classification."""
    TEXT_DENSITY = "text_density"
    COLOR_DIVERSITY = "color_diversity"
    EDGE_DENSITY = "edge_density"
    SHAPE_PRESENCE = "shape_presence"
    ERROR_KEYWORDS = "error_keywords"
    DIAGRAM_KEYWORDS = "diagram_keywords"
    UI_ELEMENTS = "ui_elements"


class ImageTypeClassifier:
    """
    Service for classifying images as architecture diagrams or error screenshots.
    """
    
    # Keywords that suggest an error screenshot
    ERROR_KEYWORDS = [
        "error", "exception", "failed", "warning", "alert", "critical",
        "fatal", "crash", "bug", "issue", "problem", "failure", "fault",
        "invalid", "denied", "rejected", "unauthorized", "forbidden",
        "not found", "timeout", "unavailable", "404", "500", "403"
    ]
    
    # Keywords that suggest an architecture diagram
    DIAGRAM_KEYWORDS = [
        "diagram", "architecture", "flow", "component", "service", "system",
        "database", "server", "client", "api", "interface", "module", "class",
        "network", "cloud", "storage", "compute", "function", "container",
        "microservice", "vpc", "subnet", "cluster", "node", "instance"
    ]
    
    def __init__(self, ocr_service: Optional[OCRService] = None):
        """
        Initialize the image type classifier.
        
        Args:
            ocr_service: OCR service for text extraction (optional)
        """
        self.ocr_service = ocr_service
    
    def classify_image(self, image_path: str, text_content: Optional[str] = None) -> Tuple[ImageType, float, Dict[str, float]]:
        """
        Classify an image as architecture diagram or error screenshot.
        
        Args:
            image_path: Path to the image file
            text_content: Pre-extracted text content (optional)
            
        Returns:
            Tuple containing:
            - ImageType: Classification result
            - float: Confidence score (0.0 to 1.0)
            - Dict[str, float]: Feature scores
        """
        try:
            # Extract features from the image
            features = self._extract_features(image_path, text_content)
            
            # Calculate scores for each image type
            architecture_score = self._calculate_architecture_score(features)
            error_score = self._calculate_error_score(features)
            
            # Determine the image type based on scores
            if architecture_score > error_score:
                image_type = ImageType.ARCHITECTURE
                confidence = min(0.5 + (architecture_score - error_score) / 2, 0.99)
            elif error_score > architecture_score:
                image_type = ImageType.ERROR
                confidence = min(0.5 + (error_score - architecture_score) / 2, 0.99)
            else:
                image_type = ImageType.UNKNOWN
                confidence = 0.5
            
            return image_type, confidence, features
            
        except Exception as e:
            logger.error(f"Error classifying image: {str(e)}")
            return ImageType.UNKNOWN, 0.5, {}
    
    def _extract_features(self, image_path: str, text_content: Optional[str] = None) -> Dict[str, float]:
        """
        Extract features from an image for classification.
        
        Args:
            image_path: Path to the image file
            text_content: Pre-extracted text content (optional)
            
        Returns:
            Dict[str, float]: Feature scores
        """
        features = {}
        
        try:
            # Open the image
            img = Image.open(image_path)
            
            # Convert to OpenCV format for some processing
            cv_img = cv2.imread(image_path)
            
            # Extract text if not provided
            if text_content is None and self.ocr_service:
                text_content = self.ocr_service.extract_text(
                    image_path=image_path,
                    preprocessing=OCRPreprocessing.ALL
                )
            
            # Calculate text-based features
            if text_content:
                features[ImageFeature.TEXT_DENSITY] = self._calculate_text_density(text_content, img.size)
                features[ImageFeature.ERROR_KEYWORDS] = self._calculate_keyword_presence(text_content, self.ERROR_KEYWORDS)
                features[ImageFeature.DIAGRAM_KEYWORDS] = self._calculate_keyword_presence(text_content, self.DIAGRAM_KEYWORDS)
            else:
                features[ImageFeature.TEXT_DENSITY] = 0.0
                features[ImageFeature.ERROR_KEYWORDS] = 0.0
                features[ImageFeature.DIAGRAM_KEYWORDS] = 0.0
            
            # Calculate image-based features
            features[ImageFeature.COLOR_DIVERSITY] = self._calculate_color_diversity(img)
            features[ImageFeature.EDGE_DENSITY] = self._calculate_edge_density(cv_img)
            features[ImageFeature.SHAPE_PRESENCE] = self._detect_shapes(cv_img)
            features[ImageFeature.UI_ELEMENTS] = self._detect_ui_elements(cv_img)
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting image features: {str(e)}")
            return {
                ImageFeature.TEXT_DENSITY: 0.0,
                ImageFeature.COLOR_DIVERSITY: 0.0,
                ImageFeature.EDGE_DENSITY: 0.0,
                ImageFeature.SHAPE_PRESENCE: 0.0,
                ImageFeature.ERROR_KEYWORDS: 0.0,
                ImageFeature.DIAGRAM_KEYWORDS: 0.0,
                ImageFeature.UI_ELEMENTS: 0.0
            }
    
    def _calculate_text_density(self, text_content: str, image_size: Tuple[int, int]) -> float:
        """
        Calculate the density of text in the image.
        
        Args:
            text_content: Extracted text from the image
            image_size: Size of the image (width, height)
            
        Returns:
            float: Text density score (0.0 to 1.0)
        """
        if not text_content:
            return 0.0
        
        # Calculate text density as characters per pixel
        char_count = len(text_content)
        pixel_count = image_size[0] * image_size[1]
        
        # Normalize to a 0-1 scale (empirically determined thresholds)
        density = char_count / pixel_count * 10000  # Scale factor for readability
        normalized_density = min(density / 5.0, 1.0)  # Assuming 5.0 is a high density
        
        return normalized_density
    
    def _calculate_keyword_presence(self, text_content: str, keywords: List[str]) -> float:
        """
        Calculate the presence of specific keywords in the text.
        
        Args:
            text_content: Extracted text from the image
            keywords: List of keywords to search for
            
        Returns:
            float: Keyword presence score (0.0 to 1.0)
        """
        if not text_content:
            return 0.0
        
        # Convert to lowercase for case-insensitive matching
        text_lower = text_content.lower()
        
        # Count occurrences of keywords
        keyword_count = sum(1 for keyword in keywords if keyword.lower() in text_lower)
        
        # Normalize to a 0-1 scale
        normalized_count = min(keyword_count / 5.0, 1.0)  # Assuming 5 keywords is a strong signal
        
        return normalized_count
    
    def _calculate_color_diversity(self, img: Image.Image) -> float:
        """
        Calculate the diversity of colors in the image.
        
        Args:
            img: PIL Image object
            
        Returns:
            float: Color diversity score (0.0 to 1.0)
        """
        try:
            # Convert to RGB if not already
            if img.mode != "RGB":
                img = img.convert("RGB")
            
            # Resize to a smaller image to reduce computation
            img_small = img.resize((100, 100), Image.LANCZOS)
            
            # Get image statistics
            stat = ImageStat.Stat(img_small)
            
            # Calculate color variance across channels
            variance = sum(stat.var) / 3
            
            # Normalize to a 0-1 scale (empirically determined thresholds)
            # Higher variance typically indicates more diverse colors (diagrams)
            # Lower variance typically indicates more uniform colors (error screens)
            normalized_variance = min(variance / 5000.0, 1.0)
            
            return normalized_variance
            
        except Exception as e:
            logger.error(f"Error calculating color diversity: {str(e)}")
            return 0.5  # Default to neutral
    
    def _calculate_edge_density(self, cv_img: np.ndarray) -> float:
        """
        Calculate the density of edges in the image.
        
        Args:
            cv_img: OpenCV image array
            
        Returns:
            float: Edge density score (0.0 to 1.0)
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
            
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # Detect edges using Canny edge detector
            edges = cv2.Canny(blurred, 50, 150)
            
            # Calculate edge density as percentage of edge pixels
            edge_count = np.count_nonzero(edges)
            total_pixels = edges.shape[0] * edges.shape[1]
            edge_density = edge_count / total_pixels
            
            # Normalize to a 0-1 scale (empirically determined thresholds)
            # Higher edge density typically indicates diagrams
            normalized_density = min(edge_density * 5.0, 1.0)  # Scale factor determined empirically
            
            return normalized_density
            
        except Exception as e:
            logger.error(f"Error calculating edge density: {str(e)}")
            return 0.5  # Default to neutral
    
    def _detect_shapes(self, cv_img: np.ndarray) -> float:
        """
        Detect the presence of geometric shapes in the image.
        
        Args:
            cv_img: OpenCV image array
            
        Returns:
            float: Shape presence score (0.0 to 1.0)
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
            
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # Apply threshold
            _, thresh = cv2.threshold(blurred, 127, 255, cv2.THRESH_BINARY)
            
            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter out very small contours
            min_contour_area = (cv_img.shape[0] * cv_img.shape[1]) * 0.001  # 0.1% of image area
            significant_contours = [c for c in contours if cv2.contourArea(c) > min_contour_area]
            
            # Count shapes (rectangles, circles, triangles)
            shape_count = 0
            for contour in significant_contours:
                # Approximate contour to polygon
                perimeter = cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, 0.04 * perimeter, True)
                
                # Count vertices
                vertices = len(approx)
                
                # Classify shapes based on vertices
                if vertices == 3:  # Triangle
                    shape_count += 1
                elif vertices == 4:  # Rectangle/Square
                    shape_count += 1
                elif vertices > 4 and vertices < 10:  # Polygon
                    shape_count += 1
                else:  # Potential circle or complex shape
                    # Check circularity
                    area = cv2.contourArea(contour)
                    if area > 0:
                        circularity = 4 * np.pi * area / (perimeter * perimeter)
                        if circularity > 0.7:  # Close to a circle
                            shape_count += 1
            
            # Normalize to a 0-1 scale
            normalized_count = min(shape_count / 10.0, 1.0)  # Assuming 10 shapes is a strong signal
            
            return normalized_count
            
        except Exception as e:
            logger.error(f"Error detecting shapes: {str(e)}")
            return 0.5  # Default to neutral
    
    def _detect_ui_elements(self, cv_img: np.ndarray) -> float:
        """
        Detect the presence of UI elements like buttons, dialog boxes, etc.
        
        Args:
            cv_img: OpenCV image array
            
        Returns:
            float: UI element presence score (0.0 to 1.0)
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
            
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # Apply adaptive threshold to detect UI elements
            thresh = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
            )
            
            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter contours to find potential UI elements (buttons, text fields, etc.)
            ui_element_count = 0
            for contour in contours:
                # Get bounding rectangle
                x, y, w, h = cv2.boundingRect(contour)
                
                # Filter by aspect ratio and size
                aspect_ratio = w / h if h > 0 else 0
                area = w * h
                min_area = (cv_img.shape[0] * cv_img.shape[1]) * 0.001  # 0.1% of image area
                max_area = (cv_img.shape[0] * cv_img.shape[1]) * 0.1  # 10% of image area
                
                # Typical UI elements have specific aspect ratios and sizes
                if (0.2 < aspect_ratio < 5) and (min_area < area < max_area):
                    # Check if it's rectangular (UI elements often are)
                    perimeter = cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, 0.04 * perimeter, True)
                    if len(approx) == 4:  # Rectangle
                        ui_element_count += 1
            
            # Normalize to a 0-1 scale
            normalized_count = min(ui_element_count / 5.0, 1.0)  # Assuming 5 UI elements is a strong signal
            
            return normalized_count
            
        except Exception as e:
            logger.error(f"Error detecting UI elements: {str(e)}")
            return 0.5  # Default to neutral
    
    def _calculate_architecture_score(self, features: Dict[str, float]) -> float:
        """
        Calculate the architecture diagram score based on features.
        
        Args:
            features: Dictionary of feature scores
            
        Returns:
            float: Architecture score (0.0 to 1.0)
        """
        # Weights for each feature (empirically determined)
        weights = {
            ImageFeature.TEXT_DENSITY: 0.1,
            ImageFeature.COLOR_DIVERSITY: 0.2,
            ImageFeature.EDGE_DENSITY: 0.25,
            ImageFeature.SHAPE_PRESENCE: 0.25,
            ImageFeature.ERROR_KEYWORDS: -0.3,  # Negative weight for error keywords
            ImageFeature.DIAGRAM_KEYWORDS: 0.3,
            ImageFeature.UI_ELEMENTS: -0.1  # Negative weight for UI elements
        }
        
        # Calculate weighted sum
        score = 0.5  # Start with neutral score
        for feature, value in features.items():
            if feature in weights:
                score += value * weights[feature]
        
        # Ensure score is in range [0, 1]
        return max(0.0, min(score, 1.0))
    
    def _calculate_error_score(self, features: Dict[str, float]) -> float:
        """
        Calculate the error screenshot score based on features.
        
        Args:
            features: Dictionary of feature scores
            
        Returns:
            float: Error score (0.0 to 1.0)
        """
        # Weights for each feature (empirically determined)
        weights = {
            ImageFeature.TEXT_DENSITY: 0.2,
            ImageFeature.COLOR_DIVERSITY: -0.1,  # Negative weight for color diversity
            ImageFeature.EDGE_DENSITY: -0.15,  # Negative weight for edge density
            ImageFeature.SHAPE_PRESENCE: -0.15,  # Negative weight for shape presence
            ImageFeature.ERROR_KEYWORDS: 0.3,
            ImageFeature.DIAGRAM_KEYWORDS: -0.2,  # Negative weight for diagram keywords
            ImageFeature.UI_ELEMENTS: 0.2
        }
        
        # Calculate weighted sum
        score = 0.5  # Start with neutral score
        for feature, value in features.items():
            if feature in weights:
                score += value * weights[feature]
        
        # Ensure score is in range [0, 1]
        return max(0.0, min(score, 1.0))