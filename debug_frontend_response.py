#!/usr/bin/env python3
"""
Debug script to test the exact response from the Gemini endpoint
and see what the frontend should be receiving.
"""

import requests
import json
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO

def create_test_image():
    """Create a simple test image."""
    img = Image.new('RGB', (300, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    draw.text((20, 20), "AWS Architecture", fill='black', font=font)
    draw.rectangle([20, 50, 100, 100], outline='blue', width=2)
    draw.text((30, 70), "EC2", fill='blue', font=font)
    
    draw.rectangle([150, 50, 230, 100], outline='green', width=2)
    draw.text((160, 70), "RDS", fill='green', font=font)
    
    draw.line([100, 75, 150, 75], fill='red', width=2)
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def test_gemini_endpoint():
    """Test the Gemini endpoint and show the exact response."""
    print("Testing Gemini endpoint response structure...")
    
    image_bytes = create_test_image()
    files = {'file': ('test.png', image_bytes, 'image/png')}
    data = {'prompt': 'Analyze this AWS architecture diagram.'}
    
    try:
        response = requests.post(
            'http://localhost:8888/analyze/image/gemini',
            files=files,
            data=data,
            timeout=60
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("\n" + "="*60)
            print("RESPONSE STRUCTURE:")
            print("="*60)
            print(f"Keys in response: {list(result.keys())}")
            
            for key, value in result.items():
                print(f"\n{key}: {type(value)}")
                if isinstance(value, str):
                    print(f"  Length: {len(value)}")
                    print(f"  Preview: {value[:200]}...")
                else:
                    print(f"  Value: {value}")
            
            print("\n" + "="*60)
            print("FULL RESPONSE (formatted):")
            print("="*60)
            print(json.dumps(result, indent=2))
            
            # Test what the frontend logic would do
            print("\n" + "="*60)
            print("FRONTEND LOGIC TEST:")
            print("="*60)
            
            if result.get("status") == "error":
                print("❌ Frontend would show error:")
                print(f"   Error: {result.get('error', 'Unknown error')}")
            else:
                print("✅ Frontend would show success:")
                extracted_text = result.get("extracted_text", "").strip()
                analysis = result.get("analysis", "No analysis available")
                model_id = result.get("model_id", "Unknown model")
                
                print(f"   Extracted text: {'Yes' if extracted_text else 'No'}")
                print(f"   Analysis length: {len(analysis)} characters")
                print(f"   Model ID: {model_id}")
                print(f"   Analysis preview: {analysis[:100]}...")
                
        else:
            print(f"❌ Error response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def test_aws_endpoint():
    """Test the AWS endpoint for comparison."""
    print("\n" + "="*60)
    print("Testing AWS endpoint for comparison...")
    print("="*60)
    
    image_bytes = create_test_image()
    files = {'file': ('test.png', image_bytes, 'image/png')}
    
    try:
        response = requests.post(
            'http://localhost:8888/analyze/image',
            files=files,
            timeout=60
        )
        
        print(f"AWS Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"AWS Response keys: {list(result.keys())}")
            print(f"AWS Status: {result.get('status', 'unknown')}")
            
        else:
            print(f"❌ AWS Error: {response.text}")
            
    except Exception as e:
        print(f"❌ AWS Request failed: {e}")

if __name__ == "__main__":
    test_gemini_endpoint()
    test_aws_endpoint()
