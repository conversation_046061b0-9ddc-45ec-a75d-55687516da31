<mxfile host="app.diagrams.net" modified="2023-11-15T10:00:00.000Z" agent="5.0" version="24.7.17">
  <diagram name="RAG System Architecture" id="RAG-ARCH-001">
    <mxGraphModel dx="1400" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
        <!-- Title -->
        <mxCell id="title" value="RAG System Architecture - AWS Cloud Documentation" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontColor=#232F3E;" vertex="1" parent="1">
          <mxGeometry x="400" y="20" width="800" height="40" as="geometry"/>
        </mxCell>
        
        <!-- Document Ingestion Section -->
        <mxCell id="ingestion_section" value="Document Ingestion Pipeline" style="swimlane;fontStyle=1;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="40" y="80" width="1520" height="280" as="geometry"/>
        </mxCell>
        
        <!-- User Upload -->
        <mxCell id="user_upload" value="User" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#232F3D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=14;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.user;" vertex="1" parent="1">
          <mxGeometry x="100" y="160" width="78" height="78" as="geometry"/>
        </mxCell>
        
        <!-- S3 Bucket -->
        <mxCell id="s3_bucket" value="S3 Bucket" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=14;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;" vertex="1" parent="1">
          <mxGeometry x="280" y="160" width="78" height="78" as="geometry"/>
        </mxCell>
        
        <!-- Document Processor -->
        <mxCell id="doc_processor" value="Document Processor&#xa;(Unstructured.io)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=14;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.lambda;" vertex="1" parent="1">
          <mxGeometry x="460" y="160" width="78" height="78" as="geometry"/>
        </mxCell>
        
        <!-- Text Chunker -->
        <mxCell id="text_chunker" value="Text Chunker&#xa;(LangChain)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=14;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloud9;" vertex="1" parent="1">
          <mxGeometry x="640" y="160" width="78" height="78" as="geometry"/>
        </mxCell>
        
        <!-- Bedrock Embedding -->
        <mxCell id="bedrock_embedding" value="Bedrock&#xa;Embedding Model" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4AB29A;gradientDirection=north;fillColor=#116D5B;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=14;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.sagemaker;" vertex="1" parent="1">
          <mxGeometry x="820" y="160" width="78" height="78" as="geometry"/>
        </mxCell>
        
        <!-- Qdrant Vector Store -->
        <mxCell id="qdrant_store" value="Qdrant&#xa;Vector Store" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=14;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.database;" vertex="1" parent="1">
          <mxGeometry x="1000" y="160" width="78" height="78" as="geometry"/>
        </mxCell>
        
        <!-- Document Ingestion Flow -->
        <mxCell id="flow_upload" value="1. Upload Documents" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#FF8000;" edge="1" parent="1" source="user_upload" target="s3_bucket">
          <mxGeometry x="0.0714" y="10" relative="1" as="geometry">
            <mxPoint x="190" y="199" as="sourcePoint"/>
            <mxPoint x="270" y="199" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow_process" value="2. Process Documents" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#FF8000;" edge="1" parent="1" source="s3_bucket" target="doc_processor">
          <mxGeometry x="0.0714" y="10" relative="1" as="geometry">
            <mxPoint x="370" y="199" as="sourcePoint"/>
            <mxPoint x="450" y="199" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow_chunk" value="3. Split into Chunks" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#FF8000;" edge="1" parent="1" source="doc_processor" target="text_chunker">
          <mxGeometry x="0.0714" y="10" relative="1" as="geometry">
            <mxPoint x="550" y="199" as="sourcePoint"/>
            <mxPoint x="630" y="199" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow_embed" value="4. Generate Embeddings" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#FF8000;" edge="1" parent="1" source="text_chunker" target="bedrock_embedding">
          <mxGeometry x="0.0714" y="10" relative="1" as="geometry">
            <mxPoint x="730" y="199" as="sourcePoint"/>
            <mxPoint x="810" y="199" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow_store" value="5. Store Vectors" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#FF8000;" edge="1" parent="1" source="bedrock_embedding" target="qdrant_store">
          <mxGeometry x="0.0714" y="10" relative="1" as="geometry">
            <mxPoint x="910" y="199" as="sourcePoint"/>
            <mxPoint x="990" y="199" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <!-- Ingestion Metadata -->
        <mxCell id="ingestion_metadata" value="Document Metadata:&#xa;- Source attribution&#xa;- File type&#xa;- Page numbers&#xa;- Image extraction&#xa;- Chunk position" style="shape=note;strokeWidth=2;fontSize=14;size=20;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontColor=#666600;align=left;spacingLeft=10;" vertex="1" parent="1">
          <mxGeometry x="1180" y="140" width="200" height="120" as="geometry"/>
        </mxCell>
        
        <mxCell id="meta_connect" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;fontSize=12;startSize=8;endSize=8;curved=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="qdrant_store" target="ingestion_metadata">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1090" y="199" as="sourcePoint"/>
            <mxPoint x="1170" y="199" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        
        <!-- Query Processing Section -->
        <mxCell id="query_section" value="Query Processing Pipeline" style="swimlane;fontStyle=1;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="40" y="400" width="1520" height="320" as="geometry"/>
        </mxCell>
        
        <!-- User Query -->
        <mxCell id="user_query" value="User" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#232F3D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=14;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.user;" vertex="1" parent="1">
          <mxGeometry x="100" y="480" width="78" height="78" as="geometry"/>
        </mxCell>
        
        <!-- Chainlit Frontend -->
        <mxCell id="chainlit_frontend" value="Chainlit Frontend&#xa;(Port 80)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=14;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudfront;" vertex="1" parent="1">
          <mxGeometry x="280" y="480" width="78" height="78" as="geometry"/>
        </mxCell>
        
        <!-- FastAPI Backend -->
        <mxCell id="fastapi_backend" value="FastAPI Backend&#xa;(Port 8080)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=14;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.lambda;" vertex="1" parent="1">
          <mxGeometry x="460" y="480" width="78" height="78" as="geometry"/>
        </mxCell>
        
        <!-- Advanced Retrieval -->
        <mxCell id="advanced_retrieval" value="Advanced Retrieval&#xa;(Hybrid Search)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=14;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.search;" vertex="1" parent="1">
          <mxGeometry x="640" y="480" width="78" height="78" as="geometry"/>
        </mxCell>
        
        <!-- Qdrant Vector Store (Query) -->
        <mxCell id="qdrant_query" value="Qdrant&#xa;Vector Store" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=14;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.database;" vertex="1" parent="1">
          <mxGeometry x="820" y="480" width="78" height="78" as="geometry"/>
        </mxCell>
        
        <!-- Prompt Templates -->
        <mxCell id="prompt_templates" value="Prompt Templates&#xa;(LangChain)" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=14;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloud9;" vertex="1" parent="1">
          <mxGeometry x="1000" y="480" width="78" height="78" as="geometry"/>
        </mxCell>
        
        <!-- Bedrock LLM -->
        <mxCell id="bedrock_llm" value="Bedrock LLM" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4AB29A;gradientDirection=north;fillColor=#116D5B;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=14;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.sagemaker;" vertex="1" parent="1">
          <mxGeometry x="1180" y="480" width="78" height="78" as="geometry"/>
        </mxCell>
        
        <!-- Query Flow -->
        <mxCell id="flow_user_query" value="1. User Query" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#009900;" edge="1" parent="1" source="user_query" target="chainlit_frontend">
          <mxGeometry x="0.0714" y="10" relative="1" as="geometry">
            <mxPoint x="190" y="519" as="sourcePoint"/>
            <mxPoint x="270" y="519" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow_frontend_backend" value="2. API Request" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#009900;" edge="1" parent="1" source="chainlit_frontend" target="fastapi_backend">
          <mxGeometry x="0.0714" y="10" relative="1" as="geometry">
            <mxPoint x="370" y="519" as="sourcePoint"/>
            <mxPoint x="450" y="519" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow_backend_retrieval" value="3. Process Query" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#009900;" edge="1" parent="1" source="fastapi_backend" target="advanced_retrieval">
          <mxGeometry x="0.0714" y="10" relative="1" as="geometry">
            <mxPoint x="550" y="519" as="sourcePoint"/>
            <mxPoint x="630" y="519" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow_retrieval_qdrant" value="4. Vector Search" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#009900;" edge="1" parent="1" source="advanced_retrieval" target="qdrant_query">
          <mxGeometry x="0.0714" y="10" relative="1" as="geometry">
            <mxPoint x="730" y="519" as="sourcePoint"/>
            <mxPoint x="810" y="519" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow_qdrant_prompt" value="5. Context Preparation" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#009900;" edge="1" parent="1" source="qdrant_query" target="prompt_templates">
          <mxGeometry x="0.0714" y="10" relative="1" as="geometry">
            <mxPoint x="910" y="519" as="sourcePoint"/>
            <mxPoint x="990" y="519" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow_prompt_llm" value="6. Generate Response" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#009900;" edge="1" parent="1" source="prompt_templates" target="bedrock_llm">
          <mxGeometry x="0.0714" y="10" relative="1" as="geometry">
            <mxPoint x="1090" y="519" as="sourcePoint"/>
            <mxPoint x="1170" y="519" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <!-- Response Flow -->
        <mxCell id="flow_llm_backend" value="7. Format Response" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;exitX=0;exitY=0.75;exitDx=0;exitDy=0;entryX=1;entryY=0.75;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#0066CC;" edge="1" parent="1" source="bedrock_llm" target="fastapi_backend">
          <mxGeometry x="-0.0588" y="-10" relative="1" as="geometry">
            <mxPoint x="1180" y="600" as="sourcePoint"/>
            <mxPoint x="500" y="600" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow_backend_frontend" value="8. Return Result" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;exitX=0;exitY=0.75;exitDx=0;exitDy=0;entryX=1;entryY=0.75;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#0066CC;" edge="1" parent="1" source="fastapi_backend" target="chainlit_frontend">
          <mxGeometry x="-0.0714" y="-10" relative="1" as="geometry">
            <mxPoint x="460" y="620" as="sourcePoint"/>
            <mxPoint x="320" y="620" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow_frontend_user" value="9. Display Answer" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;exitX=0;exitY=0.75;exitDx=0;exitDy=0;strokeWidth=2;strokeColor=#0066CC;" edge="1" parent="1" source="chainlit_frontend">
          <mxGeometry x="-0.0714" y="-10" relative="1" as="geometry">
            <mxPoint x="280" y="640" as="sourcePoint"/>
            <mxPoint x="140" y="540" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <!-- End of Query Processing Pipeline -->
        
        <!-- Legend -->
        <mxCell id="legend" value="Legend" style="swimlane;fontStyle=1;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=14;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="1300" y="480" width="240" height="260" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend_ingestion" value="Document Ingestion" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fillColor=#fff2cc;strokeColor=#d6b656;align=left;spacingLeft=10;" vertex="1" parent="1">
          <mxGeometry x="1320" y="520" width="200" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend_query" value="Query Processing" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fillColor=#dae8fc;strokeColor=#6c8ebf;align=left;spacingLeft=10;" vertex="1" parent="1">
          <mxGeometry x="1320" y="560" width="200" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend_aws" value="AWS Services" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f5f5f5;strokeColor=#666666;align=left;spacingLeft=10;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="1320" y="600" width="200" height="30" as="geometry"/>
        </mxCell>
        
        <mxCell id="legend_ingestion_flow" value="Ingestion Flow" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;strokeWidth=2;strokeColor=#FF8000;" edge="1" parent="1">
          <mxGeometry x="0.0667" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1340" y="650" as="sourcePoint"/>
            <mxPoint x="1490" y="650" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="legend_query_flow" value="Query Flow" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;strokeWidth=2;strokeColor=#009900;" edge="1" parent="1">
          <mxGeometry x="0.0667" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1340" y="680" as="sourcePoint"/>
            <mxPoint x="1490" y="680" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="legend_response_flow" value="Response Flow" style="endArrow=classic;html=1;rounded=0;fontSize=12;startSize=8;endSize=8;strokeWidth=2;strokeColor=#0066CC;" edge="1" parent="1">
          <mxGeometry x="0.0667" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1340" y="710" as="sourcePoint"/>
            <mxPoint x="1490" y="710" as="targetPoint"/>
            <mxPoint as="offset"/>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>