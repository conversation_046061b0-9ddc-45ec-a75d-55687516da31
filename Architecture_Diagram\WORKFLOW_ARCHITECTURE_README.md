# RAG System Workflow Architecture

## Overview

This document explains the workflow architecture diagram (`rag_workflow_architecture.svg`) created for the RAG (Retrieval-Augmented Generation) system for AWS Cloud Documentation. The diagram illustrates the complete workflow of the system, focusing on the data flow between components with clear directional arrows.

## Diagram Sections

The architecture diagram is divided into two main sections:

### 1. Document Ingestion Pipeline

This section illustrates how documents are processed and stored in the system:

1. **Upload**: Users upload documents to the system
2. **Process**: Documents are stored in S3 Bucket
3. **Chunk**: Unstructured.io processes documents into text
4. **Embed**: Text is chunked using LangChain
5. **Store**: Bedrock creates embeddings which are stored in Qdrant Vector Store

### 2. Query Processing Pipeline

This section shows how user queries are processed and answered:

1. **Query**: User submits a query through the interface
2. **API Request**: Chainlit Frontend sends the request to the backend
3. **Process**: FastAPI Backend processes the query
4. **Search**: Advanced Retrieval (Hybrid Search) finds relevant information
5. **Prepare**: Qdrant Vector Store returns relevant document chunks
6. **Generate**: Prompt Templates format the context for Bedrock LLM to generate a response
7. **Response Flow**: The generated answer flows back to the user

## Key Features of the Diagram

- **Clear Directional Arrows**: Each step in the workflow is connected with directional arrows showing the exact flow of data
- **Numbered Steps**: Each process is numbered sequentially to indicate the order of operations
- **Shared Resources**: The connection between the Document Ingestion and Query Processing pipelines is shown through the shared Vector Store
- **Color Coding**: Different components are color-coded based on their function and AWS service category
- **Proper Spacing**: Components are evenly spaced for better readability

## Viewing the Diagram

The workflow architecture diagram is provided as an SVG file (`rag_workflow_architecture.svg`), which can be viewed in any modern web browser or image viewer that supports SVG format.

## Implementation Notes

- The diagram focuses solely on the workflow architecture and data flow
- The deployment architecture (systemd services, etc.) has been intentionally omitted for clarity
- The advanced retrieval mechanism combines semantic search and BM25 retrieval as detailed in the codebase