"""
Analysis orchestrator for coordinating image processing and analysis steps.
"""
import logging
import asyncio
import time
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple, Callable, Set
from enum import Enum
import concurrent.futures
from functools import partial
import traceback

from .models import (
    ImageAnalysisRequest, 
    ImageAnalysisResult, 
    ImageType, 
    AnalysisStatus, 
    Analysis,
    AnalysisDetail,
    Recommendation
)
from .storage import TemporaryImageStore, AnalysisResultStore
from .ocr import OCRService
from .error_recognition import ErrorRecognitionService
from .diagram_text import DiagramTextRecognitionService
from .classifier import ImageTypeClassifier
from .diagram_components import DiagramComponentRecognitionService
from .architecture_analyzer import ArchitectureDiagramAnalyzer

# Configure logging
logger = logging.getLogger(__name__)

class ProcessingStep(str, Enum):
    """Enum for processing steps in the analysis workflow."""
    VALIDATION = "validation"
    TEXT_EXTRACTION = "text_extraction"
    CLASSIFICATION = "classification"
    FEATURE_EXTRACTION = "feature_extraction"
    ANALYSIS = "analysis"
    RESULT_GENERATION = "result_generation"
    
class ProcessingStage(str, Enum):
    """Enum for processing stages in the analysis workflow."""
    PREPROCESSING = "preprocessing"
    EXTRACTION = "extraction"
    ANALYSIS = "analysis"
    GENERATION = "generation"
    
class ProcessingStatus(str, Enum):
    """Enum for processing status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class AnalysisOrchestrator:
    """
    Orchestrates the image analysis workflow by coordinating processing steps
    and aggregating results.
    
    The orchestrator manages the entire analysis pipeline, from validating the request
    to generating the final result. It coordinates the various services involved in
    image analysis and ensures proper error handling and result aggregation.
    
    Key responsibilities:
    1. Coordinate the execution of processing steps in the correct sequence
    2. Aggregate results from different processing steps
    3. Handle errors and provide appropriate fallbacks
    4. Track processing status and update request status
    5. Manage parallel processing when appropriate
    6. Provide detailed metrics on processing performance
    7. Support dynamic workflow based on image type and content
    """
    
    # Maximum number of retries for failed steps
    MAX_RETRIES = 2
    
    # Maximum number of worker threads for parallel processing
    MAX_WORKERS = 4
    
    # Timeout for processing steps in seconds
    STEP_TIMEOUT = 60
    
    def __init__(
        self, 
        image_store: TemporaryImageStore, 
        result_store: AnalysisResultStore,
        ocr_service: OCRService,
        error_recognition_service: ErrorRecognitionService,
        diagram_text_service: DiagramTextRecognitionService,
        image_classifier: ImageTypeClassifier,
        diagram_component_service: DiagramComponentRecognitionService
    ):
        """
        Initialize the analysis orchestrator.
        
        Args:
            image_store: Storage service for images
            result_store: Storage service for analysis results
            ocr_service: OCR service for text extraction
            error_recognition_service: Service for recognizing errors
            diagram_text_service: Service for extracting text from diagrams
            image_classifier: Service for classifying image types
            diagram_component_service: Service for recognizing diagram components
        """
        self.image_store = image_store
        self.result_store = result_store
        self.ocr_service = ocr_service
        self.error_recognition_service = error_recognition_service
        self.diagram_text_service = diagram_text_service
        self.image_classifier = image_classifier
        self.diagram_component_service = diagram_component_service
        
        # Initialize architecture analyzer
        self.architecture_analyzer = ArchitectureDiagramAnalyzer()
        
        # Initialize thread pool for parallel processing
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.MAX_WORKERS)
        
        # Initialize processing steps and their status
        self.processing_steps = {
            ProcessingStep.VALIDATION: self._validate_request,
            ProcessingStep.TEXT_EXTRACTION: self._extract_text,
            ProcessingStep.CLASSIFICATION: self._classify_image,
            ProcessingStep.FEATURE_EXTRACTION: self._extract_features,
            ProcessingStep.ANALYSIS: self._analyze_content,
            ProcessingStep.RESULT_GENERATION: self._generate_result
        }
        
        # Group steps into stages for better workflow management
        self.processing_stages = {
            ProcessingStage.PREPROCESSING: [
                ProcessingStep.VALIDATION
            ],
            ProcessingStage.EXTRACTION: [
                ProcessingStep.TEXT_EXTRACTION,
                ProcessingStep.CLASSIFICATION
            ],
            ProcessingStage.ANALYSIS: [
                ProcessingStep.FEATURE_EXTRACTION,
                ProcessingStep.ANALYSIS
            ],
            ProcessingStage.GENERATION: [
                ProcessingStep.RESULT_GENERATION
            ]
        }
        
        # Define step dependencies (steps that must be completed before a step can run)
        self.step_dependencies = {
            ProcessingStep.VALIDATION: [],
            ProcessingStep.TEXT_EXTRACTION: [ProcessingStep.VALIDATION],
            ProcessingStep.CLASSIFICATION: [ProcessingStep.TEXT_EXTRACTION],
            ProcessingStep.FEATURE_EXTRACTION: [ProcessingStep.CLASSIFICATION],
            ProcessingStep.ANALYSIS: [ProcessingStep.FEATURE_EXTRACTION],
            ProcessingStep.RESULT_GENERATION: [ProcessingStep.ANALYSIS]
        }
        
        # Define fallback strategies for each step
        self.fallback_strategies = {
            ProcessingStep.VALIDATION: None,  # No fallback for validation
            ProcessingStep.TEXT_EXTRACTION: self._fallback_text_extraction,
            ProcessingStep.CLASSIFICATION: self._fallback_classification,
            ProcessingStep.FEATURE_EXTRACTION: self._fallback_feature_extraction,
            ProcessingStep.ANALYSIS: self._fallback_analysis,
            ProcessingStep.RESULT_GENERATION: None  # No fallback for result generation
        }
        
    async def process_request(self, request_id: str) -> bool:
        """
        Process an image analysis request through all steps of the workflow.
        
        This method orchestrates the entire analysis pipeline, executing each step
        in sequence and handling any errors that occur. It maintains a context
        dictionary that is passed between steps to share data.
        
        This is a wrapper around process_request_with_retries for backward compatibility.
        
        Args:
            request_id: ID of the analysis request
            
        Returns:
            bool: True if processing was successful
        """
        return await self.process_request_with_retries(request_id)
        
    async def process_request_with_retries(self, request_id: str) -> bool:
        """
        Process an image analysis request with retry and fallback mechanisms.
        
        This method orchestrates the entire analysis pipeline, executing each step
        in sequence with retry logic and fallback strategies for error handling.
        It maintains a context dictionary that is passed between steps to share data.
        
        Args:
            request_id: ID of the analysis request
            
        Returns:
            bool: True if processing was successful
        """
        start_time = time.time()
        logger.info(f"Processing request {request_id} with retries")
        
        # Get the request from storage
        request = self.result_store.get_request(request_id)
        if not request:
            logger.error(f"Request not found: {request_id}")
            return False
        
        # Update request status to PROCESSING
        request.status = AnalysisStatus.PROCESSING
        self.result_store.update_request(request)
        
        # Initialize processing context
        context = {
            "request_id": request_id,
            "request": request,
            "image_path": request.storage_path,
            "step_results": {},
            "step_status": {step: ProcessingStatus.PENDING for step in self.processing_steps},
            "metrics": {
                "start_time": start_time,
                "step_durations": {},
                "step_retries": {},
                "errors": [],
                "fallbacks_used": []
            }
        }
        
        # Process each stage in sequence
        overall_success = True
        
        try:
            for stage in ProcessingStage:
                stage_steps = self.processing_stages.get(stage, [])
                logger.info(f"Processing stage: {stage.value} with steps: {[step.value for step in stage_steps]}")
                
                # Process each step in the stage
                for step in stage_steps:
                    process_func = self.processing_steps[step]
                    
                    # Check if all dependencies are satisfied
                    dependencies_met = True
                    for dep_step in self.step_dependencies[step]:
                        if context["step_status"].get(dep_step) != ProcessingStatus.COMPLETED:
                            dependencies_met = False
                            logger.warning(f"Skipping step {step.value} because dependency {dep_step.value} is not completed")
                            break
                    
                    if not dependencies_met:
                        context["step_status"][step] = ProcessingStatus.SKIPPED
                        overall_success = False
                        continue
                    
                    # Execute the step with retries
                    step_success = await self._execute_step_with_retries(step, process_func, context)
                    
                    if not step_success:
                        logger.error(f"Step {step.value} failed after retries and fallbacks")
                        overall_success = False
                        break
                
                # If any step in the stage failed, stop processing
                if not overall_success:
                    break
                    
        except Exception as e:
            logger.error(f"Unexpected error during processing: {str(e)}")
            logger.debug(f"Exception details: {traceback.format_exc()}")
            overall_success = False
        
        # Calculate total duration
        end_time = time.time()
        context["metrics"]["total_duration"] = end_time - start_time
        
        # Update request status based on overall success
        if overall_success:
            request.status = AnalysisStatus.COMPLETED
            logger.info(f"Request {request_id} processed successfully in {context['metrics']['total_duration']:.2f}s")
        else:
            request.status = AnalysisStatus.FAILED
            logger.error(f"Request {request_id} processing failed in {context['metrics']['total_duration']:.2f}s")
        
        # Update request with metrics
        self.result_store.update_request(request)
        
        return overall_success
    
    async def _execute_step_with_retries(self, step: ProcessingStep, process_func, context: Dict[str, Any]) -> bool:
        """
        Execute a processing step with retries and fallback.
        
        Args:
            step: The processing step to execute
            process_func: The function to execute for the step
            context: Processing context
            
        Returns:
            bool: True if the step was completed successfully
        """
        step_name = step.value
        retry_count = 0
        max_retries = self.MAX_RETRIES
        
        # Initialize retry count in metrics
        context["metrics"]["step_retries"][step_name] = 0
        
        # Mark step as in progress
        context["step_status"][step] = ProcessingStatus.IN_PROGRESS
        
        # Record start time
        step_start_time = time.time()
        
        logger.info(f"Executing step: {step_name}")
        
        while retry_count <= max_retries:
            try:
                # Execute the step
                success, result = await process_func(context)
                
                if success:
                    # Store the result in context
                    context["step_results"][step_name] = result
                    
                    # Mark step as completed
                    context["step_status"][step] = ProcessingStatus.COMPLETED
                    
                    # Record duration
                    step_end_time = time.time()
                    context["metrics"]["step_durations"][step_name] = step_end_time - step_start_time
                    
                    logger.info(f"Step {step_name} completed successfully in {step_end_time - step_start_time:.2f}s")
                    
                    return True
                else:
                    # Step function returned failure
                    error_message = result.get("error", "Unknown error")
                    logger.warning(f"Step {step_name} returned failure: {error_message}")
                    
                    # Record error
                    context["metrics"]["errors"].append({
                        "step": step_name,
                        "error": error_message,
                        "time": datetime.now().isoformat(),
                        "retry": retry_count
                    })
                    
                    # Increment retry count
                    retry_count += 1
                    context["metrics"]["step_retries"][step_name] = retry_count
                    
                    if retry_count <= max_retries:
                        logger.info(f"Retrying step {step_name} (attempt {retry_count}/{max_retries})")
                    else:
                        logger.warning(f"Step {step_name} failed after {max_retries} retries")
            
            except Exception as e:
                # Step function raised an exception
                error_message = str(e)
                logger.warning(f"Step {step_name} raised exception: {error_message}")
                logger.debug(f"Exception details: {traceback.format_exc()}")
                
                # Record error
                context["metrics"]["errors"].append({
                    "step": step_name,
                    "error": error_message,
                    "time": datetime.now().isoformat(),
                    "retry": retry_count
                })
                
                # Increment retry count
                retry_count += 1
                context["metrics"]["step_retries"][step_name] = retry_count
                
                if retry_count <= max_retries:
                    logger.info(f"Retrying step {step_name} (attempt {retry_count}/{max_retries})")
                else:
                    logger.warning(f"Step {step_name} failed after {max_retries} retries")
        
        # All retries failed, try fallback if available
        fallback_func = self.fallback_strategies.get(step)
        if fallback_func:
            logger.info(f"Attempting fallback for step {step_name}")
            return await self._execute_fallback(step, context)
        else:
            # No fallback available, mark step as failed
            context["step_status"][step] = ProcessingStatus.FAILED
            logger.error(f"No fallback available for step {step_name}")
            return False
    
    async def _execute_fallback(self, step: ProcessingStep, context: Dict[str, Any]) -> bool:
        """
        Execute a fallback strategy for a failed step.
        
        Args:
            step: The processing step that failed
            context: Processing context
            
        Returns:
            bool: True if the fallback was successful
        """
        step_name = step.value
        fallback_func = self.fallback_strategies.get(step)
        
        if not fallback_func:
            logger.error(f"No fallback strategy defined for step {step_name}")
            context["step_status"][step] = ProcessingStatus.FAILED
            return False
        
        try:
            # Record fallback usage
            context["metrics"]["fallbacks_used"].append(step_name)
            
            # Execute the fallback
            logger.info(f"Executing fallback for step {step_name}")
            success, result = await fallback_func(context)
            
            if success:
                # Store the result in context
                context["step_results"][step_name] = result
                
                # Mark step as completed
                context["step_status"][step] = ProcessingStatus.COMPLETED
                
                logger.info(f"Fallback for step {step_name} completed successfully")
                
                return True
            else:
                # Fallback returned failure
                error_message = result.get("error", "Unknown error")
                logger.error(f"Fallback for step {step_name} failed: {error_message}")
                
                # Mark step as failed
                context["step_status"][step] = ProcessingStatus.FAILED
                
                return False
                
        except Exception as e:
            # Fallback raised an exception
            error_message = str(e)
            logger.error(f"Fallback for step {step_name} raised exception: {error_message}")
            logger.debug(f"Exception details: {traceback.format_exc()}")
            
            # Mark step as failed
            context["step_status"][step] = ProcessingStatus.FAILED
            
            return False
    
    async def _validate_request(self, context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate the analysis request.
        
        Args:
            context: Processing context
            
        Returns:
            Tuple[bool, Dict[str, Any]]: Success flag and validation results
        """
        request = context["request"]
        image_path = context["image_path"]
        
        # Check if the image exists
        if not self.image_store.image_exists(image_path):
            logger.error(f"Image not found: {image_path}")
            return False, {"error": "Image not found"}
        
        # Additional validation could be added here
        
        return True, {"valid": True}
    
    async def _extract_text(self, context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        Extract text from the image.
        
        Args:
            context: Processing context
            
        Returns:
            Tuple[bool, Dict[str, Any]]: Success flag and extraction results
        """
        image_path = context["image_path"]
        
        try:
            # Extract plain text
            text_content = self.ocr_service.extract_text(image_path)
            
            # Extract text with layout information
            text_with_layout = self.ocr_service.extract_text_with_layout(image_path)
            
            # Extract tables if any
            tables = self.ocr_service.extract_tables(image_path)
            
            return True, {
                "text_content": text_content,
                "text_with_layout": text_with_layout,
                "tables": tables
            }
        except Exception as e:
            logger.error(f"Error extracting text: {str(e)}")
            return False, {"error": f"Text extraction failed: {str(e)}"}
    
    async def _classify_image(self, context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        Classify the image type.
        
        Args:
            context: Processing context
            
        Returns:
            Tuple[bool, Dict[str, Any]]: Success flag and classification results
        """
        image_path = context["image_path"]
        text_content = context["step_results"][ProcessingStep.TEXT_EXTRACTION.value]["text_content"]
        
        try:
            # Classify the image
            image_type, confidence, features = self.image_classifier.classify_image(image_path, text_content)
            
            logger.info(f"Image classified as {image_type.value} with confidence {confidence:.2f}")
            
            return True, {
                "image_type": image_type,
                "confidence": confidence,
                "features": features
            }
        except Exception as e:
            logger.error(f"Error classifying image: {str(e)}")
            return False, {"error": f"Image classification failed: {str(e)}"}
    
    async def _extract_features(self, context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        Extract features from the image based on its type.
        
        Args:
            context: Processing context
            
        Returns:
            Tuple[bool, Dict[str, Any]]: Success flag and feature extraction results
        """
        image_path = context["image_path"]
        image_type = context["step_results"][ProcessingStep.CLASSIFICATION.value]["image_type"]
        text_content = context["step_results"][ProcessingStep.TEXT_EXTRACTION.value]["text_content"]
        
        try:
            features = {}
            
            # Extract features based on image type
            if image_type == ImageType.ARCHITECTURE:
                # Extract diagram components and connections
                components, connections = self.diagram_component_service.recognize_components(image_path)
                
                # Extract labels and text elements
                labels = self.diagram_text_service.extract_labels(image_path)
                
                features = {
                    "components": components,
                    "connections": connections,
                    "labels": labels
                }
            elif image_type == ImageType.ERROR:
                # Identify errors in the text
                identified_errors = self.error_recognition_service.identify_errors(text_content)
                
                # Get suggested solutions
                solutions = self.error_recognition_service.suggest_solutions(text_content)
                
                # Classify the overall error type
                severity, category, error_confidence = self.error_recognition_service.classify_error_type(text_content)
                
                features = {
                    "identified_errors": identified_errors,
                    "solutions": solutions,
                    "severity": severity,
                    "category": category,
                    "error_confidence": error_confidence
                }
            else:
                # For unknown image types, just store basic features
                features = {
                    "text_length": len(text_content),
                    "has_text": len(text_content) > 0
                }
            
            return True, features
        except Exception as e:
            logger.error(f"Error extracting features: {str(e)}")
            return False, {"error": f"Feature extraction failed: {str(e)}"}
    
    async def _analyze_content(self, context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        Analyze the image content based on extracted features.
        
        Args:
            context: Processing context
            
        Returns:
            Tuple[bool, Dict[str, Any]]: Success flag and analysis results
        """
        image_type = context["step_results"][ProcessingStep.CLASSIFICATION.value]["image_type"]
        features = context["step_results"][ProcessingStep.FEATURE_EXTRACTION.value]
        text_content = context["step_results"][ProcessingStep.TEXT_EXTRACTION.value]["text_content"]
        confidence = context["step_results"][ProcessingStep.CLASSIFICATION.value]["confidence"]
        
        try:
            analysis_result = {}
            
            # Generate analysis based on image type
            if image_type == ImageType.ARCHITECTURE:
                analysis_result = self._analyze_architecture(features)
            elif image_type == ImageType.ERROR:
                analysis_result = self._analyze_error(features)
            else:
                analysis_result = self._analyze_unknown(features)
            
            # Add metadata to the analysis result
            analysis_result["metadata"] = {
                "image_type": image_type.value,
                "confidence": confidence,
                "text_length": len(text_content),
                "processing_steps": list(context["step_results"].keys()),
                "timestamp": datetime.now().isoformat()
            }
            
            # Log analysis completion
            logger.info(f"Analysis completed for image type {image_type.value} with confidence {confidence:.2f}")
            
            return True, analysis_result
        except Exception as e:
            logger.error(f"Error analyzing content: {str(e)}")
            return False, {"error": f"Content analysis failed: {str(e)}"}
    
    def _analyze_architecture(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze architecture diagram features using the sophisticated architecture analyzer.
        
        Args:
            features: Extracted features
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        components = features.get("components", [])
        connections = features.get("connections", [])
        labels = features.get("labels", {})
        
        # Use the architecture analyzer for sophisticated analysis
        analysis_result = self.architecture_analyzer.analyze_architecture(
            components, connections, labels
        )
        
        return analysis_result
    
    def _analyze_error(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze error screenshot features.
        
        Args:
            features: Extracted features
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        identified_errors = features.get("identified_errors", [])
        solutions = features.get("solutions", [])
        severity = features.get("severity")
        category = features.get("category")
        
        # Create summary
        summary = "This appears to be an error screenshot."
        if identified_errors:
            summary += f" Identified {len(identified_errors)} potential error messages."
            if category:
                summary += f" The errors appear to be related to {category.value} issues."
            if severity:
                summary += f" The overall severity is {severity.value}."
        
        # Create details from identified errors
        details = []
        for error in identified_errors[:5]:  # Limit to top 5 errors
            details.append({
                "type": "error",
                "content": error["text"],
                "confidence": error["confidence"]
            })
        
        # Create recommendations from suggested solutions
        recommendations = []
        for i, solution in enumerate(solutions[:3], 1):  # Limit to top 3 solutions
            recommendations.append({
                "type": "solution",
                "content": solution["solution"],
                "priority": i
            })
        
        # Add generic recommendations if no specific solutions were found
        if not recommendations:
            recommendations = [
                {
                    "type": "solution",
                    "content": "Check application logs for more detailed error information.",
                    "priority": 1
                },
                {
                    "type": "solution",
                    "content": "Search for the specific error message in documentation or forums.",
                    "priority": 2
                }
            ]
        
        return {
            "summary": summary,
            "details": details,
            "recommendations": recommendations
        }
    
    def _analyze_unknown(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze unknown image type.
        
        Args:
            features: Extracted features
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        has_text = features.get("has_text", False)
        
        # Create summary
        summary = "Unable to determine the type of this image. It doesn't appear to be an architecture diagram or error screenshot."
        
        # Create details
        details = []
        if has_text:
            details.append({
                "type": "info",
                "content": "The image contains text, but it doesn't match known patterns for architecture diagrams or error screenshots.",
                "confidence": 0.5
            })
        
        # Create recommendations
        recommendations = [
            {
                "type": "suggestion",
                "content": "Try uploading a clearer image or one that more clearly shows an architecture diagram or error message.",
                "priority": 1
            },
            {
                "type": "suggestion",
                "content": "If this is an architecture diagram, ensure components and connections are clearly visible.",
                "priority": 2
            }
        ]
        
        return {
            "summary": summary,
            "details": details,
            "recommendations": recommendations
        }
    
    async def _generate_result(self, context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        Generate the final analysis result.
        
        Args:
            context: Processing context
            
        Returns:
            Tuple[bool, Dict[str, Any]]: Success flag and result generation results
        """
        request_id = context["request_id"]
        image_path = context["image_path"]
        image_type = context["step_results"][ProcessingStep.CLASSIFICATION.value]["image_type"]
        confidence = context["step_results"][ProcessingStep.CLASSIFICATION.value]["confidence"]
        text_content = context["step_results"][ProcessingStep.TEXT_EXTRACTION.value]["text_content"]
        analysis_data = context["step_results"][ProcessingStep.ANALYSIS.value]
        
        try:
            # Create Analysis object
            analysis = Analysis(
                summary=analysis_data["summary"],
                details=[AnalysisDetail(**detail) for detail in analysis_data["details"]],
                recommendations=[Recommendation(**rec) for rec in analysis_data["recommendations"]]
            )
            
            # Create ImageAnalysisResult
            result = ImageAnalysisResult(
                request_id=request_id,
                analysis_type=image_type,
                confidence=confidence,
                image_url=image_path,
                text_content=text_content,
                analysis=analysis
            )
            
            # Store the result
            self.result_store.store_result(result)
            
            return True, {"result_id": result.id}
        except Exception as e:
            logger.error(f"Error generating result: {str(e)}")
            return False, {"error": f"Result generation failed: {str(e)}"}
            
    async def _fallback_text_extraction(self, context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        Fallback strategy for text extraction.
        
        Args:
            context: Processing context
            
        Returns:
            Tuple[bool, Dict[str, Any]]: Success flag and fallback results
        """
        logger.warning("Using fallback strategy for text extraction")
        image_path = context["image_path"]
        
        try:
            # Try with minimal preprocessing and default settings
            text_content = self.ocr_service.extract_text(image_path, preprocessing=None, mode=None)
            
            # Return minimal results
            return True, {
                "text_content": text_content,
                "text_with_layout": {"text": text_content, "blocks": []},
                "tables": [],
                "is_fallback": True
            }
        except Exception as e:
            logger.error(f"Fallback text extraction failed: {str(e)}")
            # Return empty results as last resort
            return True, {
                "text_content": "",
                "text_with_layout": {"text": "", "blocks": []},
                "tables": [],
                "is_fallback": True,
                "fallback_error": str(e)
            }
    
    async def _fallback_classification(self, context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        Fallback strategy for image classification.
        
        Args:
            context: Processing context
            
        Returns:
            Tuple[bool, Dict[str, Any]]: Success flag and fallback results
        """
        logger.warning("Using fallback strategy for image classification")
        text_content = context["step_results"][ProcessingStep.TEXT_EXTRACTION.value]["text_content"]
        
        # Simple keyword-based classification
        error_keywords = ["error", "exception", "failed", "warning", "alert", "critical"]
        architecture_keywords = ["diagram", "architecture", "flow", "component", "service", "system"]
        
        # Count occurrences of keywords
        error_count = sum(1 for keyword in error_keywords if keyword.lower() in text_content.lower())
        architecture_count = sum(1 for keyword in architecture_keywords if keyword.lower() in text_content.lower())
        
        # Classify based on keyword counts
        if error_count > architecture_count:
            image_type = ImageType.ERROR
            confidence = min(0.6, error_count / 10)  # Cap confidence at 0.6 for fallback
        elif architecture_count > 0:
            image_type = ImageType.ARCHITECTURE
            confidence = min(0.6, architecture_count / 10)  # Cap confidence at 0.6 for fallback
        else:
            image_type = ImageType.UNKNOWN
            confidence = 0.3
        
        logger.info(f"Fallback classification: {image_type.value} with confidence {confidence:.2f}")
        
        return True, {
            "image_type": image_type,
            "confidence": confidence,
            "features": {},
            "is_fallback": True
        }
    
    async def _fallback_feature_extraction(self, context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        Fallback strategy for feature extraction.
        
        Args:
            context: Processing context
            
        Returns:
            Tuple[bool, Dict[str, Any]]: Success flag and fallback results
        """
        logger.warning("Using fallback strategy for feature extraction")
        image_type = context["step_results"][ProcessingStep.CLASSIFICATION.value]["image_type"]
        text_content = context["step_results"][ProcessingStep.TEXT_EXTRACTION.value]["text_content"]
        
        # Create minimal features based on image type
        if image_type == ImageType.ARCHITECTURE:
            return True, {
                "components": [],
                "connections": [],
                "labels": {},
                "is_fallback": True
            }
        elif image_type == ImageType.ERROR:
            # Extract basic error information from text
            error_lines = [line for line in text_content.split('\n') if any(kw in line.lower() for kw in ["error", "exception", "failed", "warning"])]
            
            identified_errors = []
            for i, line in enumerate(error_lines[:5]):  # Limit to top 5 error lines
                identified_errors.append({
                    "text": line,
                    "confidence": 0.5,
                    "category": "unknown",
                    "severity": "unknown",
                    "description": "Error detected by fallback mechanism"
                })
            
            return True, {
                "identified_errors": identified_errors,
                "solutions": [],
                "severity": None,
                "category": None,
                "error_confidence": 0.5,
                "is_fallback": True
            }
        else:
            return True, {
                "text_length": len(text_content),
                "has_text": len(text_content) > 0,
                "is_fallback": True
            }
    
    async def _fallback_analysis(self, context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        Fallback strategy for content analysis.
        
        Args:
            context: Processing context
            
        Returns:
            Tuple[bool, Dict[str, Any]]: Success flag and fallback results
        """
        logger.warning("Using fallback strategy for content analysis")
        image_type = context["step_results"][ProcessingStep.CLASSIFICATION.value]["image_type"]
        text_content = context["step_results"][ProcessingStep.TEXT_EXTRACTION.value]["text_content"]
        
        # Create minimal analysis based on image type
        if image_type == ImageType.ARCHITECTURE:
            return True, {
                "summary": "This appears to be an architecture diagram. Limited analysis is available due to processing issues.",
                "details": [
                    {
                        "type": "info",
                        "content": "The system encountered issues analyzing this diagram in detail.",
                        "confidence": 0.5
                    }
                ],
                "recommendations": [
                    {
                        "type": "suggestion",
                        "content": "Try uploading a clearer image with well-defined components and connections.",
                        "priority": 1
                    }
                ],
                "is_fallback": True
            }
        elif image_type == ImageType.ERROR:
            return True, {
                "summary": "This appears to be an error screenshot. Limited analysis is available due to processing issues.",
                "details": [
                    {
                        "type": "error",
                        "content": text_content[:200] + ("..." if len(text_content) > 200 else ""),
                        "confidence": 0.5
                    }
                ],
                "recommendations": [
                    {
                        "type": "solution",
                        "content": "Search for the specific error message in documentation or forums.",
                        "priority": 1
                    },
                    {
                        "type": "solution",
                        "content": "Check application logs for more detailed error information.",
                        "priority": 2
                    }
                ],
                "is_fallback": True
            }
        else:
            return True, {
                "summary": "Unable to determine the type of this image. Limited analysis is available due to processing issues.",
                "details": [],
                "recommendations": [
                    {
                        "type": "suggestion",
                        "content": "Try uploading a clearer image or one that more clearly shows an architecture diagram or error message.",
                        "priority": 1
                    }
                ],
                "is_fallback": True
            }
    
    async def process_request_with_retries(self, request_id: str) -> bool:
        """
        Process an image analysis request with retries and fallbacks.
        
        This enhanced version of process_request includes retry logic and fallback
        strategies for more robust processing.
        
        Args:
            request_id: ID of the analysis request
            
        Returns:
            bool: True if processing was successful
        """
        start_time = datetime.now()
        processing_metrics = {
            "start_time": start_time,
            "step_durations": {},
            "step_retries": {},
            "total_duration": None,
            "errors": [],
            "fallbacks_used": []
        }
        
        try:
            # Get the request
            request = self.result_store.get_request(request_id)
            if not request:
                logger.error(f"Request not found: {request_id}")
                return False
            
            # Update request status to processing
            request.status = AnalysisStatus.PROCESSING
            self.result_store.update_request(request)
            
            # Initialize context for sharing data between steps
            context = {
                "request_id": request_id,
                "request": request,
                "image_path": request.storage_path,
                "step_results": {},
                "step_status": {step: ProcessingStatus.PENDING for step in self.processing_steps},
                "metrics": processing_metrics
            }
            
            # Process each stage in sequence
            for stage, steps in self.processing_stages.items():
                logger.info(f"Processing stage {stage.value} for request {request_id}")
                
                # Process steps in this stage
                for step in steps:
                    process_func = self.processing_steps[step]
                    
                    # Check if dependencies are met
                    dependencies_met = True
                    for dep_step in self.step_dependencies[step]:
                        if context["step_status"][dep_step] != ProcessingStatus.COMPLETED:
                            dependencies_met = False
                            logger.warning(f"Dependencies not met for step {step.value}, skipping")
                            break
                    
                    if not dependencies_met:
                        context["step_status"][step] = ProcessingStatus.SKIPPED
                        continue
                    
                    # Execute the step with retries
                    step_success = await self._execute_step_with_retries(step, process_func, context)
                    
                    # If step failed even with retries and fallbacks, fail the request
                    if not step_success:
                        logger.error(f"Step {step.value} failed after retries for request {request_id}")
                        request.status = AnalysisStatus.FAILED
                        self.result_store.update_request(request)
                        return False
            
            # Calculate total processing time
            end_time = datetime.now()
            total_duration = (end_time - start_time).total_seconds()
            processing_metrics["total_duration"] = total_duration
            
            # Update request status to completed
            request.status = AnalysisStatus.COMPLETED
            if ProcessingStep.CLASSIFICATION.value in context["step_results"]:
                request.image_type = context["step_results"][ProcessingStep.CLASSIFICATION.value]["image_type"]
            self.result_store.update_request(request)
            
            # Log processing metrics
            logger.info(f"Successfully processed image: {request_id} in {total_duration:.2f} seconds")
            for step, duration in processing_metrics["step_durations"].items():
                logger.debug(f"Step {step} took {duration:.2f} seconds")
            
            if processing_metrics["fallbacks_used"]:
                logger.warning(f"Fallbacks used for steps: {', '.join(processing_metrics['fallbacks_used'])}")
            
            return True
            
        except Exception as e:
            error_msg = f"Error processing image {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # Record the error
            if "metrics" in locals():
                processing_metrics["errors"].append({
                    "step": "process_request",
                    "error": str(e),
                    "time": datetime.now().isoformat()
                })
            
            # Update request status to failed
            try:
                request = self.result_store.get_request(request_id)
                if request:
                    request.status = AnalysisStatus.FAILED
                    self.result_store.update_request(request)
            except Exception as update_error:
                logger.error(f"Error updating request status: {str(update_error)}")
                
            return False
    
    async def _execute_step_with_retries(self, step: ProcessingStep, process_func: Callable, context: Dict[str, Any]) -> bool:
        """
        Execute a processing step with retries and fallback.
        
        Args:
            step: Processing step to execute
            process_func: Function to execute the step
            context: Processing context
            
        Returns:
            bool: True if the step was completed successfully
        """
        request_id = context["request_id"]
        metrics = context["metrics"]
        
        # Initialize retry count
        retry_count = 0
        max_retries = self.MAX_RETRIES
        
        # Update step status
        context["step_status"][step] = ProcessingStatus.IN_PROGRESS
        
        # Record retry count
        metrics["step_retries"][step.value] = 0
        
        while retry_count <= max_retries:
            step_start_time = datetime.now()
            
            try:
                logger.info(f"Executing step {step.value} for request {request_id} (attempt {retry_count + 1}/{max_retries + 1})")
                
                # Execute the step with timeout
                try:
                    step_success, step_result = await asyncio.wait_for(
                        process_func(context),
                        timeout=self.STEP_TIMEOUT
                    )
                except asyncio.TimeoutError:
                    logger.error(f"Step {step.value} timed out after {self.STEP_TIMEOUT} seconds")
                    step_success = False
                    step_result = {"error": f"Step timed out after {self.STEP_TIMEOUT} seconds"}
                
                # Record step duration
                step_end_time = datetime.now()
                step_duration = (step_end_time - step_start_time).total_seconds()
                metrics["step_durations"][step.value] = step_duration
                
                if step_success:
                    # Store step results in context for use by subsequent steps
                    context["step_results"][step.value] = step_result
                    context["step_status"][step] = ProcessingStatus.COMPLETED
                    return True
                
                # Step failed, record the error
                error_msg = f"Step {step.value} failed for request {request_id}: {step_result.get('error', 'Unknown error')}"
                logger.error(error_msg)
                metrics["errors"].append({
                    "step": step.value,
                    "error": step_result.get("error", "Unknown error"),
                    "time": datetime.now().isoformat(),
                    "attempt": retry_count + 1
                })
                
                # Increment retry count
                retry_count += 1
                metrics["step_retries"][step.value] = retry_count
                
                # If we've exhausted retries, try fallback
                if retry_count > max_retries:
                    return await self._execute_fallback(step, context)
                
                # Wait before retrying (exponential backoff)
                backoff_time = min(2 ** retry_count, 10)  # Cap at 10 seconds
                logger.info(f"Retrying step {step.value} in {backoff_time} seconds")
                await asyncio.sleep(backoff_time)
                
            except Exception as e:
                error_msg = f"Error in step {step.value} for request {request_id}: {str(e)}"
                logger.error(error_msg)
                logger.error(traceback.format_exc())
                
                metrics["errors"].append({
                    "step": step.value,
                    "error": str(e),
                    "time": datetime.now().isoformat(),
                    "attempt": retry_count + 1
                })
                
                # Increment retry count
                retry_count += 1
                metrics["step_retries"][step.value] = retry_count
                
                # If we've exhausted retries, try fallback
                if retry_count > max_retries:
                    return await self._execute_fallback(step, context)
                
                # Wait before retrying (exponential backoff)
                backoff_time = min(2 ** retry_count, 10)  # Cap at 10 seconds
                logger.info(f"Retrying step {step.value} in {backoff_time} seconds")
                await asyncio.sleep(backoff_time)
        
        # This should not be reached due to the return in the fallback case
        return False
    
    async def _execute_fallback(self, step: ProcessingStep, context: Dict[str, Any]) -> bool:
        """
        Execute fallback strategy for a failed step.
        
        Args:
            step: Processing step that failed
            context: Processing context
            
        Returns:
            bool: True if fallback was successful
        """
        request_id = context["request_id"]
        metrics = context["metrics"]
        
        # Check if fallback is available for this step
        fallback_func = self.fallback_strategies.get(step)
        if not fallback_func:
            logger.error(f"No fallback strategy available for step {step.value}")
            context["step_status"][step] = ProcessingStatus.FAILED
            return False
        
        try:
            logger.warning(f"Executing fallback for step {step.value} for request {request_id}")
            
            # Record fallback usage
            metrics["fallbacks_used"].append(step.value)
            
            # Execute fallback
            fallback_start_time = datetime.now()
            fallback_success, fallback_result = await fallback_func(context)
            
            # Record fallback duration
            fallback_end_time = datetime.now()
            fallback_duration = (fallback_end_time - fallback_start_time).total_seconds()
            metrics["step_durations"][f"{step.value}_fallback"] = fallback_duration
            
            if fallback_success:
                # Store fallback results in context
                context["step_results"][step.value] = fallback_result
                context["step_status"][step] = ProcessingStatus.COMPLETED
                logger.info(f"Fallback for step {step.value} succeeded")
                return True
            
            # Fallback failed
            logger.error(f"Fallback for step {step.value} failed: {fallback_result.get('error', 'Unknown error')}")
            context["step_status"][step] = ProcessingStatus.FAILED
            return False
            
        except Exception as e:
            error_msg = f"Error in fallback for step {step.value}: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            metrics["errors"].append({
                "step": f"{step.value}_fallback",
                "error": str(e),
                "time": datetime.now().isoformat()
            })
            
            context["step_status"][step] = ProcessingStatus.FAILED
            return False
    
    async def aggregate_results(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Aggregate results from all processing steps.
        
        This method combines results from various processing steps into a
        comprehensive result object that can be used for reporting and analysis.
        
        Args:
            context: Processing context
            
        Returns:
            Dict[str, Any]: Aggregated results
        """
        # Extract key results from context
        request_id = context["request_id"]
        metrics = context["metrics"]
        step_results = context["step_results"]
        step_status = context["step_status"]
        
        # Initialize aggregated results
        aggregated_results = {
            "request_id": request_id,
            "processing_time": metrics["total_duration"],
            "steps_completed": sum(1 for status in step_status.values() if status == ProcessingStatus.COMPLETED),
            "steps_failed": sum(1 for status in step_status.values() if status == ProcessingStatus.FAILED),
            "steps_skipped": sum(1 for status in step_status.values() if status == ProcessingStatus.SKIPPED),
            "fallbacks_used": metrics["fallbacks_used"],
            "errors": metrics["errors"],
            "results": {}
        }
        
        # Add results from each completed step
        for step, status in step_status.items():
            if status == ProcessingStatus.COMPLETED and step.value in step_results:
                # Filter out large data structures to keep the aggregated results manageable
                filtered_result = self._filter_step_result(step, step_results[step.value])
                aggregated_results["results"][step.value] = filtered_result
        
        # Add analysis summary if available
        if ProcessingStep.ANALYSIS.value in step_results:
            analysis_result = step_results[ProcessingStep.ANALYSIS.value]
            if "summary" in analysis_result:
                aggregated_results["summary"] = analysis_result["summary"]
            if "details" in analysis_result:
                aggregated_results["details"] = analysis_result["details"]
            if "recommendations" in analysis_result:
                aggregated_results["recommendations"] = analysis_result["recommendations"]
        
        # Add image type and confidence if available
        if ProcessingStep.CLASSIFICATION.value in step_results:
            classification_result = step_results[ProcessingStep.CLASSIFICATION.value]
            if "image_type" in classification_result:
                aggregated_results["image_type"] = classification_result["image_type"].value
            if "confidence" in classification_result:
                aggregated_results["confidence"] = classification_result["confidence"]
        
        return aggregated_results
    
    def _filter_step_result(self, step: ProcessingStep, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Filter step result to remove large data structures.
        
        Args:
            step: Processing step
            result: Step result
            
        Returns:
            Dict[str, Any]: Filtered result
        """
        # Create a copy to avoid modifying the original
        filtered = {}
        
        # Filter based on step type
        if step == ProcessingStep.TEXT_EXTRACTION:
            # Include text content length but not the full content
            if "text_content" in result:
                filtered["text_length"] = len(result["text_content"])
                filtered["text_sample"] = result["text_content"][:100] + ("..." if len(result["text_content"]) > 100 else "")
            if "tables" in result:
                filtered["table_count"] = len(result["tables"])
            if "is_fallback" in result:
                filtered["is_fallback"] = result["is_fallback"]
        elif step == ProcessingStep.CLASSIFICATION:
            # Include all classification results
            filtered = result.copy()
            # Remove large feature vectors if present
            if "features" in filtered and isinstance(filtered["features"], dict) and len(filtered["features"]) > 10:
                filtered["features"] = {k: v for k, v in list(filtered["features"].items())[:10]}
                filtered["features"]["..."] = "Additional features omitted"
        elif step == ProcessingStep.FEATURE_EXTRACTION:
            # Summarize extracted features
            if "components" in result:
                filtered["component_count"] = len(result["components"])
            if "connections" in result:
                filtered["connection_count"] = len(result["connections"])
            if "labels" in result:
                filtered["label_count"] = sum(len(labels) for labels in result["labels"].values())
            if "identified_errors" in result:
                filtered["error_count"] = len(result["identified_errors"])
            if "solutions" in result:
                filtered["solution_count"] = len(result["solutions"])
            if "severity" in result:
                filtered["severity"] = result["severity"].value if result["severity"] else None
            if "category" in result:
                filtered["category"] = result["category"].value if result["category"] else None
            if "is_fallback" in result:
                filtered["is_fallback"] = result["is_fallback"]
        elif step == ProcessingStep.ANALYSIS:
            # Include analysis summary and metadata
            if "summary" in result:
                filtered["summary"] = result["summary"]
            if "metadata" in result:
                filtered["metadata"] = result["metadata"]
            if "is_fallback" in result:
                filtered["is_fallback"] = result["is_fallback"]
            # Count details and recommendations
            if "details" in result:
                filtered["detail_count"] = len(result["details"])
            if "recommendations" in result:
                filtered["recommendation_count"] = len(result["recommendations"])
        elif step == ProcessingStep.RESULT_GENERATION:
            # Include result ID
            if "result_id" in result:
                filtered["result_id"] = result["result_id"]
        else:
            # For other steps, include everything
            filtered = result.copy()
        
        return filtered