#!/usr/bin/env python3
"""
Test that mimics exactly what the frontend does.
"""

import asyncio
import aiohttp
from PIL import Image, ImageDraw
from io import BytesIO

# Same timeout as frontend
TIMEOUT = aiohttp.ClientTimeout(total=120)
API_BASE_URL = "http://localhost:8888"

def create_test_image():
    """Create a test image."""
    img = Image.new('RGB', (200, 150), color='white')
    draw = ImageDraw.Draw(img)
    draw.text((20, 20), "AWS Test", fill='black')
    draw.rectangle([20, 50, 80, 90], outline='blue', width=2)
    draw.text((25, 65), "EC2", fill='blue')
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

async def test_frontend_exact_call():
    """Test the exact same call that the frontend makes."""
    print("Testing exact frontend call...")
    
    # Create test image
    image_bytes = create_test_image()
    print(f"Image size: {len(image_bytes)} bytes")
    
    # Use the exact same URL and method as frontend
    url = f"{API_BASE_URL}/analyze/image/gemini"
    print(f"URL: {url}")
    
    # Create FormData exactly like the frontend
    data = aiohttp.FormData()
    data.add_field('file', image_bytes, filename='upload.png', content_type='image/png')
    # Note: No custom prompt, just like the frontend
    
    print("Sending request...")
    
    async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
        async with session.post(url, data=data) as response:
            print(f"Response status: {response.status}")
            print(f"Response headers: {dict(response.headers)}")
            
            if response.status == 200:
                result = await response.json()
                print(f"Response keys: {list(result.keys())}")
                print(f"Status: {result.get('status')}")
                
                analysis = result.get('analysis', '')
                print(f"Analysis type: {type(analysis)}")
                print(f"Analysis length: {len(analysis)}")
                print(f"Analysis empty: {not analysis}")
                
                if analysis:
                    print(f"Analysis preview: {analysis[:200]}...")
                else:
                    print("❌ Analysis is empty!")
                    print("Full response:")
                    print(result)
                    
                return result
            else:
                error_text = await response.text()
                print(f"❌ HTTP Error: {response.status}")
                print(f"Error text: {error_text}")
                return None

async def test_requests_call():
    """Test with requests library for comparison."""
    print("\nTesting with requests library...")
    
    import requests
    
    image_bytes = create_test_image()
    files = {'file': ('upload.png', image_bytes, 'image/png')}
    
    try:
        response = requests.post(
            'http://localhost:8888/analyze/image/gemini',
            files=files,
            timeout=60
        )
        
        print(f"Requests response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            analysis = result.get('analysis', '')
            print(f"Requests analysis length: {len(analysis)}")
            if analysis:
                print(f"Requests analysis preview: {analysis[:100]}...")
            else:
                print("❌ Requests analysis is empty!")
        else:
            print(f"❌ Requests error: {response.text}")
            
    except Exception as e:
        print(f"❌ Requests exception: {e}")

async def main():
    """Main function."""
    try:
        await test_frontend_exact_call()
        await test_requests_call()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
