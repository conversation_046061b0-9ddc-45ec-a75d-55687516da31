"""
Tests for the OCR service.
"""
import os
import io
import pytest
import tempfile
from PIL import Image, ImageDraw, ImageFont
from unittest.mock import patch, MagicMock

from image_analysis.ocr import OCRService, OCRLanguage, OCRMode, OCRPreprocessing


def create_text_image(text="Test Text", size=(300, 100), bg_color=(255, 255, 255), text_color=(0, 0, 0)):
    """Create a test image with text for OCR testing."""
    # Create a blank image with background color
    img = Image.new("RGB", size, bg_color)
    draw = ImageDraw.Draw(img)
    
    # Try to use a font that's likely to be available
    try:
        # Try to use Arial font if available
        font = ImageFont.truetype("arial.ttf", 20)
    except IOError:
        # Fall back to default font
        font = ImageFont.load_default()
    
    # Calculate text position to center it
    text_width, text_height = draw.textsize(text, font=font) if hasattr(draw, 'textsize') else (len(text) * 10, 20)
    position = ((size[0] - text_width) // 2, (size[1] - text_height) // 2)
    
    # Draw the text
    draw.text(position, text, fill=text_color, font=font)
    
    # Save to a bytes buffer
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format="PNG")
    img_byte_arr.seek(0)
    
    return img, img_byte_arr


class TestOCRService:
    """Tests for the OCR service."""
    
    def setup_method(self):
        """Set up test environment."""
        # Create a temporary directory for test images
        self.temp_dir = tempfile.mkdtemp()
        
        # Create OCR service
        self.ocr_service = OCRService()
        
        # Create a test image with text
        self.test_img, self.test_img_bytes = create_text_image("OCR Test Text")
        self.test_img_path = os.path.join(self.temp_dir, "test_image.png")
        self.test_img.save(self.test_img_path)
    
    def teardown_method(self):
        """Clean up test environment."""
        # Remove temporary files
        if os.path.exists(self.test_img_path):
            os.remove(self.test_img_path)
        
        # Remove temporary directory
        if os.path.exists(self.temp_dir):
            os.rmdir(self.temp_dir)
    
    @patch('pytesseract.image_to_string')
    def test_extract_text_basic(self, mock_image_to_string):
        """Test basic text extraction."""
        # Mock pytesseract response
        mock_image_to_string.return_value = "OCR Test Text"
        
        # Extract text
        text = self.ocr_service.extract_text(self.test_img_path)
        
        # Check result
        assert text == "OCR Test Text"
        mock_image_to_string.assert_called_once()
    
    @patch('pytesseract.image_to_string')
    def test_extract_text_with_preprocessing(self, mock_image_to_string):
        """Test text extraction with preprocessing."""
        # Mock pytesseract response
        mock_image_to_string.return_value = "OCR Test Text"
        
        # Extract text with different preprocessing methods
        for preprocessing in OCRPreprocessing:
            text = self.ocr_service.extract_text(
                self.test_img_path,
                preprocessing=preprocessing
            )
            
            # Check result
            assert text == "OCR Test Text"
    
    @patch('pytesseract.image_to_string')
    def test_extract_text_with_different_modes(self, mock_image_to_string):
        """Test text extraction with different OCR modes."""
        # Mock pytesseract response
        mock_image_to_string.return_value = "OCR Test Text"
        
        # Extract text with different OCR modes
        for mode in OCRMode:
            text = self.ocr_service.extract_text(
                self.test_img_path,
                mode=mode
            )
            
            # Check result
            assert text == "OCR Test Text"
    
    @patch('pytesseract.image_to_string')
    def test_extract_text_with_different_languages(self, mock_image_to_string):
        """Test text extraction with different languages."""
        # Mock pytesseract response
        mock_image_to_string.return_value = "OCR Test Text"
        
        # Extract text with different languages
        for language in OCRLanguage:
            text = self.ocr_service.extract_text(
                self.test_img_path,
                language=language
            )
            
            # Check result
            assert text == "OCR Test Text"
            # Verify language parameter was passed
            mock_image_to_string.assert_called_with(
                pytest.approx(self.test_img), 
                lang=language, 
                config=pytest.approx("")
            )
            mock_image_to_string.reset_mock()
    
    @patch('pytesseract.image_to_data')
    def test_extract_text_with_layout(self, mock_image_to_data):
        """Test text extraction with layout information."""
        # Mock pytesseract response
        mock_image_to_data.return_value = {
            "text": ["OCR", "Test", "Text"],
            "block_num": [1, 1, 1],
            "line_num": [1, 1, 1],
            "conf": [90, 90, 90],
            "left": [10, 50, 90],
            "top": [10, 10, 10],
            "width": [30, 30, 30],
            "height": [20, 20, 20]
        }
        
        # Extract text with layout
        result = self.ocr_service.extract_text_with_layout(self.test_img_path)
        
        # Check result
        assert "text" in result
        assert "blocks" in result
        assert result["text"] == "OCR Test Text"
        assert len(result["blocks"]) == 1
        assert result["blocks"][0]["text"] == "OCR Test Text"
        assert len(result["blocks"][0]["lines"]) == 1
        assert result["blocks"][0]["lines"][0]["text"] == "OCR Test Text"
        assert len(result["blocks"][0]["lines"][0]["words"]) == 3
    
    @patch('cv2.imread')
    @patch('cv2.cvtColor')
    @patch('cv2.threshold')
    @patch('cv2.findContours')
    @patch('cv2.boundingRect')
    @patch('cv2.imwrite')
    @patch('pytesseract.image_to_string')
    def test_extract_tables(self, mock_image_to_string, mock_imwrite, mock_boundingRect, 
                           mock_findContours, mock_threshold, mock_cvtColor, mock_imread):
        """Test table extraction."""
        # Mock OpenCV responses
        mock_imread.return_value = MagicMock()
        mock_cvtColor.return_value = MagicMock()
        mock_threshold.return_value = (None, MagicMock())
        mock_findContours.return_value = ([MagicMock()], None)
        mock_boundingRect.return_value = (10, 10, 200, 100)
        
        # Mock pytesseract response
        mock_image_to_string.return_value = "Table Content"
        
        # Extract tables
        tables = self.ocr_service.extract_tables(self.test_img_path)
        
        # Check result
        assert len(tables) == 1
        assert "bbox" in tables[0]
        assert "text" in tables[0]
        assert tables[0]["text"] == "Table Content"
        assert tables[0]["bbox"] == [10, 10, 200, 100]
    
    def test_nonexistent_file(self):
        """Test handling of nonexistent files."""
        # Try to extract text from a nonexistent file
        text = self.ocr_service.extract_text("nonexistent_file.png")
        
        # Check result
        assert text == ""
    
    def test_svg_file(self):
        """Test handling of SVG files."""
        # Create a test SVG file
        svg_path = os.path.join(self.temp_dir, "test.svg")
        with open(svg_path, "w") as f:
            f.write('<svg width="100" height="100"></svg>')
        
        # Try to extract text from the SVG file
        text = self.ocr_service.extract_text(svg_path)
        
        # Check result
        assert text == ""
        
        # Clean up
        os.remove(svg_path)