# Path Updates Summary: rag-app → chainlit_rag

This document summarizes all the path changes made to accommodate your actual folder name "chainlit_rag" instead of the default "rag-app".

## Files Updated

### 1. Systemd Service Files

#### `systemd-services/rag-api.service`
- **WorkingDirectory**: `/opt/rag-app` → `/opt/chainlit_rag`
- **ExecStart**: `/opt/rag-app/venv/bin/gunicorn` → `/opt/chainlit_rag/venv/bin/gunicorn`
- **ReadWritePaths**: `/opt/rag-app` → `/opt/chainlit_rag`
- **Environment PATH**: `/opt/rag-app/venv/bin` → `/opt/chainlit_rag/venv/bin`
- **EnvironmentFile**: `/opt/rag-app/.env` → `/opt/chainlit_rag/.env`

#### `systemd-services/rag-frontend.service`
- **WorkingDirectory**: `/opt/rag-app` → `/opt/chainlit_rag`
- **ExecStart**: `/opt/rag-app/venv/bin/chainlit` → `/opt/chainlit_rag/venv/bin/chainlit`
- **ReadWritePaths**: `/opt/rag-app` → `/opt/chainlit_rag`
- **Environment PATH**: `/opt/rag-app/venv/bin` → `/opt/chainlit_rag/venv/bin`
- **EnvironmentFile**: `/opt/rag-app/.env` → `/opt/chainlit_rag/.env`

### 2. Installation and Management Scripts

#### `systemd-services/install-services.sh`
- **APP_DIR variable**: `/opt/rag-app` → `/opt/chainlit_rag`
- **Vector store directory**: `$APP_DIR/vector_store` (automatically updated)
- **Environment file**: `$APP_DIR/.env` (automatically updated)
- **All ownership commands**: Updated to use new APP_DIR

#### `systemd-services/validate-services.sh`
- **APP_DIR variable**: `/opt/rag-app` → `/opt/chainlit_rag`
- **All validation checks**: Updated to use new APP_DIR

### 3. Deployment Scripts

#### `deployment-scripts/deploy-rag-systemd.sh`
- **APP_DIR variable**: `/opt/rag-app` → `/opt/chainlit_rag`

#### `deployment-scripts/rag-service-manager.sh`
- **APP_DIR variable**: `/opt/rag-app` → `/opt/chainlit_rag`

### 4. Documentation

#### `systemd-services/README.md`
- **Environment file path**: `/opt/rag-app/.env` → `/opt/chainlit_rag/.env`
- **File locations section**: Updated directory structure
- **All command examples**: Updated to use new paths
- **Troubleshooting commands**: Updated paths

## Directory Structure (Updated)

```
/opt/chainlit_rag/              # Your application directory
├── .env                        # Environment configuration
├── main.py                     # FastAPI application
├── chainlit_app.py            # Chainlit frontend
├── vector_store/              # Local Qdrant storage
├── venv/                      # Python virtual environment
├── requirements.txt           # Python dependencies
└── systemd-services/          # Service files (this directory)
    ├── rag-api.service
    ├── rag-frontend.service
    ├── install-services.sh
    ├── validate-services.sh
    └── README.md

/etc/systemd/system/           # Systemd service files (after installation)
├── rag-api.service
└── rag-frontend.service

/var/log/                      # Log directories
├── rag-api/
└── rag-frontend/
```

## Verification Commands

After deployment, verify the correct paths are being used:

```bash
# Check service files reference correct paths
sudo grep -r "/opt/chainlit_rag" /etc/systemd/system/rag-*.service

# Check if application directory exists
ls -la /opt/chainlit_rag/

# Check environment file
cat /opt/chainlit_rag/.env

# Check virtual environment
ls -la /opt/chainlit_rag/venv/bin/

# Validate services
sudo systemd-services/validate-services.sh
```

## Installation Commands (Updated)

```bash
# 1. Copy your application to the correct location
sudo cp -r /path/to/your/chainlit_rag /opt/

# 2. Install systemd services
sudo systemd-services/install-services.sh install

# 3. Configure environment
sudo nano /opt/chainlit_rag/.env

# 4. Start services
sudo systemd-services/install-services.sh start

# 5. Validate deployment
sudo systemd-services/validate-services.sh
```

## Key Changes Summary

1. **All hardcoded `/opt/rag-app` paths** → `/opt/chainlit_rag`
2. **Virtual environment paths** → Updated to new location
3. **Environment file paths** → Updated to new location
4. **Log directory references** → Remain in `/var/log/` (standard location)
5. **Working directories** → Updated in both service files
6. **Security ReadWritePaths** → Updated to allow access to new location

## No Changes Required

- **Log directories**: Still in `/var/log/rag-api/` and `/var/log/rag-frontend/`
- **Service names**: Still `rag-api.service` and `rag-frontend.service`
- **Ports**: Still 8888 (API) and 8000 (Frontend)
- **User/Group**: Still `raguser:raguser`

All path references have been systematically updated to use your actual folder name "chainlit_rag". The services are now ready for deployment with your correct directory structure.
