"""
Tests for the image type classifier.
"""
import os
import io
import pytest
from unittest.mock import MagicMock, patch
import numpy as np
from PIL import Image, ImageDraw
import cv2

from image_analysis.classifier import ImageTypeClassifier, ImageFeature
from image_analysis.models import ImageType
from image_analysis.ocr import OCRService


def create_test_image(format="PNG", size=(100, 100), color=(255, 255, 255)):
    """Create a test image for testing."""
    img = Image.new("RGB", size, color)
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format=format)
    img_byte_arr.seek(0)
    return img_byte_arr


def create_diagram_image():
    """Create a simple diagram-like test image."""
    img = Image.new("RGB", (300, 200), (255, 255, 255))
    draw = ImageDraw.Draw(img)
    
    # Draw some shapes that look like a diagram
    draw.rectangle((50, 50, 100, 100), outline=(0, 0, 0))
    draw.rectangle((150, 50, 200, 100), outline=(0, 0, 0))
    draw.line((100, 75, 150, 75), fill=(0, 0, 0))
    draw.ellipse((200, 120, 250, 170), outline=(0, 0, 0))
    
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format="PNG")
    img_byte_arr.seek(0)
    return img_byte_arr


def create_error_image():
    """Create a simple error-like test image."""
    img = Image.new("RGB", (300, 200), (240, 240, 240))
    draw = ImageDraw.Draw(img)
    
    # Draw a dialog box with error-like UI
    draw.rectangle((50, 50, 250, 150), fill=(255, 255, 255), outline=(200, 200, 200))
    draw.rectangle((50, 50, 250, 70), fill=(220, 0, 0))
    
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format="PNG")
    img_byte_arr.seek(0)
    return img_byte_arr


def save_test_images():
    """Save test images to disk for testing."""
    os.makedirs("test_data", exist_ok=True)
    
    # Save diagram image
    with open("test_data/test_diagram.png", "wb") as f:
        f.write(create_diagram_image().getvalue())
    
    # Save error image
    with open("test_data/test_error.png", "wb") as f:
        f.write(create_error_image().getvalue())
    
    # Save blank image
    with open("test_data/test_blank.png", "wb") as f:
        f.write(create_test_image().getvalue())


@pytest.fixture(scope="module", autouse=True)
def setup_test_images():
    """Set up test images for all tests."""
    save_test_images()
    yield
    # Cleanup can be added here if needed


def test_init():
    """Test initialization of the classifier."""
    # Test with OCR service
    ocr_service = MagicMock()
    classifier = ImageTypeClassifier(ocr_service)
    assert classifier.ocr_service == ocr_service
    
    # Test without OCR service
    classifier = ImageTypeClassifier()
    assert classifier.ocr_service is None


def test_classify_image_with_text():
    """Test image classification with pre-extracted text."""
    classifier = ImageTypeClassifier()
    
    # Test with diagram text
    diagram_text = "Architecture diagram showing system components and their relationships"
    image_type, confidence, features = classifier.classify_image("test_data/test_blank.png", diagram_text)
    assert image_type == ImageType.ARCHITECTURE
    assert confidence > 0.5
    
    # Test with error text
    error_text = "Error: Connection failed. The server returned an invalid response."
    image_type, confidence, features = classifier.classify_image("test_data/test_blank.png", error_text)
    assert image_type == ImageType.ERROR
    assert confidence > 0.5


@patch("image_analysis.classifier.cv2.imread")
@patch("image_analysis.classifier.Image.open")
def test_extract_features(mock_pil_open, mock_cv_imread):
    """Test feature extraction from images."""
    # Mock PIL Image
    mock_img = MagicMock()
    mock_img.mode = "RGB"
    mock_img.size = (100, 100)
    mock_img.resize.return_value = mock_img
    mock_pil_open.return_value = mock_img
    
    # Mock OpenCV image
    mock_cv_img = np.zeros((100, 100, 3), dtype=np.uint8)
    mock_cv_imread.return_value = mock_cv_img
    
    # Create classifier
    classifier = ImageTypeClassifier()
    
    # Test with text content
    features = classifier._extract_features("dummy_path.png", "Sample text content")
    
    # Check that all features are present
    for feature in ImageFeature:
        assert feature in features
    
    # Check that all feature values are between 0 and 1
    for feature, value in features.items():
        assert 0 <= value <= 1


def test_calculate_text_density():
    """Test calculation of text density."""
    classifier = ImageTypeClassifier()
    
    # Test with empty text
    density = classifier._calculate_text_density("", (100, 100))
    assert density == 0.0
    
    # Test with short text
    density = classifier._calculate_text_density("Short text", (100, 100))
    assert 0 <= density <= 1
    
    # Test with long text
    long_text = "A" * 1000
    density = classifier._calculate_text_density(long_text, (100, 100))
    assert density == 1.0  # Should be capped at 1.0


def test_calculate_keyword_presence():
    """Test calculation of keyword presence."""
    classifier = ImageTypeClassifier()
    
    # Test with empty text
    score = classifier._calculate_keyword_presence("", classifier.ERROR_KEYWORDS)
    assert score == 0.0
    
    # Test with text containing error keywords
    error_text = "Error: Connection failed. Exception occurred."
    score = classifier._calculate_keyword_presence(error_text, classifier.ERROR_KEYWORDS)
    assert score > 0.0
    
    # Test with text containing diagram keywords
    diagram_text = "Architecture diagram showing system components."
    score = classifier._calculate_keyword_presence(diagram_text, classifier.DIAGRAM_KEYWORDS)
    assert score > 0.0


@patch("image_analysis.classifier.ImageStat.Stat")
def test_calculate_color_diversity(mock_stat):
    """Test calculation of color diversity."""
    # Mock image statistics
    mock_stat_instance = MagicMock()
    mock_stat_instance.var = [1000, 1000, 1000]  # Medium variance
    mock_stat.return_value = mock_stat_instance
    
    # Mock image
    mock_img = MagicMock()
    mock_img.mode = "RGB"
    mock_img.convert.return_value = mock_img
    mock_img.resize.return_value = mock_img
    
    classifier = ImageTypeClassifier()
    diversity = classifier._calculate_color_diversity(mock_img)
    
    assert 0 <= diversity <= 1


@patch("image_analysis.classifier.cv2.Canny")
@patch("image_analysis.classifier.cv2.GaussianBlur")
@patch("image_analysis.classifier.cv2.cvtColor")
def test_calculate_edge_density(mock_cvtcolor, mock_blur, mock_canny):
    """Test calculation of edge density."""
    # Mock OpenCV functions
    mock_cvtcolor.return_value = np.zeros((100, 100), dtype=np.uint8)
    mock_blur.return_value = np.zeros((100, 100), dtype=np.uint8)
    
    # Create an edge map with 10% edge pixels
    edge_map = np.zeros((100, 100), dtype=np.uint8)
    edge_map[:10, :] = 255
    mock_canny.return_value = edge_map
    
    classifier = ImageTypeClassifier()
    cv_img = np.zeros((100, 100, 3), dtype=np.uint8)
    density = classifier._calculate_edge_density(cv_img)
    
    assert 0 <= density <= 1


@patch("image_analysis.classifier.cv2.findContours")
@patch("image_analysis.classifier.cv2.threshold")
@patch("image_analysis.classifier.cv2.GaussianBlur")
@patch("image_analysis.classifier.cv2.cvtColor")
def test_detect_shapes(mock_cvtcolor, mock_blur, mock_threshold, mock_findcontours):
    """Test detection of shapes."""
    # Mock OpenCV functions
    mock_cvtcolor.return_value = np.zeros((100, 100), dtype=np.uint8)
    mock_blur.return_value = np.zeros((100, 100), dtype=np.uint8)
    mock_threshold.return_value = (None, np.zeros((100, 100), dtype=np.uint8))
    
    # Create some mock contours
    contours = [
        np.array([[[0, 0]], [[0, 10]], [[10, 10]], [[10, 0]]]),  # Rectangle
        np.array([[[20, 20]], [[20, 30]], [[30, 30]]]),  # Triangle
    ]
    mock_findcontours.return_value = (contours, None)
    
    classifier = ImageTypeClassifier()
    cv_img = np.zeros((100, 100, 3), dtype=np.uint8)
    
    # Mock contour area to be large enough
    with patch("image_analysis.classifier.cv2.contourArea", return_value=100):
        # Mock arc length
        with patch("image_analysis.classifier.cv2.arcLength", return_value=40):
            # Mock approx poly DP to return the same contours
            with patch("image_analysis.classifier.cv2.approxPolyDP", side_effect=lambda c, *args: c):
                score = classifier._detect_shapes(cv_img)
                assert 0 <= score <= 1


@patch("image_analysis.classifier.cv2.findContours")
@patch("image_analysis.classifier.cv2.adaptiveThreshold")
@patch("image_analysis.classifier.cv2.GaussianBlur")
@patch("image_analysis.classifier.cv2.cvtColor")
def test_detect_ui_elements(mock_cvtcolor, mock_blur, mock_threshold, mock_findcontours):
    """Test detection of UI elements."""
    # Mock OpenCV functions
    mock_cvtcolor.return_value = np.zeros((100, 100), dtype=np.uint8)
    mock_blur.return_value = np.zeros((100, 100), dtype=np.uint8)
    mock_threshold.return_value = np.zeros((100, 100), dtype=np.uint8)
    
    # Create some mock contours for UI elements
    contours = [
        np.array([[[0, 0]], [[0, 10]], [[50, 10]], [[50, 0]]]),  # Button-like
        np.array([[[0, 20]], [[0, 30]], [[100, 30]], [[100, 20]]]),  # Text field-like
    ]
    mock_findcontours.return_value = (contours, None)
    
    classifier = ImageTypeClassifier()
    cv_img = np.zeros((100, 100, 3), dtype=np.uint8)
    
    # Mock contour area to be large enough
    with patch("image_analysis.classifier.cv2.contourArea", return_value=100):
        # Mock bounding rect to return reasonable values
        with patch("image_analysis.classifier.cv2.boundingRect", return_value=(0, 0, 50, 10)):
            # Mock arc length
            with patch("image_analysis.classifier.cv2.arcLength", return_value=120):
                # Mock approx poly DP to return 4 points (rectangle)
                with patch("image_analysis.classifier.cv2.approxPolyDP", return_value=np.array([[[0, 0]], [[0, 10]], [[50, 10]], [[50, 0]]])):
                    score = classifier._detect_ui_elements(cv_img)
                    assert 0 <= score <= 1


def test_calculate_architecture_score():
    """Test calculation of architecture score."""
    classifier = ImageTypeClassifier()
    
    # Test with features favoring architecture
    features = {
        ImageFeature.TEXT_DENSITY: 0.3,
        ImageFeature.COLOR_DIVERSITY: 0.7,
        ImageFeature.EDGE_DENSITY: 0.8,
        ImageFeature.SHAPE_PRESENCE: 0.9,
        ImageFeature.ERROR_KEYWORDS: 0.1,
        ImageFeature.DIAGRAM_KEYWORDS: 0.8,
        ImageFeature.UI_ELEMENTS: 0.2
    }
    
    score = classifier._calculate_architecture_score(features)
    assert 0.5 < score <= 1.0
    
    # Test with features not favoring architecture
    features = {
        ImageFeature.TEXT_DENSITY: 0.3,
        ImageFeature.COLOR_DIVERSITY: 0.2,
        ImageFeature.EDGE_DENSITY: 0.1,
        ImageFeature.SHAPE_PRESENCE: 0.1,
        ImageFeature.ERROR_KEYWORDS: 0.9,
        ImageFeature.DIAGRAM_KEYWORDS: 0.1,
        ImageFeature.UI_ELEMENTS: 0.8
    }
    
    score = classifier._calculate_architecture_score(features)
    assert 0 <= score < 0.5


def test_calculate_error_score():
    """Test calculation of error score."""
    classifier = ImageTypeClassifier()
    
    # Test with features favoring error
    features = {
        ImageFeature.TEXT_DENSITY: 0.7,
        ImageFeature.COLOR_DIVERSITY: 0.2,
        ImageFeature.EDGE_DENSITY: 0.1,
        ImageFeature.SHAPE_PRESENCE: 0.1,
        ImageFeature.ERROR_KEYWORDS: 0.9,
        ImageFeature.DIAGRAM_KEYWORDS: 0.1,
        ImageFeature.UI_ELEMENTS: 0.8
    }
    
    score = classifier._calculate_error_score(features)
    assert 0.5 < score <= 1.0
    
    # Test with features not favoring error
    features = {
        ImageFeature.TEXT_DENSITY: 0.3,
        ImageFeature.COLOR_DIVERSITY: 0.8,
        ImageFeature.EDGE_DENSITY: 0.9,
        ImageFeature.SHAPE_PRESENCE: 0.9,
        ImageFeature.ERROR_KEYWORDS: 0.1,
        ImageFeature.DIAGRAM_KEYWORDS: 0.8,
        ImageFeature.UI_ELEMENTS: 0.1
    }
    
    score = classifier._calculate_error_score(features)
    assert 0 <= score < 0.5


def test_integration_with_test_images():
    """Integration test with actual test images."""
    # Skip if test images don't exist
    if not os.path.exists("test_data/test_diagram.png") or not os.path.exists("test_data/test_error.png"):
        pytest.skip("Test images not available")
    
    classifier = ImageTypeClassifier()
    
    # Test with diagram image and mock text
    diagram_text = "Architecture diagram showing components and connections"
    image_type, confidence, features = classifier.classify_image("test_data/test_diagram.png", diagram_text)
    assert image_type == ImageType.ARCHITECTURE
    assert confidence > 0.5
    
    # Test with error image and mock text
    error_text = "Error: Connection failed. The server returned an invalid response."
    image_type, confidence, features = classifier.classify_image("test_data/test_error.png", error_text)
    assert image_type == ImageType.ERROR
    assert confidence > 0.5