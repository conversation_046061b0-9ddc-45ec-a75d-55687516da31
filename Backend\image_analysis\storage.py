"""
Storage service for temporary image storage and analysis results.
"""
import os
import uuid
import shutil
import logging
import hashlib
import threading
import time
import base64
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from fastapi import UploadFile
from cryptography.fernet import <PERSON>rnet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from .models import ImageAnalysisRequest, ImageAnalysisResult
from .validators import ImageValidator


# Configure logging
logger = logging.getLogger(__name__)


class SecureFileHandler:
    """
    Handles secure file operations with encryption.
    """
    
    def __init__(self, secret_key: Optional[str] = None):
        """
        Initialize the secure file handler.
        
        Args:
            secret_key: Secret key for encryption, generated if not provided
        """
        # Generate or use provided secret key
        if secret_key:
            # Use provided key (must be 32 url-safe base64-encoded bytes)
            self.secret_key = secret_key.encode()
        else:
            # Generate a key from os.urandom
            self.secret_key = base64.urlsafe_b64encode(os.urandom(32))
        
        # Initialize Fernet cipher
        self.cipher = Fernet(self.secret_key)
    
    def encrypt_file(self, source_path: str, target_path: str) -> bool:
        """
        Encrypt a file.
        
        Args:
            source_path: Path to the source file
            target_path: Path to save the encrypted file
            
        Returns:
            bool: True if encryption was successful
        """
        try:
            with open(source_path, 'rb') as f:
                data = f.read()
            
            # Encrypt the data
            encrypted_data = self.cipher.encrypt(data)
            
            # Write encrypted data to target file
            with open(target_path, 'wb') as f:
                f.write(encrypted_data)
                
            return True
        except Exception as e:
            logger.error(f"Error encrypting file: {str(e)}")
            return False
    
    def decrypt_file(self, source_path: str, target_path: str) -> bool:
        """
        Decrypt a file.
        
        Args:
            source_path: Path to the encrypted file
            target_path: Path to save the decrypted file
            
        Returns:
            bool: True if decryption was successful
        """
        try:
            with open(source_path, 'rb') as f:
                encrypted_data = f.read()
            
            # Decrypt the data
            decrypted_data = self.cipher.decrypt(encrypted_data)
            
            # Write decrypted data to target file
            with open(target_path, 'wb') as f:
                f.write(decrypted_data)
                
            return True
        except Exception as e:
            logger.error(f"Error decrypting file: {str(e)}")
            return False
    
    def generate_secure_token(self, data: str, expiry_seconds: int = 3600) -> str:
        """
        Generate a secure token with expiry.
        
        Args:
            data: Data to encode in the token
            expiry_seconds: Token validity in seconds
            
        Returns:
            str: Secure token
        """
        expiry = int(time.time()) + expiry_seconds
        token_data = f"{data}|{expiry}"
        return base64.urlsafe_b64encode(
            self.cipher.encrypt(token_data.encode())
        ).decode()
    
    def validate_secure_token(self, token: str) -> Optional[str]:
        """
        Validate a secure token and extract the data.
        
        Args:
            token: Secure token to validate
            
        Returns:
            Optional[str]: Extracted data if valid, None otherwise
        """
        try:
            # Decode and decrypt the token
            encrypted_data = base64.urlsafe_b64decode(token)
            decrypted_data = self.cipher.decrypt(encrypted_data).decode()
            
            # Split data and expiry
            data, expiry_str = decrypted_data.split('|')
            expiry = int(expiry_str)
            
            # Check if token has expired
            if time.time() > expiry:
                logger.warning("Token has expired")
                return None
                
            return data
        except Exception as e:
            logger.error(f"Error validating token: {str(e)}")
            return None


class CleanupThread(threading.Thread):
    """
    Background thread for periodic cleanup of old files.
    """
    
    def __init__(self, storage, interval_seconds: int = 3600):
        """
        Initialize the cleanup thread.
        
        Args:
            storage: Storage instance with cleanup_old_images method
            interval_seconds: Cleanup interval in seconds
        """
        super().__init__(daemon=True)
        self.storage = storage
        self.interval_seconds = interval_seconds
        self.stop_event = threading.Event()
    
    def run(self):
        """Run the cleanup thread."""
        while not self.stop_event.is_set():
            try:
                # Run cleanup
                self.storage.cleanup_old_images()
            except Exception as e:
                logger.error(f"Error in cleanup thread: {str(e)}")
            
            # Wait for the next interval or until stopped
            self.stop_event.wait(self.interval_seconds)
    
    def stop(self):
        """Stop the cleanup thread."""
        self.stop_event.set()


class TemporaryImageStore:
    """
    Service for storing uploaded images temporarily.
    """
    
    def __init__(self, base_dir: str = "temp_images", retention_hours: int = 24, 
                 secure_storage: bool = False, cleanup_interval: int = 3600):
        """
        Initialize the temporary image store.
        
        Args:
            base_dir: Base directory for storing images
            retention_hours: Number of hours to retain images before cleanup
            secure_storage: Whether to encrypt stored images
            cleanup_interval: Interval for automatic cleanup in seconds
        """
        self.base_dir = base_dir
        self.retention_hours = retention_hours
        self.secure_storage = secure_storage
        
        # Create secure file handler if secure storage is enabled
        self.secure_handler = SecureFileHandler() if secure_storage else None
        
        # Create encrypted storage directory if needed
        self.encrypted_dir = os.path.join(base_dir, "encrypted") if secure_storage else None
        
        # Ensure directories exist
        os.makedirs(self.base_dir, exist_ok=True)
        if self.encrypted_dir:
            os.makedirs(self.encrypted_dir, exist_ok=True)
        
        # Start cleanup thread
        self.cleanup_thread = CleanupThread(self, cleanup_interval)
        self.cleanup_thread.start()
    
    async def store_image(self, file: UploadFile) -> str:
        """
        Store an uploaded image file.
        
        Args:
            file: The uploaded file to store
            
        Returns:
            str: Path to the stored image
        """
        # Generate a unique filename
        sanitized_filename = ImageValidator.sanitize_filename(file.filename or "unknown")
        file_ext = os.path.splitext(sanitized_filename)[1] or ".png"
        unique_id = str(uuid.uuid4())
        unique_filename = f"{unique_id}{file_ext}"
        file_path = os.path.join(self.base_dir, unique_filename)
        
        # Save the file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Encrypt the file if secure storage is enabled
        if self.secure_storage and self.secure_handler and self.encrypted_dir:
            encrypted_path = os.path.join(self.encrypted_dir, f"{unique_id}.enc")
            if self.secure_handler.encrypt_file(file_path, encrypted_path):
                # Delete the original file after successful encryption
                os.remove(file_path)
                file_path = encrypted_path
        
        logger.info(f"Stored image at {file_path}")
        return file_path
        
    def generate_secure_url(self, image_path: str, expiry_seconds: int = 3600) -> Optional[str]:
        """
        Generate a secure URL for accessing a stored image.
        
        Args:
            image_path: Path to the image
            expiry_seconds: URL validity in seconds
            
        Returns:
            Optional[str]: Secure URL token if successful, None otherwise
        """
        if not self.secure_handler:
            logger.warning("Secure URL generation requires secure storage to be enabled")
            return None
            
        try:
            # Generate a token containing the image path
            return self.secure_handler.generate_secure_token(image_path, expiry_seconds)
        except Exception as e:
            logger.error(f"Error generating secure URL: {str(e)}")
            return None
    
    def get_image_by_token(self, token: str) -> Optional[str]:
        """
        Get an image using a secure token.
        
        Args:
            token: Secure token for the image
            
        Returns:
            Optional[str]: Path to a temporary decrypted copy of the image if valid, None otherwise
        """
        if not self.secure_handler:
            logger.warning("Secure image access requires secure storage to be enabled")
            return None
            
        try:
            # Validate the token and get the image path
            image_path = self.secure_handler.validate_secure_token(token)
            if not image_path or not os.path.exists(image_path):
                return None
                
            # If the image is encrypted, decrypt it to a temporary file
            if image_path.endswith('.enc') and self.encrypted_dir and image_path.startswith(self.encrypted_dir):
                # Create a temporary file for the decrypted image
                temp_path = os.path.join(self.base_dir, f"temp_{uuid.uuid4()}.png")
                
                # Decrypt the image
                if self.secure_handler.decrypt_file(image_path, temp_path):
                    return temp_path
                    
            return image_path
        except Exception as e:
            logger.error(f"Error accessing image by token: {str(e)}")
            return None
    
    def get_image_path(self, image_id: str) -> str:
        """
        Get the path to a stored image.
        
        Args:
            image_id: ID of the image
            
        Returns:
            str: Path to the image
        """
        return os.path.join(self.base_dir, image_id)
    
    def delete_image(self, image_path: str) -> bool:
        """
        Delete a stored image.
        
        Args:
            image_path: Path to the image to delete
            
        Returns:
            bool: True if deletion was successful
        """
        try:
            if os.path.exists(image_path):
                os.remove(image_path)
                logger.info(f"Deleted image at {image_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting image {image_path}: {str(e)}")
            return False
    
    def cleanup_old_images(self) -> int:
        """
        Clean up images older than the retention period.
        
        Returns:
            int: Number of images deleted
        """
        deleted_count = 0
        cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)
        
        for filename in os.listdir(self.base_dir):
            file_path = os.path.join(self.base_dir, filename)
            
            # Skip if not a file
            if not os.path.isfile(file_path):
                continue
                
            # Check file modification time
            mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            if mod_time < cutoff_time:
                if self.delete_image(file_path):
                    deleted_count += 1
        
        logger.info(f"Cleaned up {deleted_count} old images")
        return deleted_count


class AnalysisResultStore:
    """
    Service for storing analysis results.
    """
    
    def __init__(self, retention_hours: int = 24):
        """
        Initialize the analysis result store.
        
        Args:
            retention_hours: Number of hours to retain results before cleanup
        """
        self.results: Dict[str, ImageAnalysisResult] = {}
        self.requests: Dict[str, ImageAnalysisRequest] = {}
        self.retention_hours = retention_hours
    
    def store_request(self, request: ImageAnalysisRequest) -> str:
        """
        Store an analysis request.
        
        Args:
            request: The request to store
            
        Returns:
            str: ID of the stored request
        """
        self.requests[request.id] = request
        return request.id
    
    def get_request(self, request_id: str) -> Optional[ImageAnalysisRequest]:
        """
        Get an analysis request by ID.
        
        Args:
            request_id: ID of the request
            
        Returns:
            Optional[ImageAnalysisRequest]: The request if found, None otherwise
        """
        return self.requests.get(request_id)
    
    def update_request(self, request: ImageAnalysisRequest) -> bool:
        """
        Update an existing analysis request.
        
        Args:
            request: The updated request
            
        Returns:
            bool: True if update was successful
        """
        if request.id in self.requests:
            self.requests[request.id] = request
            return True
        return False
    
    def store_result(self, result: ImageAnalysisResult) -> str:
        """
        Store an analysis result.
        
        Args:
            result: The result to store
            
        Returns:
            str: ID of the stored result
        """
        self.results[result.id] = result
        return result.id
    
    def get_result(self, result_id: str) -> Optional[ImageAnalysisResult]:
        """
        Get an analysis result by ID.
        
        Args:
            result_id: ID of the result
            
        Returns:
            Optional[ImageAnalysisResult]: The result if found, None otherwise
        """
        return self.results.get(result_id)
    
    def get_result_by_request(self, request_id: str) -> Optional[ImageAnalysisResult]:
        """
        Get an analysis result by request ID.
        
        Args:
            request_id: ID of the request
            
        Returns:
            Optional[ImageAnalysisResult]: The result if found, None otherwise
        """
        for result in self.results.values():
            if result.request_id == request_id:
                return result
        return None
    
    def delete_result(self, result_id: str) -> bool:
        """
        Delete an analysis result.
        
        Args:
            result_id: ID of the result to delete
            
        Returns:
            bool: True if deletion was successful
        """
        if result_id in self.results:
            del self.results[result_id]
            return True
        return False
    
    def delete_request_and_result(self, request_id: str) -> bool:
        """
        Delete a request and its associated result.
        
        Args:
            request_id: ID of the request to delete
            
        Returns:
            bool: True if deletion was successful
        """
        success = False
        
        # Delete the request
        if request_id in self.requests:
            del self.requests[request_id]
            success = True
        
        # Find and delete the associated result
        for result_id, result in list(self.results.items()):
            if result.request_id == request_id:
                del self.results[result_id]
                success = True
                break
        
        return success
    
    def cleanup_old_results(self) -> int:
        """
        Clean up results older than the retention period.
        
        Returns:
            int: Number of results deleted
        """
        deleted_count = 0
        cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)
        
        # Clean up old requests
        for request_id, request in list(self.requests.items()):
            if request.timestamp < cutoff_time:
                if self.delete_request_and_result(request_id):
                    deleted_count += 1
        
        return deleted_count