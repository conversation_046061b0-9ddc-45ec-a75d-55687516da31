{"timestamp": "2025-07-25T15:33:28.299019", "test_suite": "Image Endpoint Tests", "environment": {"python_version": "3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]", "working_directory": "D:\\New folder (4)\\RAG_Quadrant\\Backend", "backend_directory": "D:\\New folder (4)\\RAG_Quadrant\\Backend"}, "results": {"total": 24, "passed": 15, "failed": 9, "success_rate": 62.5, "passed_tests": ["test_query_image_valid_png_success", "test_query_image_valid_jpeg_success", "test_query_image_no_text_extracted", "test_query_image_no_file_uploaded", "test_analyze_image_success_with_aws_credentials", "test_analyze_image_missing_aws_credentials", "test_analyze_image_bedrock_error", "test_analyze_image_openrouter_success", "test_analyze_image_openrouter_missing_api_key", "test_analyze_image_openrouter_api_error", "test_image_analysis_router_get_result_not_found", "test_image_analysis_router_get_image_success", "test_all_endpoints_no_file", "test_health_check", "test_root_endpoint"], "failed_tests": [["test_query_image_invalid_file_type", ""], ["test_query_image_empty_file", ""], ["test_query_image_oversized_file", ""], ["test_query_image_with_retrieval_config", ""], ["test_analyze_image_openrouter_with_custom_prompt", ""], ["test_image_analysis_router_upload_success", "<class 'image_analysis.processor.ImageProcessor'> does not have the attribute 'process_image_async'"], ["test_image_analysis_router_get_result_success", ""], ["test_image_analysis_router_delete_success", "<class 'image_analysis.storage.AnalysisResultStore'> does not have the attribute 'delete_request'"], ["test_all_endpoints_invalid_file_type", "Endpoint /query/image should reject non-image files"]]}}