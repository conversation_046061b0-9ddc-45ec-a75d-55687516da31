#!/usr/bin/env python3
"""
Test runner script for image endpoint tests.

This script runs the comprehensive test suite for all image-related endpoints
and generates a detailed report of the results.

Usage:
    python run_image_tests.py

The script will:
1. Set up test environment
2. Run all image endpoint tests
3. Generate a comprehensive report
4. Save results to a JSON file
5. Exit with appropriate status code
"""

import os
import sys
import json
import logging
from datetime import datetime

# Add the Backend directory to the Python path
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, backend_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('image_endpoint_tests.log')
    ]
)

logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if all required dependencies are available."""
    logger.info("Checking dependencies...")
    
    required_modules = [
        'fastapi',
        'pytest', 
        'PIL',
        'requests',
        'uvicorn'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            logger.info(f"✓ {module} is available")
        except ImportError:
            missing_modules.append(module)
            logger.error(f"✗ {module} is missing")
    
    if missing_modules:
        logger.error(f"Missing required modules: {', '.join(missing_modules)}")
        logger.error("Please install missing dependencies with: pip install -r requirements.txt")
        return False
    
    logger.info("All dependencies are available")
    return True

def check_environment():
    """Check if the environment is properly configured."""
    logger.info("Checking environment configuration...")
    
    # Check if main.py exists
    main_py_path = os.path.join(backend_dir, 'main.py')
    if not os.path.exists(main_py_path):
        logger.error("main.py not found in Backend directory")
        return False
    
    # Check if image_analysis module exists
    image_analysis_path = os.path.join(backend_dir, 'image_analysis')
    if not os.path.exists(image_analysis_path):
        logger.warning("image_analysis module not found - some tests may fail")
    
    # Check if required utility modules exist
    required_files = ['aws_utils.py', 'openrouter_utils.py']
    for file in required_files:
        file_path = os.path.join(backend_dir, file)
        if not os.path.exists(file_path):
            logger.warning(f"{file} not found - related tests may fail")
    
    logger.info("Environment check completed")
    return True

def save_test_results(results, filename='test_results.json'):
    """Save test results to a JSON file."""
    try:
        results_with_metadata = {
            'timestamp': datetime.now().isoformat(),
            'test_suite': 'Image Endpoint Tests',
            'environment': {
                'python_version': sys.version,
                'working_directory': os.getcwd(),
                'backend_directory': backend_dir
            },
            'results': results
        }
        
        results_file = os.path.join(backend_dir, filename)
        with open(results_file, 'w') as f:
            json.dump(results_with_metadata, f, indent=2)
        
        logger.info(f"Test results saved to {results_file}")
        return True
    except Exception as e:
        logger.error(f"Failed to save test results: {e}")
        return False

def main():
    """Main test runner function."""
    logger.info("=" * 80)
    logger.info("IMAGE ENDPOINT TEST RUNNER")
    logger.info("=" * 80)
    
    # Check dependencies
    if not check_dependencies():
        logger.error("Dependency check failed. Exiting.")
        return 1
    
    # Check environment
    if not check_environment():
        logger.error("Environment check failed. Exiting.")
        return 1
    
    try:
        # Import and run tests
        logger.info("Importing test module...")
        from test_image_endpoints import run_all_tests
        
        logger.info("Starting test execution...")
        results = run_all_tests()
        
        # Save results
        save_test_results(results)
        
        # Print final summary
        logger.info("=" * 80)
        logger.info("FINAL TEST SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Tests executed: {results['total']}")
        logger.info(f"Passed: {results['passed']} ({results['success_rate']:.1f}%)")
        logger.info(f"Failed: {results['failed']}")
        
        if results['failed'] == 0:
            logger.info("🎉 All tests passed successfully!")
            return 0
        else:
            logger.error(f"❌ {results['failed']} test(s) failed")
            return 1
            
    except ImportError as e:
        logger.error(f"Failed to import test module: {e}")
        logger.error("Make sure test_image_endpoints.py is in the same directory")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error during test execution: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
