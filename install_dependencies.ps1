# PowerShell script to install all required dependencies for RAG Quadrant

# Activate the virtual environment
Write-Host "Activating virtual environment..." -ForegroundColor Green
& "D:/New folder (4)/RAG_Quadrant/venv/Scripts/Activate.ps1"

# Check if the virtual environment is activated
if (-not $env:VIRTUAL_ENV) {
    Write-Host "Virtual environment not activated. Please check the path and try again." -ForegroundColor Red
    exit 1
}

Write-Host "Installing core dependencies..." -ForegroundColor Green
pip install -U pip setuptools wheel

# Install core packages
Write-Host "Installing LangChain and related packages..." -ForegroundColor Green
pip install langchain langchain-community langchain-core

# Install vector database
Write-Host "Installing Qdrant and vector store utilities..." -ForegroundColor Green
pip install qdrant-client

# Install AWS packages
Write-Host "Installing AWS packages..." -ForegroundColor Green
pip install boto3 amazon-bedrock-runtime

# Install FastAPI and web server
Write-Host "Installing FastAPI and web server..." -ForegroundColor Green
pip install fastapi uvicorn python-multipart

# Install image processing
Write-Host "Installing image processing packages..." -ForegroundColor Green
pip install pillow pytesseract

# Install Google Gemini packages
Write-Host "Installing Google Gemini packages..." -ForegroundColor Green
pip install google-generativeai

# Install other utilities
Write-Host "Installing other utilities..." -ForegroundColor Green
pip install python-dotenv requests aiohttp

# Install Chainlit for frontend
Write-Host "Installing Chainlit for frontend..." -ForegroundColor Green
pip install chainlit

# Install document processing
Write-Host "Installing document processing packages..." -ForegroundColor Green
pip install unstructured pdf2image

# Verify installations
Write-Host "Verifying installations..." -ForegroundColor Green
pip list

Write-Host "Installation complete!" -ForegroundColor Green
Write-Host "You can now run the backend with: python Backend/main.py" -ForegroundColor Green
Write-Host "And the frontend with: cd Frontend && chainlit run chainlit_app.py" -ForegroundColor Green
