#!/usr/bin/env python3
"""
Debug script to simulate the frontend behavior and find the issue.
"""

import asyncio
import aiohttp
import json
from PIL import Image, ImageDraw
from io import BytesIO

# Same timeout as frontend
TIMEOUT = aiohttp.ClientTimeout(total=120)
API_BASE_URL = "http://localhost:8888"

def create_test_image():
    """Create a test image."""
    img = Image.new('RGB', (200, 150), color='white')
    draw = ImageDraw.Draw(img)
    draw.text((20, 20), "AWS Test", fill='black')
    draw.rectangle([20, 50, 80, 90], outline='blue', width=2)
    draw.text((25, 65), "EC2", fill='blue')
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

async def analyze_image_with_ai_debug(image_bytes: bytes, use_gemini: bool = False, custom_prompt: str = None):
    """
    Debug version of the frontend's analyze_image_with_ai function.
    """
    print(f"DEBUG: Starting analysis with use_gemini={use_gemini}")
    
    # Choose the appropriate endpoint based on the use_gemini flag
    if use_gemini:
        url = f"{API_BASE_URL}/analyze/image/gemini"
    else:
        url = f"{API_BASE_URL}/analyze/image"
    
    print(f"DEBUG: Using URL: {url}")
        
    data = aiohttp.FormData()
    data.add_field('file', image_bytes, filename='upload.png', content_type='image/png')
    
    # Add custom prompt for Gemini
    if use_gemini and custom_prompt:
        data.add_field('prompt', custom_prompt)
        print(f"DEBUG: Added custom prompt: {custom_prompt[:50]}...")
    
    async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
        async with session.post(url, data=data) as response:
            print(f"DEBUG: HTTP response status: {response.status}")
            print(f"DEBUG: Response headers: {dict(response.headers)}")
            
            if response.status == 200:
                result = await response.json()
                print(f"DEBUG: Response keys: {list(result.keys())}")
                print(f"DEBUG: Response status field: {result.get('status')}")
                
                if 'analysis' in result:
                    analysis_value = result['analysis']
                    print(f"DEBUG: Analysis field type: {type(analysis_value)}")
                    print(f"DEBUG: Analysis field length: {len(analysis_value) if analysis_value else 0}")
                    print(f"DEBUG: Analysis is empty: {not analysis_value}")
                    print(f"DEBUG: Analysis is string: {isinstance(analysis_value, str)}")
                    if analysis_value:
                        print(f"DEBUG: Analysis preview: {repr(analysis_value[:100])}")
                    else:
                        print("DEBUG: Analysis field is empty!")
                else:
                    print("DEBUG: No 'analysis' key in response!")
                
                return result
            else:
                error_text = await response.text()
                print(f"DEBUG: HTTP error response: {error_text}")
                raise Exception(f"Image analysis failed: {error_text}")

async def simulate_frontend_logic():
    """Simulate the exact frontend logic."""
    print("=" * 60)
    print("SIMULATING FRONTEND LOGIC")
    print("=" * 60)
    
    # Create test image
    image_bytes = create_test_image()
    print(f"Created test image: {len(image_bytes)} bytes")
    
    # Simulate the frontend analysis call
    use_gemini = True  # Simulating 'gemini' choice
    analysis_result = await analyze_image_with_ai_debug(image_bytes, use_gemini)
    
    print("\n" + "=" * 40)
    print("FRONTEND PROCESSING")
    print("=" * 40)
    
    # Format and display the analysis result (same as frontend)
    if analysis_result.get("status") == "error":
        error_msg = analysis_result.get("error", "Unknown error")
        print(f"❌ Frontend would show error: {error_msg}")
    else:
        # Extract text content if available
        extracted_text = analysis_result.get("extracted_text", "").strip()
        extracted_text_section = ""
        if extracted_text:
            extracted_text_section = f"""
## 📝 **Extracted Text**

```
{extracted_text[:500]}
```
{'' if len(extracted_text) <= 500 else '*(text truncated)*'}

"""
        
        # Format the analysis (same logic as frontend)
        analysis = analysis_result.get("analysis", "No analysis available")
        model_id = analysis_result.get("model_id", "Unknown model")

        # Debug logging (same as frontend)
        print(f"DEBUG: Raw analysis type: {type(analysis)}")
        print(f"DEBUG: Raw analysis length: {len(analysis) if analysis else 0}")
        print(f"DEBUG: Raw analysis value: {repr(analysis[:100]) if analysis else 'None'}")
        print(f"DEBUG: Analysis result keys: {list(analysis_result.keys())}")

        # Ensure analysis is a string and not empty (same as frontend)
        if not analysis or not isinstance(analysis, str):
            print(f"DEBUG: Analysis failed validation - type: {type(analysis)}, empty: {not analysis}")
            analysis = "No analysis content received from the AI model."
        
        print(f"\n✅ Final analysis to display:")
        print(f"   Type: {type(analysis)}")
        print(f"   Length: {len(analysis)}")
        print(f"   Content: {analysis[:200]}...")

async def main():
    """Main function."""
    try:
        await simulate_frontend_logic()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
