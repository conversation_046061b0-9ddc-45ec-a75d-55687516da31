"""
Tests for the image validation service.
"""
import os
import io
import re
import pytest
from fastapi import UploadFile, HTTPException
from fastapi.testclient import TestClient
from PIL import Image

from image_analysis.validators import ImageValidator, MAX_IMAGE_DIMENSION


def create_test_image(format="PNG", size=(100, 100), color=(255, 0, 0)):
    """Create a test image for validation."""
    img = Image.new("RGB", size, color)
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format=format)
    img_byte_arr.seek(0)
    return img_byte_arr


def create_test_svg(content="<svg width='100' height='100'></svg>"):
    """Create a test SVG file."""
    svg_bytes = io.BytesIO(content.encode('utf-8'))
    return svg_bytes


def test_validate_file_type_valid():
    """Test validation of valid file types."""
    # Create a test PNG image
    img_bytes = create_test_image(format="PNG")
    file = UploadFile(filename="test.png", file=img_bytes)
    file.content_type = "image/png"
    
    # Validation should pass without raising an exception
    assert ImageValidator.validate_file_type(file) is True


def test_validate_file_type_invalid():
    """Test validation of invalid file types."""
    # Create a file with invalid content type
    file = UploadFile(filename="test.txt", file=io.BytesIO(b"test content"))
    file.content_type = "text/plain"
    
    # Validation should raise an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        ImageValidator.validate_file_type(file)
    
    assert excinfo.value.status_code == 400
    assert "File type not allowed" in excinfo.value.detail


def test_validate_file_size_valid():
    """Test validation of valid file sizes."""
    # Create a small test image
    img_bytes = create_test_image(size=(10, 10))
    file = UploadFile(filename="test.png", file=img_bytes)
    
    # Validation should pass without raising an exception
    assert ImageValidator.validate_file_size(file) is True


def test_validate_file_size_invalid():
    """Test validation of invalid file sizes."""
    # Mock a large file by patching the file.tell() method
    class MockFile:
        def seek(self, *args):
            pass
        
        def tell(self):
            # Return a size larger than the max allowed
            return (ImageValidator.MAX_IMAGE_SIZE_MB + 1) * 1024 * 1024
    
    file = UploadFile(filename="test.png", file=MockFile())
    
    # Validation should raise an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        ImageValidator.validate_file_size(file)
    
    assert excinfo.value.status_code == 413
    assert "File too large" in excinfo.value.detail


def test_validate_image_content_valid():
    """Test validation of valid image content."""
    # Create a valid test image
    img_bytes = create_test_image()
    file = UploadFile(filename="test.png", file=img_bytes)
    file.content_type = "image/png"
    
    # Validation should pass without raising an exception
    assert ImageValidator.validate_image_content(file) is True


def test_validate_image_content_invalid():
    """Test validation of invalid image content."""
    # Create a file with invalid image content
    file = UploadFile(filename="test.png", file=io.BytesIO(b"not an image"))
    file.content_type = "image/png"
    
    # Validation should raise an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        ImageValidator.validate_image_content(file)
    
    assert excinfo.value.status_code == 400
    assert "Invalid image content" in excinfo.value.detail


def test_validate_image_dimensions():
    """Test validation of image dimensions."""
    # Create an image with dimensions exceeding the maximum allowed
    large_img_bytes = create_test_image(size=(MAX_IMAGE_DIMENSION + 100, MAX_IMAGE_DIMENSION + 100))
    file = UploadFile(filename="large_image.png", file=large_img_bytes)
    file.content_type = "image/png"
    
    # Validation should raise an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        ImageValidator.validate_image_content(file)
    
    assert excinfo.value.status_code == 400
    assert "Image dimensions too large" in excinfo.value.detail


def test_validate_svg_content_valid():
    """Test validation of valid SVG content."""
    # Create a valid SVG file
    svg_bytes = create_test_svg()
    file = UploadFile(filename="test.svg", file=svg_bytes)
    file.content_type = "image/svg+xml"
    
    # Validation should pass without raising an exception
    assert ImageValidator.validate_svg_content(file) is True


def test_validate_svg_content_invalid():
    """Test validation of SVG with suspicious content."""
    # Create an SVG with a script tag (potentially malicious)
    malicious_svg = create_test_svg("<svg><script>alert('XSS')</script></svg>")
    file = UploadFile(filename="malicious.svg", file=malicious_svg)
    file.content_type = "image/svg+xml"
    
    # Validation should raise an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        ImageValidator.validate_svg_content(file)
    
    assert excinfo.value.status_code == 400
    assert "potentially malicious content" in excinfo.value.detail


def test_sanitize_filename():
    """Test filename sanitization."""
    # Test with various problematic filenames
    test_cases = [
        ("../../../etc/passwd", "passwd"),
        ("file with spaces.png", "file_with_spaces.png"),
        ("malicious<script>.svg", "malicious_script_.svg"),
        ("", "unnamed_file"),
        ("C:\\Windows\\System32\\cmd.exe", "cmd.exe")
    ]
    
    for input_name, expected_output in test_cases:
        assert ImageValidator.sanitize_filename(input_name) == expected_output


def test_compute_file_hash():
    """Test file hash computation."""
    # Create a test image
    img_bytes = create_test_image()
    file = UploadFile(filename="test.png", file=img_bytes)
    
    # Compute hash
    file_hash = ImageValidator.compute_file_hash(file)
    
    # Hash should be a non-empty string
    assert isinstance(file_hash, str)
    assert len(file_hash) > 0
    
    # File pointer should be reset
    assert file.file.tell() == 0


def test_get_image_metadata():
    """Test extraction of image metadata."""
    # Create a test image
    img_bytes = create_test_image(size=(200, 100))
    file = UploadFile(filename="test.png", file=img_bytes)
    file.content_type = "image/png"
    
    # Get metadata
    metadata = ImageValidator.get_image_metadata(file)
    
    # Check basic metadata
    assert metadata["filename"] == "test.png"
    assert metadata["content_type"] == "image/png"
    assert metadata["sanitized_filename"] == "test.png"
    assert "file_hash" in metadata
    assert metadata["width"] == 200
    assert metadata["height"] == 100
    assert metadata["aspect_ratio"] == 2.0
    
    # File pointer should be reset
    assert file.file.tell() == 0


def test_validate_image_all_checks():
    """Test all validation checks together."""
    # Create a valid test image
    img_bytes = create_test_image()
    file = UploadFile(filename="test.png", file=img_bytes)
    file.content_type = "image/png"
    
    # All validations should pass
    assert ImageValidator.validate_image(file) is True