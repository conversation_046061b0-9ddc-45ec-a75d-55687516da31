"""
Data models for the Image Analysis feature.
"""
from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
import uuid


class ImageType(str, Enum):
    """Enum for image types that can be analyzed."""
    ARCHITECTURE = "architecture"
    ERROR = "error"
    UNKNOWN = "unknown"


class AnalysisStatus(str, Enum):
    """Enum for analysis request status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class BoundingBox(BaseModel):
    """Model for representing a bounding box in an image."""
    x: float
    y: float
    width: float
    height: float


class AnalysisDetail(BaseModel):
    """Model for a single detail in an analysis result."""
    type: str
    content: str
    confidence: float = Field(ge=0.0, le=1.0)
    bounding_box: Optional[BoundingBox] = None


class Recommendation(BaseModel):
    """Model for a recommendation in an analysis result."""
    type: str
    content: str
    priority: int = Field(ge=1, le=5)


class Analysis(BaseModel):
    """Model for the complete analysis of an image."""
    summary: str
    details: List[AnalysisDetail] = []
    recommendations: List[Recommendation] = []


class ImageAnalysisRequest(BaseModel):
    """Model for an image analysis request."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=datetime.now)
    image_type: ImageType = ImageType.UNKNOWN
    status: AnalysisStatus = AnalysisStatus.PENDING
    original_filename: str
    content_type: str
    file_size: int
    storage_path: str

    class Config:
        orm_mode = True


class ImageAnalysisResult(BaseModel):
    """Model for an image analysis result."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    request_id: str
    timestamp: datetime = Field(default_factory=datetime.now)
    analysis_type: ImageType
    confidence: float = Field(ge=0.0, le=1.0)
    image_url: str
    text_content: Optional[str] = None
    analysis: Analysis

    class Config:
        orm_mode = True