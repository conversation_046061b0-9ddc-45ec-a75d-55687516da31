#!/usr/bin/env python3
"""
Simple test for Gemini endpoint.
"""

import requests
import os
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
from dotenv import load_dotenv

def create_test_image():
    """Create a simple test image."""
    img = Image.new('RGB', (200, 150), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    draw.text((20, 20), "Test AWS Diagram", fill='black', font=font)
    draw.rectangle([20, 50, 80, 90], outline='blue', width=2)
    draw.text((25, 65), "EC2", fill='blue', font=font)
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def main():
    load_dotenv()
    
    print("Environment variables:")
    print(f"GOOGLE_GEMINI_API_KEY: {'Set' if os.getenv('GOOGLE_GEMINI_API_KEY') else 'Not set'}")
    print(f"GOOGLE_GEMINI_MODEL_ID: {os.getenv('GOOGLE_GEMINI_MODEL_ID', 'Not set')}")
    
    # Test the endpoint
    image_bytes = create_test_image()
    files = {'file': ('test.png', image_bytes, 'image/png')}
    data = {'prompt': 'Describe this image briefly.'}
    
    try:
        print("\nTesting /analyze/image/gemini endpoint...")
        response = requests.post(
            'http://localhost:8888/analyze/image/gemini',
            files=files,
            data=data,
            timeout=60
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
