#!/usr/bin/env python3
"""
Final integration test for the Google Gemini image analysis.
"""

import requests
import json
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO

def create_test_image():
    """Create a test image for analysis."""
    img = Image.new('RGB', (400, 300), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        font = ImageFont.load_default()
    
    # Create a simple AWS architecture diagram
    draw.text((50, 30), "AWS Architecture Diagram", fill='black', font=font)
    
    # EC2 instance
    draw.rectangle([50, 80, 150, 130], outline='blue', width=2)
    draw.text((70, 100), "EC2", fill='blue', font=font)
    
    # RDS database
    draw.rectangle([250, 80, 350, 130], outline='green', width=2)
    draw.text((270, 100), "RDS", fill='green', font=font)
    
    # Connection arrow
    draw.line([150, 105, 250, 105], fill='red', width=3)
    draw.polygon([(245, 100), (250, 105), (245, 110)], fill='red')
    
    # Load balancer
    draw.rectangle([50, 180, 150, 230], outline='orange', width=2)
    draw.text((80, 200), "ALB", fill='orange', font=font)
    
    # Connection from ALB to EC2
    draw.line([100, 180, 100, 130], fill='purple', width=2)
    draw.polygon([(95, 135), (100, 130), (105, 135)], fill='purple')
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def test_backend_health():
    """Test if backend is running."""
    try:
        response = requests.get('http://localhost:8888/health', timeout=5)
        return response.status_code == 200
    except:
        return False

def test_gemini_analysis():
    """Test the Gemini image analysis endpoint."""
    print("🧪 Testing Google Gemini Image Analysis...")
    
    if not test_backend_health():
        print("❌ Backend is not running. Please start it first.")
        return False
    
    print("✅ Backend is running")
    
    # Create test image
    image_bytes = create_test_image()
    print(f"📷 Created test image ({len(image_bytes)} bytes)")
    
    # Test the endpoint
    files = {'file': ('aws_diagram.png', image_bytes, 'image/png')}
    data = {'prompt': 'Analyze this AWS architecture diagram and explain the components and their relationships.'}
    
    try:
        print("🚀 Sending request to Gemini endpoint...")
        response = requests.post(
            'http://localhost:8888/analyze/image/gemini',
            files=files,
            data=data,
            timeout=90
        )
        
        print(f"📡 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('status') == 'success':
                print("✅ Analysis successful!")
                print(f"🤖 Model: {result.get('model_id', 'unknown')}")
                
                analysis = result.get('analysis', '')
                if analysis:
                    print(f"📝 Analysis length: {len(analysis)} characters")
                    print(f"📄 Analysis preview:")
                    print("-" * 50)
                    print(analysis[:300] + "..." if len(analysis) > 300 else analysis)
                    print("-" * 50)
                    return True
                else:
                    print("❌ No analysis content received")
                    return False
            else:
                print(f"❌ Analysis failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def main():
    """Main test function."""
    print("=" * 60)
    print("FINAL GOOGLE GEMINI INTEGRATION TEST")
    print("=" * 60)
    
    success = test_gemini_analysis()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS! Google Gemini integration is working!")
        print("\n📋 Next steps:")
        print("1. Start the frontend: cd Frontend && chainlit run chainlit_app.py")
        print("2. Use the /analyze command")
        print("3. Upload an image")
        print("4. Type 'gemini' when prompted")
        print("5. View the detailed analysis results")
        print("\n✨ The analysis will now appear as a separate message!")
    else:
        print("❌ FAILED! Please check the errors above.")
    print("=" * 60)

if __name__ == "__main__":
    main()
