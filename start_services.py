#!/usr/bin/env python3
"""
Service startup script for RAG Quadrant application.

This script helps start both the Backend (FastAPI) and Frontend (Chainlit) services
with proper configuration and error handling.
"""

import os
import sys
import subprocess
import time
import requests
import webbrowser
from pathlib import Path

def check_port_available(port):
    """Check if a port is available."""
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        return result != 0
    except:
        return True

def wait_for_service(url, timeout=30, service_name="Service"):
    """Wait for a service to become available."""
    print(f"⏳ Waiting for {service_name} to start...")
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                print(f"✅ {service_name} is ready!")
                return True
        except:
            pass
        time.sleep(1)
    
    print(f"❌ {service_name} failed to start within {timeout} seconds")
    return False

def start_backend():
    """Start the FastAPI backend service."""
    backend_dir = Path("Backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found")
        return None
    
    main_py = backend_dir / "main.py"
    if not main_py.exists():
        print("❌ Backend main.py not found")
        return None
    
    print("🚀 Starting Backend (FastAPI)...")
    
    # Check if port 8888 is available
    if not check_port_available(8888):
        print("⚠️ Port 8888 is already in use. Backend may already be running.")
        return None
    
    try:
        # Start backend process
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for backend to start
        if wait_for_service("http://localhost:8888/health", service_name="Backend"):
            print("✅ Backend started successfully on http://localhost:8888")
            return process
        else:
            process.terminate()
            return None
            
    except Exception as e:
        print(f"❌ Failed to start backend: {e}")
        return None

def start_frontend():
    """Start the Chainlit frontend service."""
    frontend_dir = Path("Frontend")
    if not frontend_dir.exists():
        print("❌ Frontend directory not found")
        return None
    
    chainlit_app = frontend_dir / "chainlit_app.py"
    if not chainlit_app.exists():
        print("❌ Frontend chainlit_app.py not found")
        return None
    
    print("🚀 Starting Frontend (Chainlit)...")
    
    # Check if port 8501 is available
    if not check_port_available(8501):
        print("⚠️ Port 8501 is already in use. Frontend may already be running.")
        return None
    
    try:
        # Start frontend process
        process = subprocess.Popen(
            ["chainlit", "run", "chainlit_app.py", "--port", "8501"],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for frontend to start
        time.sleep(5)  # Chainlit takes a bit longer to start
        if wait_for_service("http://localhost:8501", service_name="Frontend"):
            print("✅ Frontend started successfully on http://localhost:8501")
            return process
        else:
            process.terminate()
            return None
            
    except Exception as e:
        print(f"❌ Failed to start frontend: {e}")
        return None

def main():
    """Main function to start both services."""
    print("=" * 60)
    print("RAG QUADRANT - SERVICE STARTUP")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("Backend").exists() or not Path("Frontend").exists():
        print("❌ Please run this script from the RAG_Quadrant root directory")
        print("   Current directory:", os.getcwd())
        return 1
    
    backend_process = None
    frontend_process = None
    
    try:
        # Start backend first
        backend_process = start_backend()
        if not backend_process:
            print("❌ Failed to start backend. Exiting.")
            return 1
        
        print()
        
        # Start frontend
        frontend_process = start_frontend()
        if not frontend_process:
            print("❌ Failed to start frontend. Stopping backend.")
            if backend_process:
                backend_process.terminate()
            return 1
        
        print()
        print("🎉 Both services started successfully!")
        print()
        print("📋 Service URLs:")
        print("   Backend API:  http://localhost:8888")
        print("   Frontend UI:  http://localhost:8501")
        print()
        print("🧪 Available Commands in Frontend:")
        print("   /help     - Show help message")
        print("   /ocr      - Upload image for text extraction and search")
        print("   /analyze  - Analyze images with AI vision models")
        print("   /diagrams - Show architecture diagrams")
        print("   /tokens   - Show token usage")
        print()
        print("🌐 Opening frontend in browser...")
        
        # Open browser
        try:
            webbrowser.open("http://localhost:8501")
        except:
            print("   (Could not open browser automatically)")
        
        print()
        print("Press Ctrl+C to stop both services...")
        
        # Wait for user interrupt
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping services...")
            
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return 1
        
    finally:
        # Clean up processes
        if frontend_process:
            print("   Stopping frontend...")
            frontend_process.terminate()
            frontend_process.wait()
            
        if backend_process:
            print("   Stopping backend...")
            backend_process.terminate()
            backend_process.wait()
            
        print("✅ All services stopped.")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
