#!/usr/bin/env python3
"""
Test the frontend logic with the actual response from the backend.
"""

import requests
import json
from PIL import Image, ImageDraw
from io import BytesIO

def create_test_image():
    """Create a test image."""
    img = Image.new('RGB', (200, 150), color='white')
    draw = ImageDraw.Draw(img)
    draw.text((20, 20), "AWS Test", fill='black')
    draw.rectangle([20, 50, 80, 90], outline='blue', width=2)
    draw.text((25, 65), "EC2", fill='blue')
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def test_frontend_logic():
    """Test the exact logic the frontend uses."""
    print("Testing frontend logic...")
    
    # Get response from backend
    image_bytes = create_test_image()
    files = {'file': ('test.png', image_bytes, 'image/png')}
    data = {'prompt': 'Analyze this AWS diagram.'}
    
    try:
        response = requests.post(
            'http://localhost:8888/analyze/image/gemini',
            files=files,
            data=data,
            timeout=60
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            analysis_result = response.json()
            print(f"Response keys: {list(analysis_result.keys())}")
            
            # Simulate frontend logic
            if analysis_result.get("status") == "error":
                error_msg = analysis_result.get("error", "Unknown error")
                print(f"❌ Frontend would show error: {error_msg}")
            else:
                # Extract text content if available
                extracted_text = analysis_result.get("extracted_text", "").strip()
                extracted_text_section = ""
                if extracted_text:
                    extracted_text_section = f"""
## 📝 **Extracted Text**

```
{extracted_text[:500]}
```
{'' if len(extracted_text) <= 500 else '*(text truncated)*'}

"""
                
                # Format the analysis
                analysis = analysis_result.get("analysis", "No analysis available")
                model_id = analysis_result.get("model_id", "Unknown model")
                
                print(f"✅ Analysis received:")
                print(f"   Type: {type(analysis)}")
                print(f"   Length: {len(analysis) if analysis else 0}")
                print(f"   Model: {model_id}")
                print(f"   Extracted text: {'Yes' if extracted_text else 'No'}")
                
                if analysis:
                    print(f"   Analysis preview: {analysis[:200]}...")
                
                result_content = f"""
# 🔍 **Image Analysis Results**

## 🤖 **AI Analysis**

{analysis}

{extracted_text_section}
---
*Analysis performed by: {model_id}*
"""
                
                print(f"\n📄 Final content length: {len(result_content)}")
                print(f"📄 Final content preview:")
                print("=" * 50)
                print(result_content[:500])
                print("=" * 50)
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_frontend_logic()
