"""
Integration tests for the Image Analysis API endpoints.
"""
import os
import io
import pytest
import tempfile
import shutil
from fastapi.testclient import TestClient
from fastapi import FastAP<PERSON>
from PIL import Image

from image_analysis.api import router as image_analysis_router
from image_analysis.models import ImageAnalysisRequest, AnalysisStatus


# Create a test FastAPI app
app = FastAPI()
app.include_router(image_analysis_router)
client = TestClient(app)


def create_test_image(format="PNG", size=(100, 100), color=(255, 0, 0)):
    """Create a test image for API testing."""
    img = Image.new("RGB", size, color)
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format=format)
    img_byte_arr.seek(0)
    return img_byte_arr


class TestImageAnalysisAPI:
    """Tests for the Image Analysis API endpoints."""
    
    def setup_method(self):
        """Set up test environment."""
        # Create a temporary directory for test images
        self.temp_dir = tempfile.mkdtemp()
        
        # Create a test image
        self.test_image = create_test_image()
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_upload_image(self):
        """Test uploading an image for analysis."""
        # Upload a test image
        response = client.post(
            "/api/image-analysis/",
            files={"file": ("test.png", self.test_image, "image/png")}
        )
        
        # Check response
        assert response.status_code == 200
        data = response.json()
        assert "id" in data
        assert data["status"] == "pending"
        assert "message" in data
        
        # Store request ID for other tests
        self.request_id = data["id"]
    
    def test_get_analysis_result(self):
        """Test getting analysis results."""
        # First upload an image
        response = client.post(
            "/api/image-analysis/",
            files={"file": ("test.png", self.test_image, "image/png")}
        )
        request_id = response.json()["id"]
        
        # Get analysis result (will be pending initially)
        response = client.get(f"/api/image-analysis/{request_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] in [status.value for status in AnalysisStatus]
    
    def test_delete_analysis(self):
        """Test deleting analysis data."""
        # First upload an image
        response = client.post(
            "/api/image-analysis/",
            files={"file": ("test.png", self.test_image, "image/png")}
        )
        request_id = response.json()["id"]
        
        # Delete the analysis
        response = client.delete(f"/api/image-analysis/{request_id}")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        
        # Verify it's deleted by trying to get it
        response = client.get(f"/api/image-analysis/{request_id}")
        assert response.status_code == 404
    
    def test_get_image(self):
        """Test getting an image by request ID."""
        # First upload an image
        response = client.post(
            "/api/image-analysis/",
            files={"file": ("test.png", self.test_image, "image/png")}
        )
        request_id = response.json()["id"]
        
        # Get the image
        response = client.get(f"/api/image-analysis/image/{request_id}")
        assert response.status_code == 200
        assert response.headers["content-type"] == "image/png"
    
    def test_batch_analyze(self):
        """Test batch analysis of multiple images."""
        # Create a batch request with dummy URLs
        response = client.post(
            "/api/image-analysis/batch",
            json={"image_urls": ["http://example.com/image1.png", "http://example.com/image2.png"]}
        )
        
        # Check response
        assert response.status_code == 200
        data = response.json()
        assert data["batch_size"] == 2
        assert len(data["request_ids"]) == 2
    
    def test_get_service_status(self):
        """Test getting service status."""
        response = client.get("/api/image-analysis/status")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "requests" in data
        assert "results" in data
    
    def test_invalid_image_upload(self):
        """Test uploading an invalid image."""
        # Create an invalid "image" (text file)
        invalid_file = io.BytesIO(b"This is not an image")
        
        # Try to upload it
        response = client.post(
            "/api/image-analysis/",
            files={"file": ("invalid.png", invalid_file, "image/png")}
        )
        
        # Check that it's rejected
        assert response.status_code == 400
    
    def test_nonexistent_analysis(self):
        """Test getting a nonexistent analysis."""
        response = client.get("/api/image-analysis/nonexistent-id")
        assert response.status_code == 404