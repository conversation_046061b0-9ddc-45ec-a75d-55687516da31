#!/usr/bin/env python3
"""
Test script to debug the exact issue in the Chainlit environment.
This will help us understand why the analysis is not being displayed.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'Frontend'))

import requests
import asyncio

async def test_analyze_image_with_ai():
    """Test the analyze_image_with_ai function exactly as used in Chainlit."""
    
    # Read the image bytes
    image_path = "test_diagrams/aws_architecture_complex.png"
    with open(image_path, 'rb') as f:
        image_bytes = f.read()
    
    print(f"📸 Testing with image: {image_path}")
    print(f"📊 Image size: {len(image_bytes)} bytes")
    
    # Simulate the exact function call from chainlit_app.py
    use_gemini = True
    custom_prompt = None
    
    # This is the exact code from the analyze_image_with_ai function
    if use_gemini:
        url = "http://localhost:8888/analyze/image/gemini"
    else:
        url = "http://localhost:8888/analyze/image"
    
    files = {'file': ('test_image.png', image_bytes, 'image/png')}
    data = {}
    
    if use_gemini and custom_prompt:
        data['prompt'] = custom_prompt
    
    try:
        response = requests.post(url, files=files, data=data, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Response received successfully")
            print(f"📋 Response keys: {list(result.keys())}")
            
            # Now simulate the exact frontend processing logic
            print("\n=== SIMULATING EXACT FRONTEND LOGIC ===")
            
            analysis_result = result  # This is what gets returned
            
            # Format and display the analysis result
            if analysis_result.get("status") == "error":
                error_msg = analysis_result.get("error", "Unknown error")
                print(f"❌ Error case: {error_msg}")
                return False
            else:
                # Extract text content if available
                extracted_text = analysis_result.get("extracted_text", "").strip()
                extracted_text_section = ""
                if extracted_text:
                    extracted_text_section = f"""
## 📝 **Extracted Text**

```
{extracted_text[:500]}
```
{'' if len(extracted_text) <= 500 else '*(text truncated)*'}

"""
                
                # Format the analysis - EXACT CODE FROM FRONTEND
                analysis = analysis_result.get("analysis", "No analysis available")
                model_id = analysis_result.get("model_id", "Unknown model")
                
                print(f"🔍 Raw analysis from response: {type(analysis)} = {repr(analysis[:100]) if analysis else 'None'}")
                
                # Debug: Log the analysis validation - EXACT CODE FROM FRONTEND
                analysis_debug = f"Analysis check: type={type(analysis)}, len={len(analysis) if analysis else 0}, bool={bool(analysis)}"
                print(f"FRONTEND DEBUG: {analysis_debug}")
                
                # Ensure analysis is a string and not empty - EXACT CODE FROM FRONTEND
                if not analysis or not isinstance(analysis, str):
                    print(f"FRONTEND DEBUG: Analysis failed validation, using fallback")
                    analysis = "No analysis content received from the AI model."
                    print(f"❌ ISSUE FOUND: Analysis failed validation!")
                    print(f"   - analysis value: {repr(analysis)}")
                    print(f"   - analysis type: {type(analysis)}")
                    print(f"   - analysis bool: {bool(analysis)}")
                    print(f"   - isinstance check: {isinstance(analysis, str)}")
                    return False
                else:
                    print(f"FRONTEND DEBUG: Analysis passed validation")
                    print(f"✅ Analysis validation passed!")
                
                # Clean the analysis content (only normalize line endings)
                analysis_clean = analysis.replace('\r\n', '\n').replace('\r', '\n')
                
                print(f"📝 Final analysis length: {len(analysis_clean)}")
                print(f"📖 Final analysis preview: {analysis_clean[:200]}...")
                
                return True
        else:
            error_text = response.text
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {error_text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_analyze_image_with_ai())
    if result:
        print("\n✅ Test passed - analysis should be displayed correctly")
    else:
        print("\n❌ Test failed - found the issue!")
