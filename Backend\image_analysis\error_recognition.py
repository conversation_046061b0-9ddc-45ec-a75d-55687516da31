"""
Error text recognition service for identifying and classifying error messages.
"""
import re
import logging
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum


# Configure logging
logger = logging.getLogger(__name__)


class ErrorSeverity(str, Enum):
    """Error severity levels."""
    CRITICAL = "critical"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    DEBUG = "debug"
    UNKNOWN = "unknown"


class ErrorCategory(str, Enum):
    """Error categories."""
    SYNTAX = "syntax"
    RUNTIME = "runtime"
    COMPILATION = "compilation"
    NETWORK = "network"
    DATABASE = "database"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    VALIDATION = "validation"
    CONFIGURATION = "configuration"
    RESOURCE = "resource"
    TIMEOUT = "timeout"
    DEPENDENCY = "dependency"
    UNKNOWN = "unknown"

class ErrorVisualIndicator(str, Enum):
    """Visual indicators of errors in images."""
    RED_BACKGROUND = "red_background"
    YELLOW_BACKGROUND = "yellow_background"
    RED_TEXT = "red_text"
    ERROR_ICON = "error_icon"
    WARNING_ICON = "warning_icon"
    EXCLAMATION_MARK = "exclamation_mark"
    ERROR_SYMBOL = "error_symbol"
    MODAL_DIALOG = "modal_dialog"
    ALERT_BOX = "alert_box"
    UNKNOWN = "unknown"

class ErrorPattern:
    """Pattern for matching error messages."""
    
    def __init__(
        self, 
        regex: str, 
        severity: ErrorSeverity, 
        category: ErrorCategory,
        description: str,
        solution: Optional[str] = None
    ):
        """
        Initialize an error pattern.
        
        Args:
            regex: Regular expression pattern for matching error messages
            severity: Severity level of the error
            category: Category of the error
            description: Description of the error
            solution: Suggested solution for the error
        """
        self.regex = regex
        self.pattern = re.compile(regex, re.IGNORECASE)
        self.severity = severity
        self.category = category
        self.description = description
        self.solution = solution


class ErrorRecognitionService:
    """
    Service for identifying and classifying error messages.
    """
    
    def __init__(self):
        """Initialize the error recognition service."""
        # Initialize error patterns
        self.error_patterns = self._initialize_error_patterns()
    
    def identify_errors(self, text: str) -> List[Dict[str, Any]]:
        """
        Identify error messages in text.
        
        Args:
            text: Text to analyze
            
        Returns:
            List[Dict[str, Any]]: List of identified errors
        """
        if not text:
            return []
        
        # Split text into lines
        lines = text.split('\n')
        
        # Identify errors in each line
        errors = []
        for line_number, line in enumerate(lines, 1):
            if not line.strip():
                continue
                
            # Check for error patterns
            for pattern in self.error_patterns:
                match = pattern.pattern.search(line)
                if match:
                    # Extract matched text
                    matched_text = match.group(0)
                    
                    # Create error object
                    error = {
                        "text": line.strip(),
                        "matched_text": matched_text,
                        "line_number": line_number,
                        "severity": pattern.severity,
                        "category": pattern.category,
                        "description": pattern.description,
                        "solution": pattern.solution,
                        "confidence": 0.8  # Default confidence score
                    }
                    
                    # Adjust confidence based on match quality
                    error["confidence"] = self._calculate_confidence(line, matched_text, pattern)
                    
                    errors.append(error)
                    break  # Stop after first match for this line
        
        # Sort errors by confidence (descending)
        errors.sort(key=lambda x: x["confidence"], reverse=True)
        
        return errors
    
    def classify_error_type(self, text: str) -> Tuple[ErrorSeverity, ErrorCategory, float]:
        """
        Classify the overall error type in text.
        
        Args:
            text: Text to analyze
            
        Returns:
            Tuple[ErrorSeverity, ErrorCategory, float]: Error severity, category, and confidence
        """
        if not text:
            return ErrorSeverity.UNKNOWN, ErrorCategory.UNKNOWN, 0.0
        
        # Identify individual errors
        errors = self.identify_errors(text)
        
        if not errors:
            return ErrorSeverity.UNKNOWN, ErrorCategory.UNKNOWN, 0.0
        
        # Count occurrences of each severity and category
        severity_counts = {severity: 0 for severity in ErrorSeverity}
        category_counts = {category: 0 for category in ErrorCategory}
        
        for error in errors:
            severity_counts[error["severity"]] += 1
            category_counts[error["category"]] += 1
        
        # Determine the most common severity and category
        max_severity = max(severity_counts.items(), key=lambda x: x[1])
        max_category = max(category_counts.items(), key=lambda x: x[1])
        
        # Calculate confidence based on the proportion of errors with the most common severity and category
        total_errors = len(errors)
        severity_confidence = max_severity[1] / total_errors if total_errors > 0 else 0.0
        category_confidence = max_category[1] / total_errors if total_errors > 0 else 0.0
        
        # Average confidence
        confidence = (severity_confidence + category_confidence) / 2
        
        return max_severity[0], max_category[0], confidence
    
    def suggest_solutions(self, text: str) -> List[Dict[str, Any]]:
        """
        Suggest solutions for errors in text.
        
        Args:
            text: Text to analyze
            
        Returns:
            List[Dict[str, Any]]: List of suggested solutions
        """
        # Identify errors
        errors = self.identify_errors(text)
        
        # Extract solutions
        solutions = []
        for error in errors:
            if error["solution"]:
                solution = {
                    "error_text": error["text"],
                    "solution": error["solution"],
                    "confidence": error["confidence"],
                    "severity": error["severity"],
                    "category": error["category"]
                }
                solutions.append(solution)
        
        # Sort solutions by confidence (descending)
        solutions.sort(key=lambda x: x["confidence"], reverse=True)
        
        # Remove duplicates while preserving order
        unique_solutions = []
        seen_solutions = set()
        for solution in solutions:
            if solution["solution"] not in seen_solutions:
                seen_solutions.add(solution["solution"])
                unique_solutions.append(solution)
        
        return unique_solutions
    
    def _calculate_confidence(self, line: str, matched_text: str, pattern: ErrorPattern) -> float:
        """
        Calculate confidence score for an error match.
        
        Args:
            line: Full line of text
            matched_text: Matched portion of the line
            pattern: Error pattern that matched
            
        Returns:
            float: Confidence score between 0 and 1
        """
        # Base confidence
        confidence = 0.7
        
        # Adjust based on match length relative to line length
        match_ratio = len(matched_text) / len(line) if len(line) > 0 else 0
        if match_ratio > 0.8:
            confidence += 0.1
        elif match_ratio < 0.2:
            confidence -= 0.1
        
        # Adjust based on error severity
        if pattern.severity == ErrorSeverity.CRITICAL:
            confidence += 0.1
        elif pattern.severity == ErrorSeverity.ERROR:
            confidence += 0.05
        
        # Adjust based on presence of error keywords
        error_keywords = ["error", "exception", "failed", "failure", "critical"]
        if any(keyword in line.lower() for keyword in error_keywords):
            confidence += 0.1
        
        # Ensure confidence is between 0 and 1
        return max(0.0, min(1.0, confidence))
    
    def _initialize_error_patterns(self) -> List[ErrorPattern]:
        """
        Initialize error patterns for common error messages.
        
        Returns:
            List[ErrorPattern]: List of error patterns
        """
        patterns = []
        
        # Syntax errors
        patterns.append(ErrorPattern(
            regex=r"syntax error",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.SYNTAX,
            description="Syntax error in code",
            solution="Check the syntax of your code for missing brackets, semicolons, or other syntax elements."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"unexpected (token|identifier|character)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.SYNTAX,
            description="Unexpected token or character in code",
            solution="Check for typos or invalid characters in your code."
        ))
        
        # Runtime errors
        patterns.append(ErrorPattern(
            regex=r"null(pointer)? (exception|reference)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.RUNTIME,
            description="Null pointer exception",
            solution="Check for null references before accessing object properties or methods."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"index out of (range|bounds)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.RUNTIME,
            description="Index out of range error",
            solution="Verify that array indices are within valid bounds."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"division by zero",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.RUNTIME,
            description="Division by zero error",
            solution="Add a check to prevent division by zero."
        ))
        
        # Compilation errors
        patterns.append(ErrorPattern(
            regex=r"cannot find symbol",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.COMPILATION,
            description="Symbol not found during compilation",
            solution="Check for typos in variable or method names, or ensure the required libraries are imported."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"incompatible types",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.COMPILATION,
            description="Incompatible types error",
            solution="Ensure that variable types are compatible or add appropriate type conversions."
        ))
        
        # Network errors
        patterns.append(ErrorPattern(
            regex=r"connection (refused|timed out|reset)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.NETWORK,
            description="Network connection error",
            solution="Check network connectivity and ensure the target service is running and accessible."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"(404|500|503) (error|not found|unavailable)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.NETWORK,
            description="HTTP error",
            solution="Verify the URL is correct and the server is functioning properly."
        ))
        
        # Database errors
        patterns.append(ErrorPattern(
            regex=r"database (error|exception)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.DATABASE,
            description="Database error",
            solution="Check database connection settings and ensure the database is running."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"sql (error|exception)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.DATABASE,
            description="SQL error",
            solution="Verify your SQL query syntax and ensure the referenced tables and columns exist."
        ))
        
        # Authentication errors
        patterns.append(ErrorPattern(
            regex=r"authentication (failed|error)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.AUTHENTICATION,
            description="Authentication failure",
            solution="Check your credentials and ensure they are correct."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"invalid (credentials|password|token)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.AUTHENTICATION,
            description="Invalid credentials",
            solution="Verify your username and password or refresh your authentication token."
        ))
        
        # Authorization errors
        patterns.append(ErrorPattern(
            regex=r"(permission|access) denied",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.AUTHORIZATION,
            description="Permission denied",
            solution="Check if you have the necessary permissions to perform this action."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"unauthorized",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.AUTHORIZATION,
            description="Unauthorized access",
            solution="Ensure you are authenticated and have the required permissions."
        ))
        
        # Validation errors
        patterns.append(ErrorPattern(
            regex=r"validation (error|failed)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.VALIDATION,
            description="Validation error",
            solution="Check the input data against the validation requirements."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"invalid (input|format|value)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.VALIDATION,
            description="Invalid input",
            solution="Ensure the input data is in the correct format and meets all requirements."
        ))
        
        # Configuration errors
        patterns.append(ErrorPattern(
            regex=r"configuration (error|missing)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.CONFIGURATION,
            description="Configuration error",
            solution="Check your configuration files for missing or incorrect settings."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"missing (config|configuration|setting)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.CONFIGURATION,
            description="Missing configuration",
            solution="Add the required configuration settings to your configuration files."
        ))
        
        # Resource errors
        patterns.append(ErrorPattern(
            regex=r"out of memory",
            severity=ErrorSeverity.CRITICAL,
            category=ErrorCategory.RESOURCE,
            description="Out of memory error",
            solution="Increase the memory allocation or optimize your code to use less memory."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"resource (unavailable|exhausted)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.RESOURCE,
            description="Resource unavailable",
            solution="Check if the required resources are available and not exhausted."
        ))
        
        # Timeout errors
        patterns.append(ErrorPattern(
            regex=r"(operation|request) timed out",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.TIMEOUT,
            description="Operation timed out",
            solution="Increase the timeout value or optimize the operation to complete faster."
        ))
        
        # Dependency errors
        patterns.append(ErrorPattern(
            regex=r"dependency (error|missing)",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.DEPENDENCY,
            description="Dependency error",
            solution="Ensure all required dependencies are installed and properly configured."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"module not found",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.DEPENDENCY,
            description="Module not found",
            solution="Install the missing module or check your import statements."
        ))
        
        # Generic error patterns
        patterns.append(ErrorPattern(
            regex=r"error",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.UNKNOWN,
            description="Generic error",
            solution="Review the error message for specific details about the issue."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"exception",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.UNKNOWN,
            description="Exception occurred",
            solution="Check the exception details for more information about the issue."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"warning",
            severity=ErrorSeverity.WARNING,
            category=ErrorCategory.UNKNOWN,
            description="Warning message",
            solution="Review the warning message and address any potential issues."
        ))
        
        patterns.append(ErrorPattern(
            regex=r"failed",
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.UNKNOWN,
            description="Operation failed",
            solution="Check the failure details for more information about the issue."
        ))
        
        return patterns