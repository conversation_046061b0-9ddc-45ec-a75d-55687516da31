#!/bin/bash
# RAG Application Service Validation Script
# Validates that systemd services are properly configured and running

set -euo pipefail

# Configuration
SERVICES=("rag-api" "rag-frontend")
API_PORT=8080
FRONTEND_PORT=80
APP_DIR="/opt/chainlit_rag"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%H:%M:%S')] ✓ $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%H:%M:%S')] ⚠ $1${NC}"; }
error() { echo -e "${RED}[$(date +'%H:%M:%S')] ✗ $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%H:%M:%S')] ℹ $1${NC}"; }

# Check if service files exist
check_service_files() {
    info "Checking systemd service files..."
    
    for service in "${SERVICES[@]}"; do
        local service_file="/etc/systemd/system/${service}.service"
        if [[ -f "$service_file" ]]; then
            log "${service}.service file exists"
        else
            error "${service}.service file missing"
            return 1
        fi
    done
}

# Check if services are enabled
check_service_enabled() {
    info "Checking if services are enabled..."
    
    for service in "${SERVICES[@]}"; do
        if systemctl is-enabled --quiet "${service}.service" 2>/dev/null; then
            log "${service}.service is enabled"
        else
            warn "${service}.service is not enabled (won't start on boot)"
        fi
    done
}

# Check if services are running
check_service_status() {
    info "Checking service status..."
    
    for service in "${SERVICES[@]}"; do
        if systemctl is-active --quiet "${service}.service" 2>/dev/null; then
            log "${service}.service is running"
        else
            error "${service}.service is not running"
            # Show brief status
            systemctl status "${service}.service" --no-pager -l || true
        fi
    done
}

# Check if ports are listening
check_ports() {
    info "Checking if services are listening on expected ports..."
    
    # Check API port
    if netstat -tuln 2>/dev/null | grep -q ":${API_PORT} "; then
        log "RAG API is listening on port ${API_PORT}"
    else
        error "RAG API is not listening on port ${API_PORT}"
    fi
    
    # Check Frontend port (port 80)
    if netstat -tuln 2>/dev/null | grep -q ":${FRONTEND_PORT} "; then
        log "RAG Frontend is listening on port ${FRONTEND_PORT} (HTTP)"
    else
        error "RAG Frontend is not listening on port ${FRONTEND_PORT}"
    fi
}

# Check application directory and permissions
check_app_directory() {
    info "Checking application directory..."
    
    if [[ -d "$APP_DIR" ]]; then
        log "Application directory exists: $APP_DIR"
        
        # Check key files
        local key_files=("main.py" "chainlit_app.py" "requirements.txt" ".env" "venv")
        for file in "${key_files[@]}"; do
            if [[ -e "$APP_DIR/$file" ]]; then
                log "Found: $file"
            else
                warn "Missing: $file"
            fi
        done
        
        # Check ownership
        if [[ -d "$APP_DIR" ]] && id "raguser" &>/dev/null; then
            local owner=$(stat -c '%U' "$APP_DIR" 2>/dev/null || echo "unknown")
            if [[ "$owner" == "raguser" ]]; then
                log "Application directory has correct ownership (raguser)"
            else
                warn "Application directory owner is '$owner', should be 'raguser'"
            fi
        fi
    else
        error "Application directory does not exist: $APP_DIR"
    fi
}

# Check environment configuration
check_environment() {
    info "Checking environment configuration..."
    
    local env_file="$APP_DIR/.env"
    if [[ -f "$env_file" ]]; then
        log "Environment file exists: $env_file"
        
        # Check for required variables
        local required_vars=("AWS_REGION" "S3_BUCKET_NAME" "QDRANT_PATH")
        for var in "${required_vars[@]}"; do
            if grep -q "^${var}=" "$env_file" 2>/dev/null; then
                log "Found environment variable: $var"
            else
                warn "Missing environment variable: $var"
            fi
        done
        
        # Check for placeholder values
        if grep -q "your-.*-here\|your-actual-" "$env_file" 2>/dev/null; then
            warn "Environment file contains placeholder values - please update with actual credentials"
        fi
    else
        error "Environment file does not exist: $env_file"
    fi
}

# Test HTTP endpoints
test_endpoints() {
    info "Testing HTTP endpoints..."
    
    # Test API health endpoint
    if curl -f -s "http://localhost:${API_PORT}/health" >/dev/null 2>&1; then
        log "RAG API health endpoint responding"
    else
        error "RAG API health endpoint not responding"
    fi

    # Test Frontend (requires sudo for port 80)
    if curl -f -s "http://localhost:${FRONTEND_PORT}" >/dev/null 2>&1; then
        log "RAG Frontend responding"
    else
        error "RAG Frontend not responding"
    fi
}

# Check logs for errors
check_logs() {
    info "Checking recent logs for errors..."
    
    for service in "${SERVICES[@]}"; do
        local error_count=$(journalctl -u "${service}.service" --since "1 hour ago" --no-pager | grep -i "error\|exception\|failed" | wc -l)
        if [[ $error_count -eq 0 ]]; then
            log "No recent errors in ${service}.service logs"
        else
            warn "Found $error_count recent errors in ${service}.service logs"
            echo "Recent errors:"
            journalctl -u "${service}.service" --since "1 hour ago" --no-pager | grep -i "error\|exception\|failed" | tail -5
        fi
    done
}

# Show system resources
check_resources() {
    info "Checking system resources..."
    
    # Memory usage
    local mem_usage=$(free | awk 'NR==2{printf "%.1f%%", $3*100/$2}')
    log "Memory usage: $mem_usage"
    
    # Disk usage for app directory
    if [[ -d "$APP_DIR" ]]; then
        local disk_usage=$(df -h "$APP_DIR" | awk 'NR==2{print $5}')
        log "Disk usage (app directory): $disk_usage"
    fi
    
    # Load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}')
    log "Load average:$load_avg"
}

# Main validation function
main() {
    echo -e "${BLUE}RAG Application Service Validation${NC}"
    echo -e "${BLUE}===================================${NC}"
    echo ""
    
    local exit_code=0
    
    # Run all checks
    check_service_files || exit_code=1
    echo ""
    
    check_service_enabled
    echo ""
    
    check_service_status || exit_code=1
    echo ""
    
    check_ports || exit_code=1
    echo ""
    
    check_app_directory
    echo ""
    
    check_environment
    echo ""
    
    test_endpoints || exit_code=1
    echo ""
    
    check_logs
    echo ""
    
    check_resources
    echo ""
    
    # Summary
    if [[ $exit_code -eq 0 ]]; then
        log "All validation checks passed!"
    else
        error "Some validation checks failed. Please review the output above."
    fi
    
    echo ""
    echo -e "${BLUE}Service Management Commands:${NC}"
    echo "  sudo systemctl status rag-api rag-frontend"
    echo "  sudo systemctl restart rag-api rag-frontend"
    echo "  sudo journalctl -u rag-api -f"
    echo "  sudo journalctl -u rag-frontend -f"
    
    exit $exit_code
}

# Handle command line arguments
case "${1:-validate}" in
    "validate"|"check")
        main
        ;;
    "quick")
        check_service_status
        check_ports
        test_endpoints
        ;;
    "logs")
        check_logs
        ;;
    "resources")
        check_resources
        ;;
    "help"|*)
        echo "RAG Application Service Validation Script"
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  validate    Run full validation (default)"
        echo "  quick       Quick status check"
        echo "  logs        Check logs for errors"
        echo "  resources   Show system resources"
        echo "  help        Show this help"
        ;;
esac
