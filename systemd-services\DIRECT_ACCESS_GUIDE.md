# Direct Access Configuration (No Nginx)

Your RAG application is configured for direct access without <PERSON><PERSON><PERSON> reverse proxy.

## Port Configuration

### **Frontend (Chainlit)**
- **Port**: 80 (HTTP)
- **Access**: `http://YOUR-EC2-PUBLIC-IP/`
- **Service**: `rag-frontend.service`

### **API (FastAPI)**
- **Port**: 8080
- **Access**: `http://YOUR-EC2-PUBLIC-IP:8080/`
- **Health Check**: `http://YOUR-EC2-PUBLIC-IP:8080/health`
- **Service**: `rag-api.service`

## EC2 Security Group Requirements

Configure your EC2 Security Group with these inbound rules:

```
Inbound Rules:
- Port 22 (SSH): Your IP only (for management)
- Port 80 (HTTP): 0.0.0.0/0 (for public frontend access)
- Port 8080 (Custom): 0.0.0.0/0 (for API access, optional)

Outbound Rules:
- All traffic: 0.0.0.0/0 (default)
```

## Deployment Steps

### 1. Install Services
```bash
cd /opt/chainlit_rag
sudo systemd-services/install-services.sh install
```

### 2. Configure Environment
```bash
sudo nano /opt/chainlit_rag/.env
```

Update these values:
```env
# Application Configuration
RAG_API_HOST=0.0.0.0
RAG_API_PORT=8080
API_BASE_URL=http://YOUR-EC2-PUBLIC-IP:8080
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=80
```

### 3. Start Services
```bash
sudo systemd-services/install-services.sh start
```

### 4. Verify Access
```bash
# Get your EC2 public IP
curl -s http://***************/latest/meta-data/public-ipv4

# Test local access
curl -f http://localhost/
curl -f http://localhost:8080/health

# Test public access (replace with your IP)
curl -f http://YOUR-EC2-PUBLIC-IP/
curl -f http://YOUR-EC2-PUBLIC-IP:8080/health
```

## User Access

### **Main Application**
Users access your RAG application at:
```
http://YOUR-EC2-PUBLIC-IP/
```

### **API Access** (Optional)
Direct API access at:
```
http://YOUR-EC2-PUBLIC-IP:8080/
```

## Service Management

### Check Status
```bash
sudo systemctl status rag-api rag-frontend
```

### View Logs
```bash
# Frontend logs
sudo journalctl -u rag-frontend -f

# API logs
sudo journalctl -u rag-api -f
```

### Restart Services
```bash
sudo systemctl restart rag-api rag-frontend
```

## Port 80 Considerations

### **Why Port 80?**
- Standard HTTP port
- No need to specify port in URL
- Works with corporate firewalls
- Professional appearance

### **Requires Root Privileges**
- Port 80 is a privileged port (< 1024)
- Service runs as `raguser` but systemd handles the privilege escalation
- This is handled automatically by the systemd service configuration

### **Alternative: Use Port 8000**
If you prefer not to use port 80, you can change the frontend to port 8000:

1. Edit the service file:
```bash
sudo nano /etc/systemd/system/rag-frontend.service
# Change: --port 80 to --port 8000
```

2. Update Security Group:
```
- Port 8000: 0.0.0.0/0 (instead of port 80)
```

3. Access becomes:
```
http://YOUR-EC2-PUBLIC-IP:8000/
```

## Troubleshooting

### Port 80 Permission Issues
```bash
# Check if port 80 is available
sudo netstat -tulpn | grep :80

# If another service is using port 80 (like Apache)
sudo systemctl stop apache2  # Ubuntu
sudo systemctl stop httpd    # CentOS/Amazon Linux
```

### Service Won't Start
```bash
# Check service status
sudo systemctl status rag-frontend

# Check logs for errors
sudo journalctl -u rag-frontend --no-pager -l

# Test manual startup
sudo -u raguser /opt/chainlit_rag/venv/bin/chainlit run chainlit_app.py --host 0.0.0.0 --port 8000
```

### Security Group Issues
- Ensure port 80 is open to `0.0.0.0/0` in AWS Console
- Check EC2 → Security Groups → Inbound Rules

### Connection Refused
```bash
# Check if services are running
sudo systemctl is-active rag-api rag-frontend

# Check if ports are listening
sudo netstat -tulpn | grep -E ':(80|8080)'

# Test from EC2 instance
curl -v http://localhost/
```

## Monitoring

### Health Checks
```bash
# API health
curl -f http://localhost:8080/health

# Frontend health
curl -f http://localhost/

# External health (replace with your IP)
curl -f http://YOUR-EC2-PUBLIC-IP/
curl -f http://YOUR-EC2-PUBLIC-IP:8080/health
```

### Resource Usage
```bash
# Check memory and CPU
free -h
htop

# Check service resource usage
sudo systemctl status rag-api rag-frontend
```

### Log Monitoring
```bash
# Real-time logs
sudo journalctl -u rag-api -u rag-frontend -f

# Recent errors
sudo journalctl -u rag-api --since "1 hour ago" | grep -i error
sudo journalctl -u rag-frontend --since "1 hour ago" | grep -i error
```

## Performance Considerations

### **Advantages of Direct Access:**
- ✅ No additional proxy layer
- ✅ Slightly lower latency
- ✅ Simpler architecture
- ✅ Fewer moving parts

### **Considerations:**
- ⚠️ No built-in load balancing
- ⚠️ No automatic SSL termination
- ⚠️ No advanced caching
- ⚠️ Limited security headers

### **For Production:**
Consider adding:
- SSL/HTTPS certificates
- Rate limiting at application level
- Monitoring and alerting
- Backup and recovery procedures

## SSL/HTTPS Setup (Optional)

If you want HTTPS without Nginx:

### 1. Get SSL Certificate
```bash
# Install Certbot
sudo apt install certbot

# Get certificate (requires domain name)
sudo certbot certonly --standalone -d your-domain.com
```

### 2. Configure Chainlit for HTTPS
```bash
# Update service to use SSL
sudo nano /etc/systemd/system/rag-frontend.service

# Change ExecStart to:
ExecStart=/opt/chainlit_rag/venv/bin/chainlit run chainlit_app.py \
    --host 0.0.0.0 \
    --port 443 \
    --ssl-keyfile /etc/letsencrypt/live/your-domain.com/privkey.pem \
    --ssl-certfile /etc/letsencrypt/live/your-domain.com/fullchain.pem \
    --headless
```

### 3. Update Security Group
```
- Port 443 (HTTPS): 0.0.0.0/0
```

Your RAG application is now accessible directly without Nginx!
