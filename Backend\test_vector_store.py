#!/usr/bin/env python
"""
Test script for vector store functionality in the RAG application.
This script verifies that the vector store is properly configured and works as expected.
"""

import os
import logging
import json
from dotenv import load_dotenv
from vectorstore_utils import load_vectorstore, load_vectorstore_client, get_bedrock_embeddings
from langchain.schema import Document

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("test_vector_store.log")
    ]
)

logger = logging.getLogger(__name__)

def test_vector_store_connection():
    """Test connection to the vector store."""
    try:
        logger.info("Testing vector store connection")
        client = load_vectorstore_client()
        if client:
            logger.info("Successfully connected to vector store")
            return True
        else:
            logger.error("Failed to connect to vector store")
            return False
    except Exception as e:
        logger.error(f"Error connecting to vector store: {e}")
        return False

def test_embeddings():
    """Test embedding functionality."""
    try:
        logger.info("Testing embedding functionality")
        embeddings = get_bedrock_embeddings()
        
        # Test embedding a simple text
        test_text = "This is a test text for embedding"
        vector = embeddings.embed_query(test_text)
        
        logger.info(f"Successfully generated embedding with {len(vector)} dimensions")
        return True
    except Exception as e:
        logger.error(f"Error testing embeddings: {e}")
        return False

def test_vector_search():
    """Test vector search functionality."""
    try:
        logger.info("Testing vector search functionality")
        vector_store = load_vectorstore()
        
        if not vector_store:
            logger.error("Failed to load vector store")
            return False
        
        # Test a simple query
        query = "AWS EC2 instance"
        results = vector_store.similarity_search(query, k=3)
        
        if results:
            logger.info(f"Successfully retrieved {len(results)} documents")
            for i, doc in enumerate(results):
                logger.info(f"Document {i+1}: {doc.page_content[:100]}... (truncated)")
            return True
        else:
            logger.warning("No documents found in vector store")
            return False
    except Exception as e:
        logger.error(f"Error testing vector search: {e}")
        return False

def test_add_and_retrieve():
    """Test adding and retrieving documents from the vector store."""
    try:
        logger.info("Testing adding and retrieving documents")
        vector_store = load_vectorstore()
        
        if not vector_store:
            logger.error("Failed to load vector store")
            return False
        
        # Create test documents
        test_docs = [
            Document(
                page_content="AWS EC2 (Elastic Compute Cloud) is a web service that provides resizable compute capacity in the cloud.",
                metadata={"source": "test_doc_1", "category": "compute"}
            ),
            Document(
                page_content="AWS S3 (Simple Storage Service) is an object storage service that offers industry-leading scalability, data availability, security, and performance.",
                metadata={"source": "test_doc_2", "category": "storage"}
            )
        ]
        
        # Extract texts and metadata
        texts = [doc.page_content for doc in test_docs]
        metadatas = [doc.metadata for doc in test_docs]
        
        # Add documents to vector store
        ids = vector_store.add_texts(texts, metadatas)
        logger.info(f"Added {len(ids)} documents to vector store")
        
        # Test retrieval
        query = "What is AWS EC2?"
        results = vector_store.similarity_search(query, k=1)
        
        if results and len(results) > 0:
            logger.info(f"Successfully retrieved document: {results[0].page_content}")
            return True
        else:
            logger.warning("Failed to retrieve added document")
            return False
    except Exception as e:
        logger.error(f"Error testing add and retrieve: {e}")
        return False

def main():
    """Main function to run the vector store tests."""
    logger.info("Starting vector store tests")
    
    # Test vector store connection
    connection_result = test_vector_store_connection()
    
    if not connection_result:
        logger.error("Vector store connection test failed. Skipping remaining tests.")
        return 1
    
    # Test embeddings
    embeddings_result = test_embeddings()
    
    # Test vector search
    search_result = test_vector_search()
    
    # Test adding and retrieving documents
    add_retrieve_result = test_add_and_retrieve()
    
    # Summarize results
    results = {
        "connection_test": connection_result,
        "embeddings_test": embeddings_result,
        "search_test": search_result,
        "add_retrieve_test": add_retrieve_result
    }
    
    logger.info(f"Test results: {json.dumps(results)}")
    
    # Overall success if all tests passed
    success = all(results.values())
    logger.info(f"Vector store tests {'succeeded' if success else 'failed'}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())