# RAG System for AWS Cloud Documentation

A Retrieval-Augmented Generation (RAG) system built with FastAPI, LangChain, and AWS services for processing and querying AWS cloud infrastructure documentation.

## Features

### Backend (FastAPI)
- Document ingestion from S3 bucket
- Text chunking and embedding generation using AWS Bedrock
- Vector storage using Qdrant
- PDF and DOCX parsing using Unstructured.io with optimized strategies
- FastAPI endpoints for ingestion and querying
- Duplicate content filtering and relevance scoring
- Advanced retrieval strategies (hybrid search, MMR)
- LangChain integration with prompt templates

### Frontend (Chainlit)
- Interactive chat interface for AWS documentation queries
- Multiple query modes (Standard, Diverse, Advanced, Quick)
- Real-time responses with source citations
- Professional AWS-themed UI design
- Chat profiles and starter questions
- Comprehensive error handling and troubleshooting

## Prerequisites

- Python 3.8+
- AWS Account with access to:
  - S3
  - Bedrock
- Required Python packages (see requirements.txt)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/rag-genomic.git
cd rag-genomic
```

2. Create and activate a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Install system dependencies for Unstructured (optional but recommended):
```bash
# For Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y libmagic-dev poppler-utils tesseract-ocr libreoffice pandoc

# For macOS
brew install libmagic poppler tesseract libreoffice pandoc
```

5. Create a `.env` file with your AWS credentials and configuration:
```env
AWS_REGION=your-region
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
BEDROCK_EMBEDDING_MODEL_ID=your-model-id
S3_BUCKET_NAME=your-bucket-name
QDRANT_PATH=./qdrant_data
```

## Document Processing

The system uses Unstructured.io exclusively for parsing PDF and DOCX documents:

- **PDF Processing**: Uses Unstructured's `partition_pdf` with the "fast" strategy and table structure inference
- **DOCX Processing**: Uses Unstructured's `partition_docx` with cleaning for improved text quality
- **Output Format**: Documents are converted to markdown format for better structure preservation
- **Metadata**: Document source, file type, and other metadata are preserved in the vector store
- **No Fallback Parsers**: The system relies exclusively on Unstructured.io's robust document processing capabilities

## Usage

### Option 1: Interactive Frontend (Recommended)

1. Start the FastAPI backend:
```bash
python main.py
```

2. Start the Chainlit frontend:
```bash
python start_chainlit.py
```

3. Open your browser to http://localhost:8000 and start chatting!

### Option 2: API Endpoints

1. Start the FastAPI server:
```bash
python main.py
```

2. Ingest documents:
```bash
curl -X POST http://localhost:8888/ingest
```

3. Query the system:
```bash
curl -X POST http://localhost:8888/query \
  -H "Content-Type: application/json" \
  -d '{"question": "your question here", "k": 3}'
```

## API Endpoints

- `POST /ingest`: Ingest documents from S3 bucket
- `POST /query`: Enhanced query with LangChain retrieval chains
- `POST /query/legacy`: Legacy query endpoint for backward compatibility
- `POST /query/advanced`: Advanced query with configurable retrieval strategies
- `GET /health`: Health check endpoint

## Project Structure

```
RAG_Quadrant/
├── Backend (FastAPI)
│   ├── main.py                      # FastAPI application and endpoints
│   ├── ingest.py                    # Document ingestion and processing
│   ├── query.py                     # Query engine implementation
│   ├── aws_utils.py                 # AWS utilities and S3 operations
│   ├── prompts.py                   # LangChain prompt templates
│   ├── advanced_retrieval.py        # Advanced retrieval strategies
│   └── vectorstore_utils.py         # Vector store utilities
├── Frontend (Chainlit)
│   ├── chainlit_app.py              # Main Chainlit application
│   ├── chainlit_settings.py         # Chat profiles and configuration
│   ├── start_chainlit.py            # Startup script with health checks
│   └── public/style.css             # Custom CSS styling
├── Configuration
│   ├── .chainlit/config.toml        # Chainlit configuration
│   ├── .env                         # Environment variables
│   └── .env.example                 # Environment template
├── Documentation
│   ├── README.md                    # Main documentation
│   └── CHAINLIT_README.md           # Frontend-specific documentation
└── Tests
    ├── test_core_functionality.py   # Core functionality tests
    ├── test_api_endpoints.py        # API endpoint tests
    ├── test_langchain_integration.py # LangChain integration tests
    └── final_verification_test.py   # Final verification tests
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- FastAPI
- LangChain
- AWS Bedrock
- Qdrant
- Unstructured.io 