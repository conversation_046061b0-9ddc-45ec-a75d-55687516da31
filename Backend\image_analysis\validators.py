"""
Validators for image files and content.
"""
import os
import re
import imghdr
import logging
import hashlib
from typing import List, Tuple, Optional, Dict, Any
from fastapi import UploadFile, HTTPException
from PIL import Image


# Configure logging
logger = logging.getLogger(__name__)


# Constants for validation
MAX_IMAGE_SIZE_MB = 10  # Maximum file size in MB
MAX_IMAGE_DIMENSION = 5000  # Maximum width or height in pixels
ALLOWED_IMAGE_TYPES = ["png", "jpeg", "jpg", "svg"]
ALLOWED_MIME_TYPES = [
    "image/png", 
    "image/jpeg", 
    "image/jpg", 
    "image/svg+xml"
]

# Suspicious patterns in SVG files that could indicate malicious content
SVG_SUSPICIOUS_PATTERNS = [
    r'<script',
    r'javascript:',
    r'data:',
    r'xlink:href=',
    r'onload=',
    r'onerror=',
    r'eval\(',
]


class ImageValidator:
    """
    Validates image files for security and compatibility.
    """
    
    @staticmethod
    def validate_file_type(file: UploadFile) -> bool:
        """
        Validate that the file has an allowed MIME type.
        
        Args:
            file: The uploaded file to validate
            
        Returns:
            bool: True if valid, raises HTTPException otherwise
        """
        if file.content_type not in ALLOWED_MIME_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"File type not allowed. Allowed types: {', '.join(ALLOWED_IMAGE_TYPES)}"
            )
        return True
    
    @staticmethod
    def validate_file_size(file: UploadFile) -> bool:
        """
        Validate that the file size is within allowed limits.
        
        Args:
            file: The uploaded file to validate
            
        Returns:
            bool: True if valid, raises HTTPException otherwise
        """
        # Get file size
        file.file.seek(0, os.SEEK_END)
        file_size = file.file.tell()
        file.file.seek(0)  # Reset file pointer
        
        max_size_bytes = MAX_IMAGE_SIZE_MB * 1024 * 1024
        
        if file_size > max_size_bytes:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size allowed: {MAX_IMAGE_SIZE_MB}MB"
            )
        return True
    
    @staticmethod
    def validate_image_content(file: UploadFile) -> bool:
        """
        Validate image content by opening it with PIL.
        This helps detect malformed images or potential security issues.
        
        Args:
            file: The uploaded file to validate
            
        Returns:
            bool: True if valid, raises HTTPException otherwise
        """
        # Handle SVG files separately
        if file.content_type == "image/svg+xml":
            return ImageValidator.validate_svg_content(file)
            
        try:
            # Read file content
            content = file.file.read()
            file.file.seek(0)  # Reset file pointer
            
            # Check if it's a valid image format
            img_format = imghdr.what(None, h=content)
            if img_format not in ["png", "jpeg"]:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid image content"
                )
                
            # Try to open the image with PIL
            img = Image.open(file.file)
            
            # Check image dimensions
            width, height = img.size
            if width > MAX_IMAGE_DIMENSION or height > MAX_IMAGE_DIMENSION:
                raise HTTPException(
                    status_code=400,
                    detail=f"Image dimensions too large. Maximum allowed: {MAX_IMAGE_DIMENSION}x{MAX_IMAGE_DIMENSION} pixels"
                )
            
            # Reset file pointer
            file.file.seek(0)
            
            return True
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.warning(f"Invalid image content: {str(e)}")
            raise HTTPException(
                status_code=400,
                detail=f"Invalid image content: {str(e)}"
            )
            
    @staticmethod
    def validate_svg_content(file: UploadFile) -> bool:
        """
        Validate SVG content for potential security issues.
        
        Args:
            file: The uploaded SVG file to validate
            
        Returns:
            bool: True if valid, raises HTTPException otherwise
        """
        try:
            # Read SVG content
            content = file.file.read()
            file.file.seek(0)  # Reset file pointer
            
            # Convert bytes to string for regex matching
            svg_text = content.decode('utf-8', errors='ignore')
            
            # Check for suspicious patterns
            for pattern in SVG_SUSPICIOUS_PATTERNS:
                if re.search(pattern, svg_text, re.IGNORECASE):
                    logger.warning(f"Suspicious pattern found in SVG: {pattern}")
                    raise HTTPException(
                        status_code=400,
                        detail="SVG contains potentially malicious content"
                    )
            
            return True
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.warning(f"Invalid SVG content: {str(e)}")
            raise HTTPException(
                status_code=400,
                detail=f"Invalid SVG content: {str(e)}"
            )
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """
        Sanitize a filename to prevent path traversal and other security issues.
        
        Args:
            filename: The original filename
            
        Returns:
            str: Sanitized filename
        """
        # Remove path components
        filename = os.path.basename(filename)
        
        # Replace potentially dangerous characters
        filename = re.sub(r'[^\w\.-]', '_', filename)
        
        # Ensure the filename is not empty
        if not filename:
            filename = "unnamed_file"
            
        return filename
    
    @staticmethod
    def compute_file_hash(file: UploadFile) -> str:
        """
        Compute a hash of the file content for integrity checks.
        
        Args:
            file: The uploaded file
            
        Returns:
            str: SHA-256 hash of the file content
        """
        try:
            # Read file content
            content = file.file.read()
            file.file.seek(0)  # Reset file pointer
            
            # Compute SHA-256 hash
            file_hash = hashlib.sha256(content).hexdigest()
            
            return file_hash
        except Exception as e:
            logger.error(f"Error computing file hash: {str(e)}")
            file.file.seek(0)  # Reset file pointer
            return ""
    
    @staticmethod
    def get_image_metadata(file: UploadFile) -> Dict[str, Any]:
        """
        Extract metadata from an image file.
        
        Args:
            file: The uploaded image file
            
        Returns:
            Dict[str, Any]: Dictionary containing image metadata
        """
        metadata = {
            "filename": file.filename,
            "content_type": file.content_type,
            "sanitized_filename": ImageValidator.sanitize_filename(file.filename or ""),
            "file_hash": ""
        }
        
        # Skip detailed metadata for SVG files
        if file.content_type == "image/svg+xml":
            metadata["file_hash"] = ImageValidator.compute_file_hash(file)
            return metadata
            
        try:
            # Compute file hash
            metadata["file_hash"] = ImageValidator.compute_file_hash(file)
            
            # Extract image properties
            img = Image.open(file.file)
            file.file.seek(0)  # Reset file pointer
            
            metadata.update({
                "format": img.format,
                "mode": img.mode,
                "width": img.width,
                "height": img.height,
                "aspect_ratio": round(img.width / img.height, 2) if img.height > 0 else 0
            })
            
            # Extract EXIF data if available
            if hasattr(img, "_getexif") and callable(img._getexif):
                exif = img._getexif()
                if exif:
                    # Include only safe EXIF data
                    safe_exif = {}
                    for tag, value in exif.items():
                        tag_name = str(tag)
                        # Convert value to string to ensure it's serializable
                        safe_exif[tag_name] = str(value)
                    metadata["exif"] = safe_exif
            
        except Exception as e:
            logger.warning(f"Error extracting image metadata: {str(e)}")
            
        return metadata
    
    @classmethod
    def validate_image(cls, file: UploadFile) -> bool:
        """
        Run all validation checks on an uploaded image file.
        
        Args:
            file: The uploaded file to validate
            
        Returns:
            bool: True if all validations pass, raises HTTPException otherwise
        """
        cls.validate_file_type(file)
        cls.validate_file_size(file)
        cls.validate_image_content(file)
        return True