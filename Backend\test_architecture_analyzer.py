"""
Unit tests for the architecture diagram analyzer.
"""
import pytest
from unittest.mock import MagicMock, patch
import networkx as nx

from image_analysis.architecture_analyzer import ArchitectureDiagramAnalyzer, ArchitecturePattern
from image_analysis.diagram_components import DiagramComponent, DiagramConnection, ComponentType
from image_analysis.models import BoundingBox


class TestArchitectureDiagramAnalyzer:
    """Test cases for the ArchitectureDiagramAnalyzer class."""
    
    @pytest.fixture
    def analyzer(self):
        """Create an ArchitectureDiagramAnalyzer instance for testing."""
        return ArchitectureDiagramAnalyzer()
    
    @pytest.fixture
    def sample_components(self):
        """Create sample diagram components for testing."""
        frontend = DiagramComponent(
            component_id="component_0",
            component_type=ComponentType.BOX,
            bounding_box=BoundingBox(x=50, y=50, width=100, height=50),
            confidence=0.9,
            text="Web Frontend"
        )
        
        backend = DiagramComponent(
            component_id="component_1",
            component_type=ComponentType.BOX,
            bounding_box=BoundingBox(x=200, y=50, width=100, height=50),
            confidence=0.9,
            text="API Server"
        )
        
        database = DiagramComponent(
            component_id="component_2",
            component_type=ComponentType.CIRCLE,
            bounding_box=BoundingBox(x=350, y=50, width=80, height=80),
            confidence=0.9,
            text="Database"
        )
        
        queue = DiagramComponent(
            component_id="component_3",
            component_type=ComponentType.BOX,
            bounding_box=BoundingBox(x=200, y=150, width=100, height=50),
            confidence=0.9,
            text="Message Queue"
        )
        
        worker = DiagramComponent(
            component_id="component_4",
            component_type=ComponentType.BOX,
            bounding_box=BoundingBox(x=350, y=150, width=100, height=50),
            confidence=0.9,
            text="Worker Service"
        )
        
        return [frontend, backend, database, queue, worker]
    
    @pytest.fixture
    def sample_connections(self):
        """Create sample diagram connections for testing."""
        frontend_to_backend = DiagramConnection(
            connection_id="connection_0",
            source_id="component_0",
            target_id="component_1",
            connection_type="arrow",
            points=[(150, 75), (200, 75)],
            confidence=0.8,
            text="HTTP"
        )
        
        backend_to_database = DiagramConnection(
            connection_id="connection_1",
            source_id="component_1",
            target_id="component_2",
            connection_type="arrow",
            points=[(300, 75), (350, 75)],
            confidence=0.8,
            text="SQL"
        )
        
        backend_to_queue = DiagramConnection(
            connection_id="connection_2",
            source_id="component_1",
            target_id="component_3",
            connection_type="arrow",
            points=[(250, 100), (250, 150)],
            confidence=0.8,
            text="Publish"
        )
        
        queue_to_worker = DiagramConnection(
            connection_id="connection_3",
            source_id="component_3",
            target_id="component_4",
            connection_type="arrow",
            points=[(300, 175), (350, 175)],
            confidence=0.8,
            text="Consume"
        )
        
        worker_to_database = DiagramConnection(
            connection_id="connection_4",
            source_id="component_4",
            target_id="component_2",
            connection_type="arrow",
            points=[(400, 150), (400, 90)],
            confidence=0.8,
            text="Update"
        )
        
        return [frontend_to_backend, backend_to_database, backend_to_queue, queue_to_worker, worker_to_database]
    
    def test_init(self, analyzer):
        """Test initialization of the analyzer."""
        assert isinstance(analyzer, ArchitectureDiagramAnalyzer)
    
    def test_classify_component_roles(self, analyzer, sample_components):
        """Test component role classification."""
        # Act
        roles = analyzer._classify_component_roles(sample_components)
        
        # Assert
        assert roles["component_0"] == "frontend"
        assert roles["component_1"] == "api"
        assert roles["component_2"] == "database"
        assert roles["component_3"] == "queue"
        assert roles["component_4"] == "api"  # "Worker Service" contains "service" which matches API
    
    def test_build_architecture_graph(self, analyzer, sample_components, sample_connections):
        """Test building the architecture graph."""
        # Act
        graph = analyzer._build_architecture_graph(sample_components, sample_connections)
        
        # Assert
        assert isinstance(graph, nx.DiGraph)
        assert len(graph.nodes) == 5
        assert len(graph.edges) == 5
        
        # Check node attributes
        assert graph.nodes["component_0"]["text"] == "Web Frontend"
        assert graph.nodes["component_1"]["text"] == "API Server"
        
        # Check edge attributes
        assert graph.edges["component_0", "component_1"]["text"] == "HTTP"
        assert graph.edges["component_1", "component_2"]["text"] == "SQL"
    
    def test_identify_architecture_patterns(self, analyzer, sample_components, sample_connections):
        """Test identification of architecture patterns."""
        # Arrange
        component_roles = {
            "component_0": "frontend",
            "component_1": "backend",  # Changed to backend to match three-tier pattern
            "component_2": "database",
            "component_3": "queue",
            "component_4": "backend"
        }
        graph = analyzer._build_architecture_graph(sample_components, sample_connections)
        
        # Act
        patterns = analyzer._identify_architecture_patterns(sample_components, sample_connections, component_roles, graph)
        
        # Assert
        assert len(patterns) > 0
        
        # Check for three-tier pattern
        three_tier_pattern = next((p for p in patterns if p.name == "Three-Tier Architecture"), None)
        assert three_tier_pattern is not None
        assert "component_0" in three_tier_pattern.components
        assert "component_1" in three_tier_pattern.components
        assert "component_2" in three_tier_pattern.components
        
        # Check for event-driven pattern
        event_driven_pattern = next((p for p in patterns if p.name == "Event-Driven Architecture"), None)
        assert event_driven_pattern is not None
        assert "component_3" in event_driven_pattern.components
        assert "component_4" in event_driven_pattern.components
    
    def test_analyze_component_relationships(self, analyzer, sample_components, sample_connections):
        """Test analysis of component relationships."""
        # Arrange
        graph = analyzer._build_architecture_graph(sample_components, sample_connections)
        
        # Act
        relationships = analyzer._analyze_component_relationships(sample_components, sample_connections, graph)
        
        # Assert
        assert len(relationships) > 0
        
        # Check for central component
        central_components = [r for r in relationships if r["type"] == "central_component"]
        assert len(central_components) > 0
        
        # The API server should be identified as a central component
        api_server = next((c for c in central_components if c["component_id"] == "component_1"), None)
        assert api_server is not None
    
    def test_identify_potential_issues(self, analyzer, sample_components, sample_connections):
        """Test identification of potential issues."""
        # Arrange
        graph = analyzer._build_architecture_graph(sample_components, sample_connections)
        
        # Act
        issues = analyzer._identify_potential_issues(sample_components, sample_connections, graph)
        
        # Assert
        assert isinstance(issues, list)
        
        # In our sample architecture, the API server might be identified as a single point of failure
        spof_issues = [i for i in issues if i["type"] == "single_point_of_failure"]
        if spof_issues:
            assert any(i["component_id"] == "component_1" for i in spof_issues)
    
    def test_generate_summary(self, analyzer, sample_components, sample_connections):
        """Test summary generation."""
        # Arrange
        component_roles = {
            "component_0": "frontend",
            "component_1": "api",
            "component_2": "database",
            "component_3": "queue",
            "component_4": "backend"
        }
        patterns = [
            ArchitecturePattern(
                name="Three-Tier Architecture",
                confidence=0.9,
                components=["component_0", "component_1", "component_2"],
                description="Classic three-tier architecture"
            ),
            ArchitecturePattern(
                name="Event-Driven Architecture",
                confidence=0.85,
                components=["component_1", "component_3", "component_4"],
                description="Event-driven architecture"
            )
        ]
        
        # Act
        summary = analyzer._generate_summary(sample_components, sample_connections, patterns, component_roles)
        
        # Assert
        assert "Architecture diagram with 5 components and 5 connections" in summary
        assert "Three-Tier Architecture" in summary
        assert "Event-Driven Architecture" in summary
    
    def test_generate_details(self, analyzer, sample_components, sample_connections):
        """Test details generation."""
        # Arrange
        patterns = [
            ArchitecturePattern(
                name="Three-Tier Architecture",
                confidence=0.9,
                components=["component_0", "component_1", "component_2"],
                description="Classic three-tier architecture"
            )
        ]
        relationships = [
            {
                "type": "central_component",
                "component_id": "component_1",
                "component_text": "API Server",
                "score": 0.8,
                "description": "Central component with connections to 3 other components"
            }
        ]
        
        # Act
        details = analyzer._generate_details(sample_components, sample_connections, patterns, relationships)
        
        # Assert
        assert len(details) > 0
        assert any(d["type"] == "pattern" and "Three-Tier Architecture" in d["content"] for d in details)
        assert any(d["type"] == "relationship" and "Central component" in d["content"] for d in details)
        assert any(d["type"] == "component" for d in details)
    
    def test_generate_recommendations(self, analyzer, sample_components, sample_connections):
        """Test recommendations generation."""
        # Arrange
        issues = [
            {
                "type": "single_point_of_failure",
                "component_id": "component_1",
                "component_text": "API Server",
                "severity": "high",
                "description": "Potential single point of failure"
            }
        ]
        
        # Act
        recommendations = analyzer._generate_recommendations(sample_components, sample_connections, issues)
        
        # Assert
        assert len(recommendations) > 0
        assert any(r["type"] == "improvement" and "redundancy" in r["content"] for r in recommendations)
    
    def test_analyze_architecture(self, analyzer, sample_components, sample_connections):
        """Test the complete architecture analysis."""
        # Arrange
        labels = {}
        
        # Act
        result = analyzer.analyze_architecture(sample_components, sample_connections, labels)
        
        # Assert
        assert "summary" in result
        assert "details" in result
        assert "recommendations" in result
        assert "component_roles" in result
        assert "patterns" in result
        assert "relationships" in result
        assert "issues" in result
        
        # Check summary
        assert "Architecture diagram with 5 components" in result["summary"]
        
        # Check details
        assert len(result["details"]) > 0
        
        # Check recommendations
        assert isinstance(result["recommendations"], list)
    
    def test_analyze_architecture_with_error(self, analyzer):
        """Test error handling in architecture analysis."""
        # Arrange
        # Create a situation that will cause an error
        components = None
        connections = None
        labels = {}
        
        # Act
        result = analyzer.analyze_architecture(components, connections, labels)
        
        # Assert
        assert "summary" in result
        assert "Failed to analyze" in result["summary"]
        assert "details" in result
        assert "recommendations" in result
    
    def test_architecture_pattern_to_dict(self):
        """Test conversion of ArchitecturePattern to dictionary."""
        # Arrange
        pattern = ArchitecturePattern(
            name="Test Pattern",
            confidence=0.9,
            components=["component_1", "component_2"],
            description="Test description"
        )
        
        # Act
        pattern_dict = pattern.to_dict()
        
        # Assert
        assert pattern_dict["name"] == "Test Pattern"
        assert pattern_dict["confidence"] == 0.9
        assert pattern_dict["components"] == ["component_1", "component_2"]
        assert pattern_dict["description"] == "Test description"