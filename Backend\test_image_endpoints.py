"""
Comprehensive test suite for all image-related endpoints in the FastAPI application.

This test suite covers:
1. /query/image - OCR text extraction and retrieval queries
2. /analyze/image - AWS Bedrock vision model analysis
3. /analyze/image/openrouter - OpenRouter API vision analysis
4. Image analysis router endpoints from image_analysis.api

Tests include both success and failure scenarios, proper error handling,
file validation, and API credential configuration checks.
"""

import pytest
import asyncio
import os
import io
import json
import tempfile
import logging
from typing import Dict, Any, Optional
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi.testclient import TestClient
from fastapi import UploadFile
from PIL import Image, ImageDraw, ImageFont
import requests

# Import the FastAPI app
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from main import app

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create test client
client = TestClient(app)

class TestImageEndpoints:
    """Test class for all image-related endpoints."""
    
    @classmethod
    def setup_class(cls):
        """Set up test class with sample images and configurations."""
        cls.test_images = cls._create_test_images()
        cls.original_env = {}
        
    @classmethod
    def teardown_class(cls):
        """Clean up test resources."""
        # Clean up test images
        for image_info in cls.test_images.values():
            if 'path' in image_info and os.path.exists(image_info['path']):
                os.unlink(image_info['path'])
    
    @staticmethod
    def _create_test_images() -> Dict[str, Dict[str, Any]]:
        """Create various test images for different scenarios."""
        test_images = {}
        
        # 1. Valid PNG image with text
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)
        try:
            # Try to use a default font, fallback to basic if not available
            font = ImageFont.load_default()
        except:
            font = None
        
        draw.text((50, 50), "Sample Architecture Diagram", fill='black', font=font)
        draw.text((50, 100), "Database -> API -> Frontend", fill='black', font=font)
        draw.text((50, 150), "Error: Connection timeout", fill='red', font=font)
        
        # Add some basic shapes to simulate a diagram
        draw.rectangle([100, 200, 200, 250], outline='blue', width=2)
        draw.text((110, 215), "Database", fill='blue', font=font)
        
        draw.rectangle([300, 200, 400, 250], outline='green', width=2)
        draw.text((320, 215), "API", fill='green', font=font)
        
        draw.rectangle([500, 200, 600, 250], outline='purple', width=2)
        draw.text((520, 215), "Frontend", fill='purple', font=font)
        
        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        img.save(temp_file.name, 'PNG')
        temp_file.close()
        
        test_images['valid_png'] = {
            'path': temp_file.name,
            'content_type': 'image/png',
            'expected_text': 'Sample Architecture Diagram',
            'size': os.path.getsize(temp_file.name)
        }
        
        # 2. Valid JPEG image
        img_jpeg = Image.new('RGB', (400, 300), color='lightblue')
        draw_jpeg = ImageDraw.Draw(img_jpeg)
        draw_jpeg.text((50, 50), "JPEG Test Image", fill='black', font=font)
        draw_jpeg.text((50, 100), "Error 404: Not Found", fill='red', font=font)
        
        temp_file_jpeg = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
        img_jpeg.save(temp_file_jpeg.name, 'JPEG')
        temp_file_jpeg.close()
        
        test_images['valid_jpeg'] = {
            'path': temp_file_jpeg.name,
            'content_type': 'image/jpeg',
            'expected_text': 'JPEG Test Image',
            'size': os.path.getsize(temp_file_jpeg.name)
        }
        
        # 3. Large image (for size validation testing)
        large_img = Image.new('RGB', (2000, 1500), color='yellow')
        draw_large = ImageDraw.Draw(large_img)
        draw_large.text((100, 100), "Large Image for Testing", fill='black', font=font)
        
        temp_file_large = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        large_img.save(temp_file_large.name, 'PNG')
        temp_file_large.close()
        
        test_images['large_image'] = {
            'path': temp_file_large.name,
            'content_type': 'image/png',
            'expected_text': 'Large Image for Testing',
            'size': os.path.getsize(temp_file_large.name)
        }
        
        # 4. Empty image (minimal content)
        empty_img = Image.new('RGB', (100, 100), color='white')
        temp_file_empty = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        empty_img.save(temp_file_empty.name, 'PNG')
        temp_file_empty.close()
        
        test_images['empty_image'] = {
            'path': temp_file_empty.name,
            'content_type': 'image/png',
            'expected_text': '',
            'size': os.path.getsize(temp_file_empty.name)
        }
        
        # 5. SVG image
        svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
        <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
            <rect x="50" y="50" width="100" height="60" fill="lightblue" stroke="blue"/>
            <text x="70" y="85" font-family="Arial" font-size="14" fill="black">Database</text>
            <rect x="200" y="50" width="100" height="60" fill="lightgreen" stroke="green"/>
            <text x="230" y="85" font-family="Arial" font-size="14" fill="black">API</text>
            <text x="50" y="150" font-family="Arial" font-size="12" fill="red">Error: Connection failed</text>
        </svg>'''
        
        temp_file_svg = tempfile.NamedTemporaryFile(suffix='.svg', delete=False, mode='w')
        temp_file_svg.write(svg_content)
        temp_file_svg.close()
        
        test_images['valid_svg'] = {
            'path': temp_file_svg.name,
            'content_type': 'image/svg+xml',
            'expected_text': '',  # SVG OCR might not work
            'size': os.path.getsize(temp_file_svg.name)
        }
        
        return test_images
    
    def _create_file_upload(self, image_key: str, filename: Optional[str] = None) -> tuple:
        """Create a file upload tuple for testing."""
        image_info = self.test_images[image_key]
        with open(image_info['path'], 'rb') as f:
            content = f.read()
        
        if filename is None:
            filename = os.path.basename(image_info['path'])
            
        return (
            'file',
            (filename, content, image_info['content_type'])
        )
    
    def _backup_env_var(self, var_name: str):
        """Backup an environment variable."""
        self.original_env[var_name] = os.environ.get(var_name)
    
    def _restore_env_var(self, var_name: str):
        """Restore an environment variable."""
        if var_name in self.original_env:
            if self.original_env[var_name] is not None:
                os.environ[var_name] = self.original_env[var_name]
            elif var_name in os.environ:
                del os.environ[var_name]

    # ==================== TESTS FOR /query/image ENDPOINT ====================

    def test_query_image_valid_png_success(self):
        """Test successful image query with valid PNG image."""
        logger.info("Testing /query/image with valid PNG image")

        file_upload = self._create_file_upload('valid_png')

        with patch('main.get_query_engine') as mock_get_engine:
            # Mock the query engine
            mock_engine = MagicMock()
            mock_engine.query_advanced.return_value = {
                "answer": "Based on the extracted text, this appears to be an architecture diagram showing a database, API, and frontend connection.",
                "sources": [{"content": "Sample document content", "metadata": {"source": "test.pdf"}}],
                "query_type": "text_query"
            }
            mock_get_engine.return_value = mock_engine

            # Mock OCR function
            with patch('main.extract_text_from_image_bytes') as mock_ocr:
                mock_ocr.return_value = "Sample Architecture Diagram\nDatabase -> API -> Frontend\nError: Connection timeout"

                response = client.post(
                    "/query/image",
                    files=[file_upload]
                )

        assert response.status_code == 200
        data = response.json()

        # Verify response structure
        assert "answer" in data
        assert "sources" in data
        assert "extracted_text" in data
        assert "query_type" in data

        # Verify extracted text is included
        assert data["extracted_text"] == "Sample Architecture Diagram\nDatabase -> API -> Frontend\nError: Connection timeout"

        # Verify query engine was called with extracted text
        mock_engine.query_advanced.assert_called_once()
        call_args = mock_engine.query_advanced.call_args
        assert call_args[1]["question"] == "Sample Architecture Diagram\nDatabase -> API -> Frontend\nError: Connection timeout"

        logger.info("✓ /query/image with valid PNG image test passed")

    def test_query_image_valid_jpeg_success(self):
        """Test successful image query with valid JPEG image."""
        logger.info("Testing /query/image with valid JPEG image")

        file_upload = self._create_file_upload('valid_jpeg')

        with patch('main.get_query_engine') as mock_get_engine:
            mock_engine = MagicMock()
            mock_engine.query_advanced.return_value = {
                "answer": "This appears to be a JPEG test image with an error message.",
                "sources": [],
                "query_type": "text_query"
            }
            mock_get_engine.return_value = mock_engine

            with patch('main.extract_text_from_image_bytes') as mock_ocr:
                mock_ocr.return_value = "JPEG Test Image\nError 404: Not Found"

                response = client.post(
                    "/query/image",
                    files=[file_upload]
                )

        assert response.status_code == 200
        data = response.json()
        assert "extracted_text" in data
        assert data["extracted_text"] == "JPEG Test Image\nError 404: Not Found"

        logger.info("✓ /query/image with valid JPEG image test passed")

    def test_query_image_no_text_extracted(self):
        """Test image query when no text can be extracted."""
        logger.info("Testing /query/image with no extractable text")

        file_upload = self._create_file_upload('empty_image')

        with patch('main.extract_text_from_image_bytes') as mock_ocr:
            mock_ocr.return_value = ""  # No text extracted

            response = client.post(
                "/query/image",
                files=[file_upload]
            )

        assert response.status_code == 200
        data = response.json()

        # Should return helpful message when no text is extracted
        assert "answer" in data
        assert "No text could be extracted" in data["answer"]
        assert data["extracted_text"] == ""
        assert data["images"] == []
        assert data["sources"] == []

        logger.info("✓ /query/image with no extractable text test passed")

    def test_query_image_no_file_uploaded(self):
        """Test image query endpoint with no file uploaded."""
        logger.info("Testing /query/image with no file")

        response = client.post("/query/image")

        assert response.status_code == 422  # Unprocessable Entity
        data = response.json()
        assert "detail" in data

        logger.info("✓ /query/image with no file test passed")

    def test_query_image_invalid_file_type(self):
        """Test image query with invalid file type."""
        logger.info("Testing /query/image with invalid file type")

        # Create a text file instead of an image
        text_content = b"This is not an image file"

        response = client.post(
            "/query/image",
            files=[("file", ("test.txt", text_content, "text/plain"))]
        )

        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "Unsupported file type" in data["detail"]

        logger.info("✓ /query/image with invalid file type test passed")

    def test_query_image_empty_file(self):
        """Test image query with empty file."""
        logger.info("Testing /query/image with empty file")

        response = client.post(
            "/query/image",
            files=[("file", ("empty.png", b"", "image/png"))]
        )

        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "Empty file uploaded" in data["detail"]

        logger.info("✓ /query/image with empty file test passed")

    def test_query_image_oversized_file(self):
        """Test image query with oversized file."""
        logger.info("Testing /query/image with oversized file")

        # Create a large file content (simulate 25MB file)
        large_content = b"x" * (25 * 1024 * 1024)

        response = client.post(
            "/query/image",
            files=[("file", ("large.png", large_content, "image/png"))]
        )

        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "exceeds the 20MB limit" in data["detail"]

        logger.info("✓ /query/image with oversized file test passed")

    def test_query_image_with_retrieval_config(self):
        """Test image query with custom retrieval configuration."""
        logger.info("Testing /query/image with retrieval config")

        file_upload = self._create_file_upload('valid_png')

        with patch('main.get_query_engine') as mock_get_engine:
            mock_engine = MagicMock()
            mock_engine.query_advanced.return_value = {
                "answer": "Custom retrieval response",
                "sources": [],
                "query_type": "advanced_query"
            }
            mock_get_engine.return_value = mock_engine

            with patch('main.extract_text_from_image_bytes') as mock_ocr:
                mock_ocr.return_value = "Test text"

                # Include retrieval config in form data
                response = client.post(
                    "/query/image",
                    files=[file_upload],
                    data={"retrieval_config": json.dumps({"k": 5, "score_threshold": 0.8})}
                )

        assert response.status_code == 200

        # Verify retrieval config was passed to query engine
        mock_engine.query_advanced.assert_called_once()
        call_args = mock_engine.query_advanced.call_args
        assert call_args[1]["retrieval_config"] is not None

        logger.info("✓ /query/image with retrieval config test passed")

    # ==================== TESTS FOR /analyze/image ENDPOINT ====================

    def test_analyze_image_success_with_aws_credentials(self):
        """Test successful image analysis with AWS Bedrock."""
        logger.info("Testing /analyze/image with valid AWS credentials")

        # Backup and set AWS credentials
        self._backup_env_var('AWS_ACCESS_KEY_ID')
        self._backup_env_var('AWS_SECRET_ACCESS_KEY')
        self._backup_env_var('AWS_REGION')

        os.environ['AWS_ACCESS_KEY_ID'] = 'test_access_key'
        os.environ['AWS_SECRET_ACCESS_KEY'] = 'test_secret_key'
        os.environ['AWS_REGION'] = 'us-east-1'

        try:
            file_upload = self._create_file_upload('valid_png')

            with patch('main.extract_text_from_image_bytes') as mock_ocr:
                mock_ocr.return_value = "Sample Architecture Diagram"

                with patch('main.analyze_image_with_bedrock') as mock_bedrock:
                    mock_bedrock.return_value = {
                        "status": "success",
                        "analysis": "This is an architecture diagram showing database, API, and frontend components with a connection timeout error.",
                        "model_id": "anthropic.claude-3-sonnet-20240229-v1:0"
                    }

                    response = client.post(
                        "/analyze/image",
                        files=[file_upload]
                    )

            assert response.status_code == 200
            data = response.json()

            # Verify response structure
            assert "extracted_text" in data
            assert "analysis" in data
            assert "status" in data
            assert "model_id" in data

            assert data["status"] == "success"
            assert data["extracted_text"] == "Sample Architecture Diagram"
            assert "architecture diagram" in data["analysis"].lower()

            # Verify Bedrock was called with image bytes
            mock_bedrock.assert_called_once()

            logger.info("✓ /analyze/image with AWS credentials test passed")

        finally:
            # Restore environment variables
            self._restore_env_var('AWS_ACCESS_KEY_ID')
            self._restore_env_var('AWS_SECRET_ACCESS_KEY')
            self._restore_env_var('AWS_REGION')

    def test_analyze_image_missing_aws_credentials(self):
        """Test image analysis with missing AWS credentials."""
        logger.info("Testing /analyze/image with missing AWS credentials")

        # Backup and remove AWS credentials
        self._backup_env_var('AWS_ACCESS_KEY_ID')
        self._backup_env_var('AWS_SECRET_ACCESS_KEY')
        self._backup_env_var('AWS_REGION')

        if 'AWS_ACCESS_KEY_ID' in os.environ:
            del os.environ['AWS_ACCESS_KEY_ID']
        if 'AWS_SECRET_ACCESS_KEY' in os.environ:
            del os.environ['AWS_SECRET_ACCESS_KEY']
        if 'AWS_REGION' in os.environ:
            del os.environ['AWS_REGION']

        try:
            file_upload = self._create_file_upload('valid_png')

            with patch('main.extract_text_from_image_bytes') as mock_ocr:
                mock_ocr.return_value = "Sample text"

                response = client.post(
                    "/analyze/image",
                    files=[file_upload]
                )

            assert response.status_code == 200
            data = response.json()

            # Should return error about missing credentials
            assert data["status"] == "error"
            assert "AWS credentials are not configured" in data["error"]
            assert data["extracted_text"] == "Sample text"

            logger.info("✓ /analyze/image with missing AWS credentials test passed")

        finally:
            # Restore environment variables
            self._restore_env_var('AWS_ACCESS_KEY_ID')
            self._restore_env_var('AWS_SECRET_ACCESS_KEY')
            self._restore_env_var('AWS_REGION')

    def test_analyze_image_bedrock_error(self):
        """Test image analysis when Bedrock returns an error."""
        logger.info("Testing /analyze/image with Bedrock error")

        # Set AWS credentials
        self._backup_env_var('AWS_ACCESS_KEY_ID')
        self._backup_env_var('AWS_SECRET_ACCESS_KEY')
        self._backup_env_var('AWS_REGION')

        os.environ['AWS_ACCESS_KEY_ID'] = 'test_access_key'
        os.environ['AWS_SECRET_ACCESS_KEY'] = 'test_secret_key'
        os.environ['AWS_REGION'] = 'us-east-1'

        try:
            file_upload = self._create_file_upload('valid_png')

            with patch('main.extract_text_from_image_bytes') as mock_ocr:
                mock_ocr.return_value = "Sample text"

                with patch('main.analyze_image_with_bedrock') as mock_bedrock:
                    mock_bedrock.return_value = {
                        "status": "error",
                        "error": "Model not found or access denied",
                        "analysis": "Failed to analyze image with AI vision model."
                    }

                    response = client.post(
                        "/analyze/image",
                        files=[file_upload]
                    )

            assert response.status_code == 200
            data = response.json()

            assert data["status"] == "error"
            assert "error" in data
            assert data["error"] == "Model not found or access denied"

            logger.info("✓ /analyze/image with Bedrock error test passed")

        finally:
            self._restore_env_var('AWS_ACCESS_KEY_ID')
            self._restore_env_var('AWS_SECRET_ACCESS_KEY')
            self._restore_env_var('AWS_REGION')

    # ==================== TESTS FOR /analyze/image/openrouter ENDPOINT ====================

    def test_analyze_image_openrouter_success(self):
        """Test successful image analysis with OpenRouter API."""
        logger.info("Testing /analyze/image/openrouter with valid API key")

        # Backup and set OpenRouter API key
        self._backup_env_var('OPENROUTER_API_KEY')
        os.environ['OPENROUTER_API_KEY'] = 'test_openrouter_key'

        try:
            file_upload = self._create_file_upload('valid_png')

            with patch('main.extract_text_from_image_bytes') as mock_ocr:
                mock_ocr.return_value = "Sample Architecture Diagram"

                with patch('main.analyze_image_with_openrouter') as mock_openrouter:
                    mock_openrouter.return_value = {
                        "status": "success",
                        "analysis": "This image shows an architecture diagram with database, API, and frontend components. There appears to be a connection timeout error indicated.",
                        "model_id": "mistralai/mistral-small-3.2-24b"
                    }

                    response = client.post(
                        "/analyze/image/openrouter",
                        files=[file_upload]
                    )

            assert response.status_code == 200
            data = response.json()

            # Verify response structure
            assert "extracted_text" in data
            assert "analysis" in data
            assert "status" in data
            assert "model_id" in data

            assert data["status"] == "success"
            assert data["extracted_text"] == "Sample Architecture Diagram"
            assert "architecture diagram" in data["analysis"].lower()
            assert data["model_id"] == "mistralai/mistral-small-3.2-24b"

            # Verify OpenRouter was called with image bytes and no custom prompt
            mock_openrouter.assert_called_once()
            call_args = mock_openrouter.call_args
            assert len(call_args[0]) == 2  # image_bytes and prompt
            assert call_args[0][1] is None  # prompt should be None

            logger.info("✓ /analyze/image/openrouter success test passed")

        finally:
            self._restore_env_var('OPENROUTER_API_KEY')

    def test_analyze_image_openrouter_with_custom_prompt(self):
        """Test OpenRouter image analysis with custom prompt."""
        logger.info("Testing /analyze/image/openrouter with custom prompt")

        self._backup_env_var('OPENROUTER_API_KEY')
        os.environ['OPENROUTER_API_KEY'] = 'test_openrouter_key'

        try:
            file_upload = self._create_file_upload('valid_png')
            custom_prompt = "Focus on identifying any error messages in this image"

            with patch('main.extract_text_from_image_bytes') as mock_ocr:
                mock_ocr.return_value = "Error: Connection timeout"

                with patch('main.analyze_image_with_openrouter') as mock_openrouter:
                    mock_openrouter.return_value = {
                        "status": "success",
                        "analysis": "I can see an error message indicating 'Connection timeout' in this image.",
                        "model_id": "mistralai/mistral-small-3.2-24b"
                    }

                    response = client.post(
                        "/analyze/image/openrouter",
                        files=[file_upload],
                        data={"prompt": custom_prompt}
                    )

            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"

            # Verify custom prompt was passed
            mock_openrouter.assert_called_once()
            call_args = mock_openrouter.call_args
            assert call_args[0][1] == custom_prompt

            logger.info("✓ /analyze/image/openrouter with custom prompt test passed")

        finally:
            self._restore_env_var('OPENROUTER_API_KEY')

    def test_analyze_image_openrouter_missing_api_key(self):
        """Test OpenRouter image analysis with missing API key."""
        logger.info("Testing /analyze/image/openrouter with missing API key")

        # Backup and remove OpenRouter API key
        self._backup_env_var('OPENROUTER_API_KEY')
        if 'OPENROUTER_API_KEY' in os.environ:
            del os.environ['OPENROUTER_API_KEY']

        try:
            file_upload = self._create_file_upload('valid_png')

            with patch('main.extract_text_from_image_bytes') as mock_ocr:
                mock_ocr.return_value = "Sample text"

                response = client.post(
                    "/analyze/image/openrouter",
                    files=[file_upload]
                )

            assert response.status_code == 200
            data = response.json()

            # Should return error about missing API key
            assert data["status"] == "error"
            assert "OpenRouter API key is not configured" in data["error"]
            assert data["extracted_text"] == "Sample text"

            logger.info("✓ /analyze/image/openrouter with missing API key test passed")

        finally:
            self._restore_env_var('OPENROUTER_API_KEY')

    def test_analyze_image_openrouter_api_error(self):
        """Test OpenRouter image analysis when API returns an error."""
        logger.info("Testing /analyze/image/openrouter with API error")

        self._backup_env_var('OPENROUTER_API_KEY')
        os.environ['OPENROUTER_API_KEY'] = 'test_openrouter_key'

        try:
            file_upload = self._create_file_upload('valid_png')

            with patch('main.extract_text_from_image_bytes') as mock_ocr:
                mock_ocr.return_value = "Sample text"

                with patch('main.analyze_image_with_openrouter') as mock_openrouter:
                    mock_openrouter.return_value = {
                        "status": "error",
                        "error": "API rate limit exceeded",
                        "analysis": "Failed to analyze image with OpenRouter API."
                    }

                    response = client.post(
                        "/analyze/image/openrouter",
                        files=[file_upload]
                    )

            assert response.status_code == 200
            data = response.json()

            assert data["status"] == "error"
            assert "error" in data
            assert data["error"] == "API rate limit exceeded"

            logger.info("✓ /analyze/image/openrouter with API error test passed")

        finally:
            self._restore_env_var('OPENROUTER_API_KEY')

    # ==================== TESTS FOR IMAGE ANALYSIS ROUTER ENDPOINTS ====================

    def test_image_analysis_router_upload_success(self):
        """Test successful image upload to image analysis router."""
        logger.info("Testing /api/image-analysis/ upload endpoint")

        file_upload = self._create_file_upload('valid_png')

        with patch('image_analysis.storage.TemporaryImageStore.store_image') as mock_store:
            mock_store.return_value = "/tmp/test_image.png"

            with patch('image_analysis.storage.AnalysisResultStore.store_request') as mock_store_request:
                mock_store_request.return_value = "test-request-id"

                with patch('image_analysis.processor.ImageProcessor.process_image_async') as mock_process:
                    response = client.post(
                        "/api/image-analysis/",
                        files=[file_upload]
                    )

        assert response.status_code == 200
        data = response.json()

        # Verify response structure
        assert "id" in data
        assert "status" in data
        assert "message" in data

        assert data["status"] == "pending"
        assert "uploaded successfully" in data["message"]

        logger.info("✓ /api/image-analysis/ upload test passed")

    def test_image_analysis_router_get_result_success(self):
        """Test getting analysis result from image analysis router."""
        logger.info("Testing /api/image-analysis/{request_id} get result endpoint")

        test_request_id = "test-request-id-123"

        with patch('image_analysis.storage.AnalysisResultStore.get_result') as mock_get_result:
            mock_get_result.return_value = {
                "id": "result-id-123",
                "request_id": test_request_id,
                "analysis_type": "architecture",
                "confidence": 0.85,
                "image_url": f"/api/image-analysis/image/{test_request_id}",
                "text_content": "Sample extracted text",
                "analysis": {
                    "summary": "This is an architecture diagram",
                    "details": [
                        {
                            "type": "component",
                            "content": "Database component detected",
                            "confidence": 0.9
                        }
                    ],
                    "recommendations": [
                        {
                            "type": "improvement",
                            "content": "Consider adding error handling",
                            "priority": 3
                        }
                    ]
                }
            }

            response = client.get(f"/api/image-analysis/{test_request_id}")

        assert response.status_code == 200
        data = response.json()

        # Verify response structure matches ImageAnalysisResult model
        assert "id" in data
        assert "request_id" in data
        assert "analysis_type" in data
        assert "confidence" in data
        assert "image_url" in data
        assert "text_content" in data
        assert "analysis" in data

        # Verify analysis structure
        analysis = data["analysis"]
        assert "summary" in analysis
        assert "details" in analysis
        assert "recommendations" in analysis

        assert data["request_id"] == test_request_id
        assert data["analysis_type"] == "architecture"
        assert data["confidence"] == 0.85

        logger.info("✓ /api/image-analysis/{request_id} get result test passed")

    def test_image_analysis_router_get_result_not_found(self):
        """Test getting analysis result for non-existent request."""
        logger.info("Testing /api/image-analysis/{request_id} with non-existent ID")

        test_request_id = "non-existent-id"

        with patch('image_analysis.storage.AnalysisResultStore.get_result') as mock_get_result:
            mock_get_result.return_value = None

            response = client.get(f"/api/image-analysis/{test_request_id}")

        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()

        logger.info("✓ /api/image-analysis/{request_id} not found test passed")

    def test_image_analysis_router_get_image_success(self):
        """Test getting image file from image analysis router."""
        logger.info("Testing /api/image-analysis/image/{request_id} get image endpoint")

        test_request_id = "test-request-id-123"

        with patch('image_analysis.storage.AnalysisResultStore.get_request') as mock_get_request:
            mock_get_request.return_value = MagicMock(
                storage_path=self.test_images['valid_png']['path'],
                content_type='image/png',
                original_filename='test.png'
            )

            response = client.get(f"/api/image-analysis/image/{test_request_id}")

        assert response.status_code == 200
        assert response.headers['content-type'] == 'image/png'

        logger.info("✓ /api/image-analysis/image/{request_id} get image test passed")

    def test_image_analysis_router_delete_success(self):
        """Test deleting analysis request and data."""
        logger.info("Testing DELETE /api/image-analysis/{request_id}")

        test_request_id = "test-request-id-123"

        with patch('image_analysis.storage.AnalysisResultStore.delete_request') as mock_delete:
            mock_delete.return_value = True

            response = client.delete(f"/api/image-analysis/{test_request_id}")

        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "deleted successfully" in data["message"]

        logger.info("✓ DELETE /api/image-analysis/{request_id} test passed")

    # ==================== COMMON ERROR SCENARIOS ====================

    def test_all_endpoints_invalid_file_type(self):
        """Test all image endpoints with invalid file types."""
        logger.info("Testing all endpoints with invalid file types")

        endpoints = [
            "/query/image",
            "/analyze/image",
            "/analyze/image/openrouter",
            "/api/image-analysis/"
        ]

        # Test with text file
        text_content = b"This is not an image"

        for endpoint in endpoints:
            logger.info(f"Testing {endpoint} with invalid file type")

            response = client.post(
                endpoint,
                files=[("file", ("test.txt", text_content, "text/plain"))]
            )

            # All endpoints should reject non-image files
            assert response.status_code in [400, 422], f"Endpoint {endpoint} should reject non-image files"

        logger.info("✓ All endpoints invalid file type tests passed")

    def test_all_endpoints_no_file(self):
        """Test all image endpoints with no file uploaded."""
        logger.info("Testing all endpoints with no file")

        endpoints = [
            "/query/image",
            "/analyze/image",
            "/analyze/image/openrouter",
            "/api/image-analysis/"
        ]

        for endpoint in endpoints:
            logger.info(f"Testing {endpoint} with no file")

            response = client.post(endpoint)

            # All endpoints should require a file
            assert response.status_code == 422, f"Endpoint {endpoint} should require a file"

        logger.info("✓ All endpoints no file tests passed")

    def test_health_check(self):
        """Test the health check endpoint to ensure the app is running."""
        logger.info("Testing health check endpoint")

        response = client.get("/health")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"

        logger.info("✓ Health check test passed")

    def test_root_endpoint(self):
        """Test the root endpoint."""
        logger.info("Testing root endpoint")

        response = client.get("/")

        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "RAG API" in data["message"]

        logger.info("✓ Root endpoint test passed")


def run_all_tests():
    """Run all image endpoint tests and generate a comprehensive report."""
    logger.info("=" * 80)
    logger.info("STARTING COMPREHENSIVE IMAGE ENDPOINT TESTS")
    logger.info("=" * 80)

    test_instance = TestImageEndpoints()
    test_instance.setup_class()

    # List of all test methods
    test_methods = [
        # /query/image tests
        test_instance.test_query_image_valid_png_success,
        test_instance.test_query_image_valid_jpeg_success,
        test_instance.test_query_image_no_text_extracted,
        test_instance.test_query_image_no_file_uploaded,
        test_instance.test_query_image_invalid_file_type,
        test_instance.test_query_image_empty_file,
        test_instance.test_query_image_oversized_file,
        test_instance.test_query_image_with_retrieval_config,

        # /analyze/image tests
        test_instance.test_analyze_image_success_with_aws_credentials,
        test_instance.test_analyze_image_missing_aws_credentials,
        test_instance.test_analyze_image_bedrock_error,

        # /analyze/image/openrouter tests
        test_instance.test_analyze_image_openrouter_success,
        test_instance.test_analyze_image_openrouter_with_custom_prompt,
        test_instance.test_analyze_image_openrouter_missing_api_key,
        test_instance.test_analyze_image_openrouter_api_error,

        # Image analysis router tests
        test_instance.test_image_analysis_router_upload_success,
        test_instance.test_image_analysis_router_get_result_success,
        test_instance.test_image_analysis_router_get_result_not_found,
        test_instance.test_image_analysis_router_get_image_success,
        test_instance.test_image_analysis_router_delete_success,

        # Common error scenarios
        test_instance.test_all_endpoints_invalid_file_type,
        test_instance.test_all_endpoints_no_file,
        test_instance.test_health_check,
        test_instance.test_root_endpoint,
    ]

    passed_tests = []
    failed_tests = []

    for test_method in test_methods:
        try:
            test_method()
            passed_tests.append(test_method.__name__)
        except Exception as e:
            failed_tests.append((test_method.__name__, str(e)))
            logger.error(f"❌ Test {test_method.__name__} failed: {e}")

    # Cleanup
    test_instance.teardown_class()

    # Generate report
    logger.info("=" * 80)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("=" * 80)
    logger.info(f"Total tests run: {len(test_methods)}")
    logger.info(f"Passed: {len(passed_tests)}")
    logger.info(f"Failed: {len(failed_tests)}")
    logger.info("")

    if passed_tests:
        logger.info("✅ PASSED TESTS:")
        for test_name in passed_tests:
            logger.info(f"  - {test_name}")
        logger.info("")

    if failed_tests:
        logger.info("❌ FAILED TESTS:")
        for test_name, error in failed_tests:
            logger.info(f"  - {test_name}: {error}")
        logger.info("")

    success_rate = (len(passed_tests) / len(test_methods)) * 100
    logger.info(f"Success rate: {success_rate:.1f}%")
    logger.info("=" * 80)

    return {
        "total": len(test_methods),
        "passed": len(passed_tests),
        "failed": len(failed_tests),
        "success_rate": success_rate,
        "passed_tests": passed_tests,
        "failed_tests": failed_tests
    }


if __name__ == "__main__":
    # Run tests when script is executed directly
    results = run_all_tests()

    # Exit with error code if any tests failed
    if results["failed"] > 0:
        exit(1)
    else:
        exit(0)
