"""
OCR (Optical Character Recognition) service for extracting text from images.
"""
import os
import logging
import tempfile
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum
from PIL import Image, ImageEnhance, ImageFilter
import pytesseract
import cv2


# Configure logging
logger = logging.getLogger(__name__)


class OCRLanguage(str, Enum):
    """Supported OCR languages."""
    ENGLISH = "eng"
    GERMAN = "deu"
    FRENCH = "fra"
    SPANISH = "spa"
    CHINESE = "chi_sim"
    JAPANESE = "jpn"
    KOREAN = "kor"
    RUSSIAN = "rus"
    ARABIC = "ara"


class OCRMode(str, Enum):
    """OCR processing modes."""
    DEFAULT = "default"  # Standard OCR
    DOCUMENT = "document"  # Optimized for documents with paragraphs
    SINGLE_LINE = "single_line"  # Optimized for single line text
    SINGLE_WORD = "single_word"  # Optimized for single words
    DIGIT = "digit"  # Optimized for digits
    SPARSE_TEXT = "sparse_text"  # Optimized for sparse text in images
    TABLE = "table"  # Optimized for tables


class OCRPreprocessing(str, Enum):
    """Image preprocessing methods for OCR."""
    NONE = "none"  # No preprocessing
    GRAYSCALE = "grayscale"  # Convert to grayscale
    BINARIZATION = "binarization"  # Convert to binary image
    NOISE_REMOVAL = "noise_removal"  # Remove noise
    DESKEW = "deskew"  # Correct skewed images
    CONTRAST_ENHANCEMENT = "contrast_enhancement"  # Enhance contrast
    ALL = "all"  # Apply all preprocessing methods


class OCRService:
    """
    Service for extracting text from images using OCR.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the OCR service.
        
        Args:
            config: Configuration options for OCR
        """
        self.config = config or {}
        
        # Set default configuration
        self.default_language = self.config.get("language", OCRLanguage.ENGLISH)
        self.default_mode = self.config.get("mode", OCRMode.DEFAULT)
        self.default_preprocessing = self.config.get("preprocessing", OCRPreprocessing.NONE)
        self.default_confidence_threshold = self.config.get("confidence_threshold", 0)
        
        # Check if tesseract is installed and configured
        try:
            pytesseract.get_tesseract_version()
        except Exception as e:
            logger.warning(f"Tesseract OCR not properly installed or configured: {str(e)}")
            logger.warning("OCR functionality may be limited or unavailable")
    
    def extract_text(
        self, 
        image_path: str, 
        language: Optional[OCRLanguage] = None,
        mode: Optional[OCRMode] = None,
        preprocessing: Optional[OCRPreprocessing] = None,
        confidence_threshold: Optional[int] = None
    ) -> str:
        """
        Extract text from an image.
        
        Args:
            image_path: Path to the image
            language: OCR language
            mode: OCR processing mode
            preprocessing: Image preprocessing method
            confidence_threshold: Minimum confidence threshold for extracted text
            
        Returns:
            str: Extracted text
        """
        try:
            # Skip OCR for non-image files or non-existent files
            if not os.path.exists(image_path) or image_path.endswith('.svg'):
                logger.warning(f"Skipping OCR for non-image or non-existent file: {image_path}")
                return ""
            
            # Set parameters
            language = language or self.default_language
            mode = mode or self.default_mode
            preprocessing = preprocessing or self.default_preprocessing
            confidence_threshold = confidence_threshold or self.default_confidence_threshold
            
            # Load the image
            img = self._load_image(image_path)
            
            # Preprocess the image
            img = self._preprocess_image(img, preprocessing)
            
            # Configure OCR options
            config = self._get_tesseract_config(mode, confidence_threshold)
            
            # Extract text
            text = pytesseract.image_to_string(img, lang=language, config=config)
            
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from image: {str(e)}")
            return ""
    
    def extract_text_with_layout(
        self, 
        image_path: str,
        language: Optional[OCRLanguage] = None,
        preprocessing: Optional[OCRPreprocessing] = None
    ) -> Dict[str, Any]:
        """
        Extract text with layout information.
        
        Args:
            image_path: Path to the image
            language: OCR language
            preprocessing: Image preprocessing method
            
        Returns:
            Dict[str, Any]: Extracted text with layout information
        """
        try:
            # Skip OCR for non-image files or non-existent files
            if not os.path.exists(image_path) or image_path.endswith('.svg'):
                logger.warning(f"Skipping OCR for non-image or non-existent file: {image_path}")
                return {"text": "", "blocks": []}
            
            # Set parameters
            language = language or self.default_language
            preprocessing = preprocessing or self.default_preprocessing
            
            # Load the image
            img = self._load_image(image_path)
            
            # Preprocess the image
            img = self._preprocess_image(img, preprocessing)
            
            # Extract text with layout information
            data = pytesseract.image_to_data(img, lang=language, output_type=pytesseract.Output.DICT)
            
            # Process the extracted data
            blocks = []
            current_block = {"text": "", "lines": []}
            current_line = {"text": "", "words": []}
            current_block_id = -1
            current_line_id = -1
            
            for i in range(len(data["text"])):
                # Skip empty text
                if not data["text"][i].strip():
                    continue
                
                # Check if this is a new block
                if data["block_num"][i] != current_block_id:
                    # Save the previous block if it has text
                    if current_block["text"]:
                        # Save the current line if it has text
                        if current_line["text"]:
                            current_block["lines"].append(current_line)
                        blocks.append(current_block)
                    
                    # Start a new block
                    current_block = {"text": "", "lines": []}
                    current_line = {"text": "", "words": []}
                    current_block_id = data["block_num"][i]
                    current_line_id = data["line_num"][i]
                
                # Check if this is a new line
                elif data["line_num"][i] != current_line_id:
                    # Save the current line if it has text
                    if current_line["text"]:
                        current_block["lines"].append(current_line)
                    
                    # Start a new line
                    current_line = {"text": "", "words": []}
                    current_line_id = data["line_num"][i]
                
                # Add the word to the current line
                word = data["text"][i]
                confidence = data["conf"][i]
                left = data["left"][i]
                top = data["top"][i]
                width = data["width"][i]
                height = data["height"][i]
                
                current_line["words"].append({
                    "text": word,
                    "confidence": confidence,
                    "bbox": [left, top, width, height]
                })
                
                # Update line text
                if current_line["text"]:
                    current_line["text"] += " " + word
                else:
                    current_line["text"] = word
                
                # Update block text
                if current_block["text"]:
                    if data["line_num"][i] != current_line_id:
                        current_block["text"] += "\n" + word
                    else:
                        current_block["text"] += " " + word
                else:
                    current_block["text"] = word
            
            # Save the last block and line if they have text
            if current_line["text"]:
                current_block["lines"].append(current_line)
            if current_block["text"]:
                blocks.append(current_block)
            
            # Combine all text
            all_text = "\n\n".join(block["text"] for block in blocks)
            
            return {
                "text": all_text,
                "blocks": blocks
            }
        except Exception as e:
            logger.error(f"Error extracting text with layout from image: {str(e)}")
            return {"text": "", "blocks": []}
    
    def extract_tables(self, image_path: str) -> List[Dict[str, Any]]:
        """
        Extract tables from an image.
        
        Args:
            image_path: Path to the image
            
        Returns:
            List[Dict[str, Any]]: List of extracted tables
        """
        try:
            # Skip OCR for non-image files or non-existent files
            if not os.path.exists(image_path) or image_path.endswith('.svg'):
                logger.warning(f"Skipping table extraction for non-image or non-existent file: {image_path}")
                return []
            
            # Load the image
            img = cv2.imread(image_path)
            if img is None:
                logger.warning(f"Failed to load image for table extraction: {image_path}")
                return []
            
            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Apply threshold to get binary image
            _, thresh = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY_INV)
            
            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
            
            # Find table-like structures (rectangles)
            tables = []
            for contour in contours:
                # Approximate the contour to a polygon
                epsilon = 0.1 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # Check if the polygon has 4 sides (rectangle)
                if len(approx) == 4:
                    # Get bounding rectangle
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Filter out small rectangles
                    if w > 100 and h > 100:
                        # Extract the table region
                        table_img = img[y:y+h, x:x+w]
                        
                        # Save the table image to a temporary file
                        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as temp_file:
                            cv2.imwrite(temp_file.name, table_img)
                            
                            # Extract text from the table image
                            table_text = self.extract_text(
                                temp_file.name,
                                mode=OCRMode.TABLE
                            )
                            
                            # Delete the temporary file
                            os.unlink(temp_file.name)
                        
                        # Add the table to the list
                        tables.append({
                            "bbox": [x, y, w, h],
                            "text": table_text
                        })
            
            return tables
        except Exception as e:
            logger.error(f"Error extracting tables from image: {str(e)}")
            return []
    
    def _load_image(self, image_path: str) -> Image.Image:
        """
        Load an image from a file.
        
        Args:
            image_path: Path to the image
            
        Returns:
            Image.Image: Loaded image
        """
        return Image.open(image_path)
    
    def _preprocess_image(self, img: Image.Image, preprocessing: OCRPreprocessing) -> Image.Image:
        """
        Preprocess an image for OCR.
        
        Args:
            img: Image to preprocess
            preprocessing: Preprocessing method
            
        Returns:
            Image.Image: Preprocessed image
        """
        if preprocessing == OCRPreprocessing.NONE:
            return img
        
        # Convert to OpenCV format for advanced preprocessing
        img_np = np.array(img)
        
        if preprocessing == OCRPreprocessing.GRAYSCALE or preprocessing == OCRPreprocessing.ALL:
            # Convert to grayscale
            if len(img_np.shape) == 3:  # Color image
                img_np = cv2.cvtColor(img_np, cv2.COLOR_RGB2GRAY)
        
        if preprocessing == OCRPreprocessing.BINARIZATION or preprocessing == OCRPreprocessing.ALL:
            # Apply adaptive thresholding
            if len(img_np.shape) == 2:  # Grayscale image
                img_np = cv2.adaptiveThreshold(
                    img_np, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
                )
            else:
                # Convert to grayscale first
                gray = cv2.cvtColor(img_np, cv2.COLOR_RGB2GRAY)
                img_np = cv2.adaptiveThreshold(
                    gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
                )
        
        if preprocessing == OCRPreprocessing.NOISE_REMOVAL or preprocessing == OCRPreprocessing.ALL:
            # Apply median blur to remove noise
            img_np = cv2.medianBlur(img_np, 3)
        
        if preprocessing == OCRPreprocessing.DESKEW or preprocessing == OCRPreprocessing.ALL:
            # Deskew the image
            if len(img_np.shape) == 2:  # Grayscale image
                # Find all non-zero points
                coords = np.column_stack(np.where(img_np > 0))
                if len(coords) > 0:
                    # Find the minimum area rectangle
                    rect = cv2.minAreaRect(coords)
                    angle = rect[2]
                    
                    # Determine the angle to rotate
                    if angle < -45:
                        angle = -(90 + angle)
                    else:
                        angle = -angle
                    
                    # Rotate the image
                    (h, w) = img_np.shape[:2]
                    center = (w // 2, h // 2)
                    M = cv2.getRotationMatrix2D(center, angle, 1.0)
                    img_np = cv2.warpAffine(
                        img_np, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE
                    )
        
        if preprocessing == OCRPreprocessing.CONTRAST_ENHANCEMENT or preprocessing == OCRPreprocessing.ALL:
            # Convert back to PIL for contrast enhancement
            img = Image.fromarray(img_np)
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(2.0)
            
            return img
        
        # Convert back to PIL
        return Image.fromarray(img_np)
    
    def _get_tesseract_config(self, mode: OCRMode, confidence_threshold: int) -> str:
        """
        Get Tesseract configuration string based on mode and confidence threshold.
        
        Args:
            mode: OCR processing mode
            confidence_threshold: Minimum confidence threshold
            
        Returns:
            str: Tesseract configuration string
        """
        config = []
        
        # Add confidence threshold
        if confidence_threshold > 0:
            config.append("--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ.,;:!?()[]{}'\"-+*/=<>@#$%^&_\\| -c tessedit_char_blacklist=~ -c tessedit_char_blacklist=`")
        
        # Add mode-specific configuration
        if mode == OCRMode.DOCUMENT:
            config.append("--psm 6")  # Assume a single uniform block of text
        elif mode == OCRMode.SINGLE_LINE:
            config.append("--psm 7")  # Treat the image as a single line of text
        elif mode == OCRMode.SINGLE_WORD:
            config.append("--psm 8")  # Treat the image as a single word
        elif mode == OCRMode.DIGIT:
            config.append("--psm 10")  # Treat the image as a single character
            config.append("-c tessedit_char_whitelist=0123456789")
        elif mode == OCRMode.SPARSE_TEXT:
            config.append("--psm 11")  # Sparse text. Find as much text as possible in no particular order
        elif mode == OCRMode.TABLE:
            config.append("--psm 6")  # Assume a single uniform block of text
        
        return " ".join(config)