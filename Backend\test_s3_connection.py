#!/usr/bin/env python
"""
Test script for S3 connection in the RAG application.
This script verifies that the S3 connection is properly configured and works as expected.
"""

import os
import logging
import json
import boto3
from dotenv import load_dotenv
from botocore.exceptions import NoCredentialsError, ClientError
from aws_utils import create_aws_session, AWSClient

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("test_s3_connection.log")
    ]
)

logger = logging.getLogger(__name__)

def test_aws_credentials():
    """Test AWS credentials."""
    try:
        logger.info("Testing AWS credentials")
        
        # Get AWS credentials from environment variables
        aws_access_key = os.getenv("AWS_ACCESS_KEY_ID")
        aws_secret_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        aws_region = os.getenv("AWS_REGION")
        
        if not aws_access_key or not aws_secret_key or not aws_region:
            logger.error("AWS credentials not found in environment variables")
            return False
        
        # Create AWS session
        session = create_aws_session()
        
        # Test the session by getting caller identity
        sts_client = session.client("sts")
        identity = sts_client.get_caller_identity()
        
        logger.info(f"Successfully authenticated with AWS. Account ID: {identity['Account']}")
        return True
    except NoCredentialsError:
        logger.error("AWS credentials not found or invalid")
        return False
    except Exception as e:
        logger.error(f"Error testing AWS credentials: {e}")
        return False

def test_s3_bucket_access():
    """Test access to S3 bucket."""
    try:
        logger.info("Testing S3 bucket access")
        
        # Get S3 bucket name from environment variables
        bucket_name = os.getenv("S3_BUCKET_NAME")
        
        if not bucket_name:
            logger.error("S3_BUCKET_NAME not found in environment variables")
            return False
        
        # Create AWS session
        session = create_aws_session()
        
        # Create S3 client
        s3_client = session.client("s3")
        
        # Test bucket access by listing objects
        try:
            response = s3_client.list_objects_v2(Bucket=bucket_name, MaxKeys=1)
            has_objects = "Contents" in response
            
            logger.info(f"Successfully accessed S3 bucket '{bucket_name}'. Contains objects: {has_objects}")
            return True
        except ClientError as e:
            if e.response["Error"]["Code"] == "NoSuchBucket":
                logger.error(f"S3 bucket '{bucket_name}' does not exist")
            elif e.response["Error"]["Code"] == "AccessDenied":
                logger.error(f"Access denied to S3 bucket '{bucket_name}'")
            else:
                logger.error(f"Error accessing S3 bucket '{bucket_name}': {e}")
            return False
    except Exception as e:
        logger.error(f"Error testing S3 bucket access: {e}")
        return False

def test_aws_client():
    """Test AWSClient class."""
    try:
        logger.info("Testing AWSClient class")
        
        # Create AWSClient instance
        client = AWSClient()
        
        # Test connection
        result = client.test_connection()
        
        if result["status"] == "success":
            logger.info(f"Successfully tested AWSClient connection to bucket '{result['bucket']}'")
            return True
        else:
            logger.error(f"Failed to test AWSClient connection: {result}")
            return False
    except Exception as e:
        logger.error(f"Error testing AWSClient: {e}")
        return False

def test_s3_file_operations():
    """Test S3 file operations."""
    try:
        logger.info("Testing S3 file operations")
        
        # Get S3 bucket name from environment variables
        bucket_name = os.getenv("S3_BUCKET_NAME")
        
        if not bucket_name:
            logger.error("S3_BUCKET_NAME not found in environment variables")
            return False
        
        # Create AWS session
        session = create_aws_session()
        
        # Create S3 client
        s3_client = session.client("s3")
        
        # Create a test file
        test_file_key = "test/test_file.txt"
        test_content = "This is a test file for S3 operations"
        
        # Upload the test file
        s3_client.put_object(
            Bucket=bucket_name,
            Key=test_file_key,
            Body=test_content
        )
        logger.info(f"Successfully uploaded test file to S3: {test_file_key}")
        
        # Download the test file
        response = s3_client.get_object(
            Bucket=bucket_name,
            Key=test_file_key
        )
        downloaded_content = response["Body"].read().decode("utf-8")
        
        if downloaded_content == test_content:
            logger.info("Successfully downloaded test file from S3")
        else:
            logger.error("Downloaded content does not match uploaded content")
            return False
        
        # Delete the test file
        s3_client.delete_object(
            Bucket=bucket_name,
            Key=test_file_key
        )
        logger.info(f"Successfully deleted test file from S3: {test_file_key}")
        
        return True
    except Exception as e:
        logger.error(f"Error testing S3 file operations: {e}")
        return False

def main():
    """Main function to run the S3 connection tests."""
    logger.info("Starting S3 connection tests")
    
    # Test AWS credentials
    credentials_result = test_aws_credentials()
    
    if not credentials_result:
        logger.error("AWS credentials test failed. Skipping remaining tests.")
        return 1
    
    # Test S3 bucket access
    bucket_result = test_s3_bucket_access()
    
    # Test AWSClient
    client_result = test_aws_client()
    
    # Test S3 file operations
    file_ops_result = test_s3_file_operations()
    
    # Summarize results
    results = {
        "credentials_test": credentials_result,
        "bucket_test": bucket_result,
        "client_test": client_result,
        "file_ops_test": file_ops_result
    }
    
    logger.info(f"Test results: {json.dumps(results)}")
    
    # Overall success if all tests passed
    success = all(results.values())
    logger.info(f"S3 connection tests {'succeeded' if success else 'failed'}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())