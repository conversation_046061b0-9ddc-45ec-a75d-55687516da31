#!/bin/bash
# Complete RAG Application Deployment Script for EC2 with systemd
# Supports Ubuntu 20.04+ and Amazon Linux 2

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_DIR="/opt/chainlit_rag"
REPO_URL="${REPO_URL:-http://git.monitoring.bfsgodirect.com/genai/observability-bot.git}"
BRANCH="${BRANCH:-main}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; exit 1; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   error "This script must be run as root (use sudo)"
fi

# Detect OS
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$ID
        VER=$VERSION_ID
    else
        error "Cannot detect OS version"
    fi
    
    log "Detected OS: $OS $VER"
}

# Install system dependencies
install_system_deps() {
    log "Installing system dependencies..."
    
    case $OS in
        ubuntu|debian)
            apt-get update
            apt-get install -y \
                python3 python3-pip python3-venv python3-dev \
                git curl wget unzip build-essential \
                libmagic-dev poppler-utils tesseract-ocr libreoffice pandoc \
                libssl-dev libffi-dev \
                nginx ufw logrotate \
                htop iotop nethogs
            ;;
        amzn|centos|rhel)
            yum update -y
            yum groupinstall -y "Development Tools"
            yum install -y \
                python3 python3-pip python3-devel \
                git curl wget unzip \
                file-devel poppler-utils tesseract libreoffice \
                openssl-devel libffi-devel \
                nginx firewalld logrotate \
                htop iotop
            ;;
        *)
            error "Unsupported OS: $OS"
            ;;
    esac
    
    log "System dependencies installed"
}

# Install Docker for Qdrant
install_docker() {
    log "Installing Docker..."
    
    if ! command -v docker &> /dev/null; then
        curl -fsSL https://get.docker.com -o get-docker.sh
        sh get-docker.sh
        rm get-docker.sh
        
        # Add users to docker group
        usermod -aG docker ubuntu 2>/dev/null || true
        usermod -aG docker ec2-user 2>/dev/null || true
        
        systemctl enable docker
        systemctl start docker
        
        log "Docker installed and started"
    else
        log "Docker already installed"
    fi
}

# Setup Qdrant
setup_qdrant() {
    log "Setting up Qdrant vector database..."
    
    # Create Qdrant directories
    mkdir -p /opt/qdrant/{config,storage,logs}
    
    # Create Qdrant configuration
    cat > /opt/qdrant/config/production.yaml << 'EOF'
log_level: INFO
storage:
  storage_path: /opt/qdrant/storage
service:
  http_port: 6333
  grpc_port: 6334
  host: 127.0.0.1
  max_request_size_mb: 32
  max_workers: 0
  enable_cors: false
cluster:
  enabled: false
telemetry_disabled: true
EOF
    
    # Download and install Qdrant binary
    QDRANT_VERSION="v1.7.4"
    wget -O /tmp/qdrant.tar.gz "https://github.com/qdrant/qdrant/releases/download/${QDRANT_VERSION}/qdrant-x86_64-unknown-linux-gnu.tar.gz"
    tar -xzf /tmp/qdrant.tar.gz -C /tmp/
    mv /tmp/qdrant /usr/local/bin/
    chmod +x /usr/local/bin/qdrant
    rm /tmp/qdrant.tar.gz
    
    log "Qdrant installed"
}

# Clone and setup application
setup_application() {
    log "Setting up RAG application..."
    
    # Clone repository
    if [[ -d "$APP_DIR" ]]; then
        warn "Application directory exists, backing up..."
        mv "$APP_DIR" "${APP_DIR}.backup.$(date +%s)"
    fi
    
    git clone -b "$BRANCH" "$REPO_URL" "$APP_DIR"
    cd "$APP_DIR"
    
    # Copy production files
    if [[ -f "$SCRIPT_DIR/../production_main.py" ]]; then
        cp "$SCRIPT_DIR/../production_main.py" "$APP_DIR/"
        log "Copied production_main.py"
    fi
    
    if [[ -f "$SCRIPT_DIR/../production_chainlit_app.py" ]]; then
        cp "$SCRIPT_DIR/../production_chainlit_app.py" "$APP_DIR/"
        log "Copied production_chainlit_app.py"
    fi
    
    # Create Python virtual environment
    python3 -m venv venv
    source venv/bin/activate
    
    # Upgrade pip and install dependencies
    pip install --upgrade pip
    pip install -r requirements.txt
    
    # Install additional production dependencies
    pip install gunicorn supervisor
    
    log "Application setup completed"
}

# Install systemd services
install_services() {
    log "Installing systemd services..."
    
    # Copy service files
    for service in qdrant.service rag-api.service rag-frontend.service; do
        if [[ -f "$SCRIPT_DIR/../systemd-services/$service" ]]; then
            cp "$SCRIPT_DIR/../systemd-services/$service" "/etc/systemd/system/"
            log "Installed $service"
        else
            warn "Service file $service not found"
        fi
    done
    
    # Reload systemd
    systemctl daemon-reload
    
    # Enable services
    systemctl enable qdrant.service
    systemctl enable rag-api.service
    systemctl enable rag-frontend.service
    
    log "Systemd services installed and enabled"
}

# Configure environment
configure_environment() {
    log "Configuring application environment..."
    
    # Create environment file if it doesn't exist
    if [[ ! -f "$APP_DIR/.env" ]]; then
        if [[ -f "$APP_DIR/.env.template" ]]; then
            cp "$APP_DIR/.env.template" "$APP_DIR/.env"
            warn "Created .env from template - please update with your actual values"
        else
            cat > "$APP_DIR/.env" << 'EOF'
# AWS Configuration - UPDATE THESE VALUES
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key-here
AWS_SECRET_ACCESS_KEY=your-secret-key-here
BEDROCK_EMBEDDING_MODEL_ID=amazon.titan-embed-text-v1
BEDROCK_LLM_PROFILE_ARN=your-bedrock-profile-arn

# S3 Configuration - UPDATE THIS VALUE
S3_BUCKET_NAME=your-s3-bucket-name

# Qdrant Configuration
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=

# Application Configuration
RAG_API_HOST=0.0.0.0
RAG_API_PORT=8888
RAG_API_WORKERS=2
ALLOWED_ORIGINS=http://localhost:8000

# Chainlit Configuration
API_BASE_URL=http://localhost:8888
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=8000
EOF
            warn "Created default .env file - please update with your actual values"
        fi
    fi
    
    log "Environment configuration completed"
}

# Setup nginx reverse proxy
setup_nginx() {
    log "Setting up Nginx reverse proxy..."
    
    # Backup default nginx config
    if [[ -f /etc/nginx/sites-available/default ]]; then
        cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup
    fi
    
    # Create RAG application nginx config
    cat > /etc/nginx/sites-available/rag-app << 'EOF'
server {
    listen 80;
    server_name _;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Frontend (Chainlit)
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # API endpoints
    location /api/ {
        rewrite ^/api/(.*) /$1 break;
        proxy_pass http://127.0.0.1:8888;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://127.0.0.1:8888/health;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        access_log off;
    }
}
EOF
    
    # Enable the site
    ln -sf /etc/nginx/sites-available/rag-app /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    
    # Test nginx configuration
    nginx -t
    
    # Enable and start nginx
    systemctl enable nginx
    systemctl restart nginx
    
    log "Nginx configured and started"
}

# Start services
start_services() {
    log "Starting RAG services..."
    
    # Start services in order
    systemctl start qdrant
    sleep 10
    
    systemctl start rag-api
    sleep 15
    
    systemctl start rag-frontend
    sleep 10
    
    # Check service status
    for service in qdrant rag-api rag-frontend; do
        if systemctl is-active --quiet $service; then
            log "✓ $service is running"
        else
            error "✗ $service failed to start"
        fi
    done
    
    log "All services started successfully"
}

# Verify deployment
verify_deployment() {
    log "Verifying deployment..."
    
    # Check service health
    sleep 20
    
    # Test Qdrant
    if curl -f -s http://localhost:6333/health > /dev/null; then
        log "✓ Qdrant is healthy"
    else
        warn "✗ Qdrant health check failed"
    fi
    
    # Test API
    if curl -f -s http://localhost:8888/health > /dev/null; then
        log "✓ RAG API is healthy"
    else
        warn "✗ RAG API health check failed"
    fi
    
    # Test Frontend
    if curl -f -s http://localhost:8000 > /dev/null; then
        log "✓ RAG Frontend is accessible"
    else
        warn "✗ RAG Frontend health check failed"
    fi
    
    # Test Nginx
    if curl -f -s http://localhost/health > /dev/null; then
        log "✓ Nginx proxy is working"
    else
        warn "✗ Nginx proxy health check failed"
    fi
}

# Main deployment function
main() {
    log "Starting RAG Application Deployment for systemd"
    log "=============================================="
    
    detect_os
    install_system_deps
    install_docker
    
    # Run security setup
    if [[ -f "$SCRIPT_DIR/setup-users-security.sh" ]]; then
        log "Running security setup..."
        bash "$SCRIPT_DIR/setup-users-security.sh"
    else
        warn "Security setup script not found, skipping..."
    fi
    
    setup_qdrant
    setup_application
    configure_environment
    install_services
    setup_nginx
    start_services
    verify_deployment
    
    log ""
    log "=============================================="
    log "RAG Application Deployment Completed!"
    log "=============================================="
    log ""
    log "Services Status:"
    systemctl status qdrant rag-api rag-frontend --no-pager -l
    log ""
    log "Access your application:"
    log "- Frontend: http://$(curl -s ifconfig.me)/"
    log "- API: http://$(curl -s ifconfig.me)/api/"
    log "- Health: http://$(curl -s ifconfig.me)/health"
    log ""
    log "Important next steps:"
    log "1. Update /opt/rag-app/.env with your AWS credentials"
    log "2. Configure your S3 bucket and Bedrock settings"
    log "3. Run document ingestion: curl -X POST http://localhost/api/ingest"
    log "4. Monitor logs: journalctl -u rag-api -f"
    log ""
    log "Service management commands:"
    log "- Start: systemctl start qdrant rag-api rag-frontend"
    log "- Stop: systemctl stop rag-frontend rag-api qdrant"
    log "- Restart: systemctl restart rag-api"
    log "- Status: systemctl status rag-api"
    log "- Logs: journalctl -u rag-api -f"
}

# Handle command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "update")
        log "Updating RAG application..."
        cd "$APP_DIR"
        git pull origin "$BRANCH"
        source venv/bin/activate
        pip install -r requirements.txt
        systemctl restart rag-api rag-frontend
        log "Application updated and restarted"
        ;;
    "status")
        log "RAG Application Status:"
        systemctl status qdrant rag-api rag-frontend --no-pager
        ;;
    "logs")
        service="${2:-rag-api}"
        journalctl -u "$service" -f
        ;;
    "health")
        bash "$APP_DIR/health-check.sh"
        ;;
    *)
        echo "Usage: $0 {deploy|update|status|logs|health}"
        echo "  deploy - Full deployment"
        echo "  update - Update application code"
        echo "  status - Show service status"
        echo "  logs [service] - Show service logs"
        echo "  health - Run health check"
        exit 1
        ;;
esac
