# Design Document

## Overview

This document outlines the design for testing the RAG application's API endpoints. The testing framework will verify the functionality, error handling, and performance of the four main endpoints: `/query/advanced`, `/query/image`, `/analyze/image`, and `/analyze/image/openrouter`. The tests will be designed to be comprehensive, covering both positive and negative test cases, and will include unit tests, integration tests, and manual tests.

## Architecture

The testing framework will consist of the following components:

1. **Test Utilities**: Helper functions for making API requests, validating responses, and setting up test data.
2. **Unit Tests**: Tests for individual components of the endpoints.
3. **Integration Tests**: Tests that verify the end-to-end functionality of the endpoints.
4. **Test Data**: Sample text queries, images with text, images without text, and invalid files.
5. **Test Reports**: Logs and reports of test results.

## Components and Interfaces

### Test Utilities

```python
class EndpointTester:
    def __init__(self, base_url="http://localhost:8888"):
        self.base_url = base_url
        
    def test_query_advanced(self, question, retrieval_config=None):
        """Test the /query/advanced endpoint"""
        url = f"{self.base_url}/query/advanced"
        payload = {
            "question": question,
            "retrieval_config": retrieval_config or {}
        }
        response = requests.post(url, json=payload)
        return response
        
    def test_query_image(self, image_path, retrieval_config=None):
        """Test the /query/image endpoint"""
        url = f"{self.base_url}/query/image"
        files = {"file": open(image_path, "rb")}
        data = {}
        if retrieval_config:
            data["retrieval_config"] = json.dumps(retrieval_config)
        response = requests.post(url, files=files, data=data)
        return response
        
    def test_analyze_image(self, image_path):
        """Test the /analyze/image endpoint"""
        url = f"{self.base_url}/analyze/image"
        files = {"file": open(image_path, "rb")}
        response = requests.post(url, files=files)
        return response
        
    def test_analyze_image_openrouter(self, image_path, prompt=None):
        """Test the /analyze/image/openrouter endpoint"""
        url = f"{self.base_url}/analyze/image/openrouter"
        files = {"file": open(image_path, "rb")}
        data = {}
        if prompt:
            data["prompt"] = prompt
        response = requests.post(url, files=files, data=data)
        return response
```

### Test Data

1. **Text Queries**:
   - Simple queries (e.g., "What is AWS EC2?")
   - Complex queries (e.g., "Compare AWS Lambda and EC2 for serverless applications")
   - Invalid queries (e.g., empty strings, very long strings)

2. **Images**:
   - Images with clear text (e.g., screenshots of documentation)
   - Images with diagrams (e.g., architecture diagrams)
   - Images without text (e.g., photographs)
   - Invalid images (e.g., corrupt files, non-image files)

## Data Models

### Test Case

```python
class TestCase:
    def __init__(self, name, endpoint, input_data, expected_status, expected_content=None):
        self.name = name
        self.endpoint = endpoint
        self.input_data = input_data
        self.expected_status = expected_status
        self.expected_content = expected_content
        
    def run(self, tester):
        """Run the test case and return the result"""
        # Implementation depends on the endpoint
        pass
        
    def validate(self, response):
        """Validate the response against expected values"""
        assert response.status_code == self.expected_status
        if self.expected_content:
            for key, value in self.expected_content.items():
                assert key in response.json()
                # Validate based on the type of value
                if isinstance(value, str):
                    assert value in response.json()[key]
                elif isinstance(value, bool):
                    assert bool(response.json()[key]) == value
                # Add more validation types as needed
```

### Test Report

```python
class TestReport:
    def __init__(self):
        self.results = []
        
    def add_result(self, test_case, response, passed, error=None):
        self.results.append({
            "test_case": test_case.name,
            "endpoint": test_case.endpoint,
            "status_code": response.status_code if response else None,
            "passed": passed,
            "error": str(error) if error else None,
            "response": response.json() if response and hasattr(response, 'json') else None
        })
        
    def generate_report(self):
        """Generate a report of test results"""
        total = len(self.results)
        passed = sum(1 for result in self.results if result["passed"])
        failed = total - passed
        
        report = {
            "total": total,
            "passed": passed,
            "failed": failed,
            "pass_rate": passed / total if total > 0 else 0,
            "results": self.results
        }
        
        return report
```

## Error Handling

The testing framework will handle the following error scenarios:

1. **Server Not Running**: If the server is not running, the tests will fail with a connection error.
2. **Invalid Responses**: If the server returns an unexpected response, the tests will fail with a validation error.
3. **Missing Dependencies**: If required dependencies (e.g., test images) are missing, the tests will fail with an appropriate error message.

## Testing Strategy

### Unit Tests

1. **Query Validation**: Test the validation of query inputs.
2. **Image Processing**: Test the extraction of text from images.
3. **Response Parsing**: Test the parsing of responses from AWS Bedrock and OpenRouter.

### Integration Tests

1. **End-to-End Tests**: Test the complete flow from request to response for each endpoint.
2. **Error Handling Tests**: Test how the endpoints handle various error scenarios.
3. **Performance Tests**: Test the response time and resource usage of the endpoints.

### Manual Tests

1. **UI Integration**: Test the integration with frontend components.
2. **Real-world Scenarios**: Test with real-world queries and images.

## Decision

For this testing framework, we will use Python's `unittest` framework for unit tests and `pytest` for integration tests. We will also use `requests` for making API calls and `PIL` for image manipulation. The tests will be designed to be run automatically as part of a CI/CD pipeline, but can also be run manually for development and debugging purposes.

The testing framework will be implemented in a separate directory to keep it isolated from the main application code. It will include a README with instructions on how to run the tests and interpret the results.