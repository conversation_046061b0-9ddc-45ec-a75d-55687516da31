# Image Analysis Feature

This module provides AI-powered analysis of architecture diagrams and error screenshots. The system uses computer vision and natural language processing techniques to extract information from images and generate meaningful explanations.

## Features

- Upload and analyze architecture diagrams
- Upload and analyze error screenshots
- Extract text from images using OCR
- Identify components and relationships in architecture diagrams
- Identify errors and suggest solutions for error screenshots
- Secure file handling and validation

## Directory Structure

```
Backend/image_analysis/
├── __init__.py          # Package initialization
├── models.py            # Data models for requests and results
├── validators.py        # Image validation utilities
├── storage.py           # Temporary storage services
├── api.py               # FastAPI endpoints
└── README.md            # This documentation file
```

## API Endpoints

### POST /api/image-analysis/

Upload an image for analysis.

**Request:**
- Multipart form data with image file

**Response:**
```json
{
  "id": "string (UUID)",
  "status": "pending",
  "message": "Image uploaded successfully and queued for analysis"
}
```

### GET /api/image-analysis/{request_id}

Get the analysis result for a request.

**Response (in progress):**
```json
{
  "status": "processing",
  "message": "Analysis is processing"
}
```

**Response (completed):**
```json
{
  "status": "completed",
  "result": {
    "id": "string (UUID)",
    "request_id": "string (UUID)",
    "timestamp": "datetime",
    "analysis_type": "architecture|error",
    "confidence": 0.95,
    "image_url": "string (temporary URL)",
    "text_content": "string (extracted text)",
    "analysis": {
      "summary": "string",
      "details": [
        {
          "type": "string",
          "content": "string",
          "confidence": 0.9,
          "bounding_box": {
            "x": 100,
            "y": 100,
            "width": 200,
            "height": 50
          }
        }
      ],
      "recommendations": [
        {
          "type": "string",
          "content": "string",
          "priority": 1
        }
      ]
    }
  }
}
```

### DELETE /api/image-analysis/{request_id}

Delete an analysis request and its associated data.

**Response:**
```json
{
  "message": "Analysis data deleted successfully"
}
```

## Frontend Integration

The feature is integrated into the frontend through the `/analyze` command in the chat interface. Users can upload images and choose between AWS Bedrock (Claude) or OpenRouter (Mistral) for analysis.

## Dependencies

- FastAPI for API endpoints
- Pillow for image processing
- pytesseract for OCR
- OpenCV for image analysis
- Transformers for AI-powered analysis