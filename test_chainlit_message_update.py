#!/usr/bin/env python3
"""
Test script to verify Chainlit message update mechanism.
"""

import chainlit as cl
import asyncio

@cl.on_message
async def test_message_update(message: cl.Message):
    """Test message update functionality."""
    
    if message.content.strip().lower() == "test":
        # Create a loading message
        loading_msg = await cl.Message(
            content="🔍 Testing message update...",
            author="Assistant"
        ).send()
        
        # Simulate getting analysis result
        analysis = "This is a test analysis that should be displayed correctly."
        model_id = "test-model"
        
        # Format the content exactly like the frontend
        result_content = f"""# 🔍 Image Analysis Results

## 🤖 AI Analysis

{analysis}

---
*Analysis performed by: {model_id}*"""
        
        # Update the message
        loading_msg.content = result_content
        await loading_msg.update()
        
        # Send a confirmation
        await cl.Message(
            content="✅ Message update test completed!",
            author="Assistant"
        ).send()

if __name__ == "__main__":
    print("Test Chainlit app for message updates")
    print("Send 'test' to test the message update functionality")
