#!/usr/bin/env python3
"""
Simple Chainlit test to see if the issue is with content display.
"""

import chainlit as cl
import requests
from PIL import Image, ImageDraw
from io import BytesIO

def create_test_image():
    """Create a test image."""
    img = Image.new('RGB', (200, 150), color='white')
    draw = ImageDraw.Draw(img)
    draw.text((20, 20), "AWS Test", fill='black')
    draw.rectangle([20, 50, 80, 90], outline='blue', width=2)
    draw.text((25, 65), "EC2", fill='blue')
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

@cl.on_message
async def main(message: cl.Message):
    """Handle incoming messages."""
    
    if message.content.lower() == "test":
        # Get real analysis from backend
        image_bytes = create_test_image()
        files = {'file': ('test.png', image_bytes, 'image/png')}
        
        try:
            response = requests.post(
                'http://localhost:8888/analyze/image/gemini',
                files=files,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                analysis = result.get('analysis', 'No analysis')
                model_id = result.get('model_id', 'unknown')
                
                # Test different ways of displaying the content
                await cl.Message(content=f"Analysis length: {len(analysis)}").send()
                await cl.Message(content=f"Model: {model_id}").send()
                
                # Try simple text first
                await cl.Message(content=analysis[:500] + "...").send()
                
                # Try with basic formatting
                formatted_content = f"""Analysis Results:

{analysis}

Model: {model_id}"""
                
                await cl.Message(content=formatted_content).send()
                
            else:
                await cl.Message(content=f"Error: {response.status_code}").send()
                
        except Exception as e:
            await cl.Message(content=f"Exception: {str(e)}").send()
            
    else:
        await cl.Message(content="Type 'test' to run the analysis test.").send()

if __name__ == "__main__":
    cl.run()
