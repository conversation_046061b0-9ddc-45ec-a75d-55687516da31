#!/usr/bin/env python3
"""
Simple Chainlit test to see if the issue is with content display.
"""

import chainlit as cl

@cl.on_message
async def main(message: cl.Message):
    """Handle incoming messages."""
    
    if message.content.lower() == "test":
        # Test with simple content
        await cl.Message(content="Simple test message").send()
        
    elif message.content.lower() == "test2":
        # Test with markdown
        content = """# Test Analysis

## AI Analysis

This is a test analysis with some content.

### Details
- Point 1
- Point 2

---
*Analysis performed by: test-model*"""
        
        await cl.Message(content=content).send()
        
    elif message.content.lower() == "test3":
        # Test with actual Gemini response format
        analysis = """Of course. Here is a detailed analysis of the provided AWS diagram.

### 1. Core Component Identification

The diagram is extremely simple and shows two elements:

*   **Title:** "AWS Test" - This immediately suggests the architecture is for a non-production environment.
*   **AWS Service:** A single box labeled **EC2**. This represents an **Amazon Elastic Compute Cloud** instance."""
        
        content = f"""# 🔍 Image Analysis Results

## 🤖 AI Analysis

{analysis}

---
*Analysis performed by: gemini-2.5-pro*"""
        
        await cl.Message(content=content).send()
        
    else:
        await cl.Message(content="Type 'test', 'test2', or 'test3' to run different tests.").send()

if __name__ == "__main__":
    cl.run()
