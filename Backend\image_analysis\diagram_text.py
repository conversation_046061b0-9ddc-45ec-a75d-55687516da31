"""
Diagram text recognition service for extracting and positioning text in diagrams.
"""
import os
import logging
import numpy as np
import cv2
from typing import Dict, Any, Optional, List, Tuple
from PIL import Image
from enum import Enum

from .ocr import OCRService, OCRLanguage, OCRMode, OCRPreprocessing


# Configure logging
logger = logging.getLogger(__name__)


class TextAlignment(str, Enum):
    """Text alignment types."""
    LEFT = "left"
    RIGHT = "right"
    CENTER = "center"
    TOP = "top"
    BOTTOM = "bottom"
    TOP_LEFT = "top_left"
    TOP_RIGHT = "top_right"
    BOTTOM_LEFT = "bottom_left"
    BOTTOM_RIGHT = "bottom_right"
    UNKNOWN = "unknown"


class TextElement:
    """Represents a text element in a diagram."""
    
    def __init__(
        self, 
        text: str, 
        bbox: Tuple[int, int, int, int],
        confidence: float = 0.0,
        alignment: TextAlignment = TextAlignment.UNKNOWN
    ):
        """
        Initialize a text element.
        
        Args:
            text: The text content
            bbox: Bounding box coordinates (x, y, width, height)
            confidence: Confidence score for the text recognition
            alignment: Text alignment
        """
        self.text = text
        self.bbox = bbox
        self.confidence = confidence
        self.alignment = alignment
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the text element to a dictionary.
        
        Returns:
            Dict[str, Any]: Dictionary representation of the text element
        """
        return {
            "text": self.text,
            "bbox": self.bbox,
            "confidence": self.confidence,
            "alignment": self.alignment
        }


class DiagramElement:
    """Represents a diagram element (shape, line, etc.)."""
    
    def __init__(
        self, 
        element_type: str,
        bbox: Tuple[int, int, int, int],
        confidence: float = 0.0
    ):
        """
        Initialize a diagram element.
        
        Args:
            element_type: Type of the element (rectangle, circle, line, etc.)
            bbox: Bounding box coordinates (x, y, width, height)
            confidence: Confidence score for the element detection
        """
        self.element_type = element_type
        self.bbox = bbox
        self.confidence = confidence
        self.related_text: List[TextElement] = []
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the diagram element to a dictionary.
        
        Returns:
            Dict[str, Any]: Dictionary representation of the diagram element
        """
        return {
            "element_type": self.element_type,
            "bbox": self.bbox,
            "confidence": self.confidence,
            "related_text": [text.to_dict() for text in self.related_text]
        }


class DiagramTextRecognitionService:
    """
    Service for extracting and positioning text in diagrams.
    """
    
    def __init__(self, ocr_service: Optional[OCRService] = None):
        """
        Initialize the diagram text recognition service.
        
        Args:
            ocr_service: OCR service for text extraction
        """
        # Initialize OCR service if not provided
        self.ocr_service = ocr_service or OCRService({
            "language": OCRLanguage.ENGLISH,
            "mode": OCRMode.DEFAULT,
            "preprocessing": OCRPreprocessing.ALL,
            "confidence_threshold": 0
        })
    
    def extract_text_elements(self, image_path: str) -> List[TextElement]:
        """
        Extract text elements from a diagram image.
        
        Args:
            image_path: Path to the diagram image
            
        Returns:
            List[TextElement]: List of extracted text elements
        """
        try:
            # Skip processing for non-image files or non-existent files
            if not os.path.exists(image_path) or image_path.endswith('.svg'):
                logger.warning(f"Skipping diagram text extraction for non-image or non-existent file: {image_path}")
                return []
            
            # Extract text with layout information
            layout_data = self.ocr_service.extract_text_with_layout(
                image_path=image_path,
                preprocessing=OCRPreprocessing.ALL
            )
            
            # Extract text elements from layout data
            text_elements = []
            for block in layout_data.get("blocks", []):
                for line in block.get("lines", []):
                    for word in line.get("words", []):
                        text = word.get("text", "").strip()
                        if not text:
                            continue
                            
                        bbox = word.get("bbox", [0, 0, 0, 0])
                        confidence = word.get("confidence", 0.0)
                        
                        # Create text element
                        text_element = TextElement(
                            text=text,
                            bbox=tuple(bbox),
                            confidence=confidence
                        )
                        
                        # Determine text alignment
                        text_element.alignment = self._determine_alignment(
                            image_path, bbox
                        )
                        
                        text_elements.append(text_element)
            
            return text_elements
        except Exception as e:
            logger.error(f"Error extracting text elements from diagram: {str(e)}")
            return []
    
    def extract_labels(self, image_path: str) -> Dict[str, List[TextElement]]:
        """
        Extract labels from a diagram image.
        
        Args:
            image_path: Path to the diagram image
            
        Returns:
            Dict[str, List[TextElement]]: Dictionary of label types and their text elements
        """
        try:
            # Extract text elements
            text_elements = self.extract_text_elements(image_path)
            
            # Categorize text elements as labels
            labels = {
                "component": [],
                "connection": [],
                "annotation": [],
                "title": [],
                "other": []
            }
            
            # Load the image for analysis
            img = cv2.imread(image_path)
            if img is None:
                logger.warning(f"Failed to load image for label extraction: {image_path}")
                return labels
            
            height, width = img.shape[:2]
            
            for element in text_elements:
                x, y, w, h = element.bbox
                
                # Skip elements with invalid bounding boxes
                if x < 0 or y < 0 or w <= 0 or h <= 0 or x + w > width or y + h > height:
                    continue
                
                # Categorize based on position and size
                if self._is_title(element, width, height):
                    labels["title"].append(element)
                elif self._is_component_label(element, img):
                    labels["component"].append(element)
                elif self._is_connection_label(element, img):
                    labels["connection"].append(element)
                elif self._is_annotation(element, img):
                    labels["annotation"].append(element)
                else:
                    labels["other"].append(element)
            
            return labels
        except Exception as e:
            logger.error(f"Error extracting labels from diagram: {str(e)}")
            return {
                "component": [],
                "connection": [],
                "annotation": [],
                "title": [],
                "other": []
            }
    
    def detect_diagram_elements(self, image_path: str) -> List[DiagramElement]:
        """
        Detect diagram elements (shapes, lines) in an image.
        
        Args:
            image_path: Path to the diagram image
            
        Returns:
            List[DiagramElement]: List of detected diagram elements
        """
        try:
            # Skip processing for non-image files or non-existent files
            if not os.path.exists(image_path) or image_path.endswith('.svg'):
                logger.warning(f"Skipping diagram element detection for non-image or non-existent file: {image_path}")
                return []
            
            # Load the image
            img = cv2.imread(image_path)
            if img is None:
                logger.warning(f"Failed to load image for diagram element detection: {image_path}")
                return []
            
            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # Apply edge detection
            edges = cv2.Canny(blurred, 50, 150)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Process contours to identify diagram elements
            elements = []
            for contour in contours:
                # Skip small contours
                if cv2.contourArea(contour) < 100:
                    continue
                
                # Get bounding box
                x, y, w, h = cv2.boundingRect(contour)
                
                # Determine element type based on shape
                element_type = self._determine_element_type(contour)
                
                # Create diagram element
                element = DiagramElement(
                    element_type=element_type,
                    bbox=(x, y, w, h),
                    confidence=0.8  # Placeholder confidence score
                )
                
                elements.append(element)
            
            # Detect lines separately
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, minLineLength=50, maxLineGap=10)
            if lines is not None:
                for line in lines:
                    x1, y1, x2, y2 = line[0]
                    # Create a bounding box for the line
                    x = min(x1, x2)
                    y = min(y1, y2)
                    w = abs(x2 - x1)
                    h = abs(y2 - y1)
                    
                    # Add some minimum dimensions for thin lines
                    if w < 5:
                        w = 5
                    if h < 5:
                        h = 5
                    
                    # Create diagram element for the line
                    element = DiagramElement(
                        element_type="line",
                        bbox=(x, y, w, h),
                        confidence=0.7  # Placeholder confidence score
                    )
                    
                    elements.append(element)
            
            return elements
        except Exception as e:
            logger.error(f"Error detecting diagram elements: {str(e)}")
            return []
    
    def associate_text_with_elements(self, image_path: str) -> Dict[str, Any]:
        """
        Associate text elements with diagram elements.
        
        Args:
            image_path: Path to the diagram image
            
        Returns:
            Dict[str, Any]: Dictionary of diagram elements with associated text
        """
        try:
            # Extract text elements
            text_elements = self.extract_text_elements(image_path)
            
            # Detect diagram elements
            diagram_elements = self.detect_diagram_elements(image_path)
            
            # Associate text with diagram elements
            for text_element in text_elements:
                # Find the nearest diagram element
                nearest_element = self._find_nearest_element(text_element, diagram_elements)
                
                # Associate text with the nearest element
                if nearest_element is not None:
                    nearest_element.related_text.append(text_element)
            
            # Create result dictionary
            result = {
                "elements": [element.to_dict() for element in diagram_elements],
                "unassociated_text": [
                    element.to_dict() for element in text_elements
                    if not any(element in e.related_text for e in diagram_elements)
                ]
            }
            
            return result
        except Exception as e:
            logger.error(f"Error associating text with diagram elements: {str(e)}")
            return {"elements": [], "unassociated_text": []}
    
    def extract_text_with_positions(self, image_path: str) -> Dict[str, Any]:
        """
        Extract text with positions from a diagram image.
        
        Args:
            image_path: Path to the diagram image
            
        Returns:
            Dict[str, Any]: Dictionary of extracted text with positions
        """
        try:
            # Extract text elements
            text_elements = self.extract_text_elements(image_path)
            
            # Extract labels
            labels = self.extract_labels(image_path)
            
            # Associate text with diagram elements
            element_associations = self.associate_text_with_elements(image_path)
            
            # Create result dictionary
            result = {
                "text_elements": [element.to_dict() for element in text_elements],
                "labels": {
                    label_type: [element.to_dict() for element in elements]
                    for label_type, elements in labels.items()
                },
                "element_associations": element_associations
            }
            
            return result
        except Exception as e:
            logger.error(f"Error extracting text with positions from diagram: {str(e)}")
            return {"text_elements": [], "labels": {}, "element_associations": {}}
    
    def _determine_alignment(self, image_path: str, bbox: Tuple[int, int, int, int]) -> TextAlignment:
        """
        Determine the alignment of a text element.
        
        Args:
            image_path: Path to the image
            bbox: Bounding box coordinates (x, y, width, height)
            
        Returns:
            TextAlignment: Text alignment
        """
        try:
            # Load the image
            img = cv2.imread(image_path)
            if img is None:
                return TextAlignment.UNKNOWN
                
            height, width = img.shape[:2]
            x, y, w, h = bbox
            
            # Calculate center positions
            center_x = x + w // 2
            center_y = y + h // 2
            
            # Determine horizontal alignment
            if center_x < width // 3:
                h_alignment = "LEFT"
            elif center_x > 2 * width // 3:
                h_alignment = "RIGHT"
            else:
                h_alignment = "CENTER"
            
            # Determine vertical alignment
            if center_y < height // 3:
                v_alignment = "TOP"
            elif center_y > 2 * height // 3:
                v_alignment = "BOTTOM"
            else:
                v_alignment = "CENTER"
            
            # Combine alignments for corners
            if h_alignment != "CENTER" and v_alignment != "CENTER":
                combined = f"{v_alignment}_{h_alignment}"
                return TextAlignment(combined.lower())
            elif h_alignment != "CENTER":
                return TextAlignment(h_alignment.lower())
            elif v_alignment != "CENTER":
                return TextAlignment(v_alignment.lower())
            else:
                return TextAlignment.CENTER
        except Exception as e:
            logger.warning(f"Error determining text alignment: {str(e)}")
            return TextAlignment.UNKNOWN
    
    def _is_title(self, element: TextElement, width: int, height: int) -> bool:
        """
        Check if a text element is a title.
        
        Args:
            element: Text element to check
            width: Image width
            height: Image height
            
        Returns:
            bool: True if the element is a title
        """
        x, y, w, h = element.bbox
        
        # Titles are typically at the top of the image
        if y < height * 0.2:
            # Titles are typically centered
            center_x = x + w // 2
            if width * 0.3 < center_x < width * 0.7:
                # Titles are typically larger than other text
                if w > width * 0.2 or h > height * 0.05:
                    return True
        
        return False
    
    def _is_component_label(self, element: TextElement, img: np.ndarray) -> bool:
        """
        Check if a text element is a component label.
        
        Args:
            element: Text element to check
            img: Image as NumPy array
            
        Returns:
            bool: True if the element is a component label
        """
        x, y, w, h = element.bbox
        
        # Extract a region around the text
        padding = 10
        x1 = max(0, x - padding)
        y1 = max(0, y - padding)
        x2 = min(img.shape[1], x + w + padding)
        y2 = min(img.shape[0], y + h + padding)
        
        region = img[y1:y2, x1:x2]
        
        # Convert to grayscale
        if len(region.shape) == 3:
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
        else:
            gray = region
        
        # Apply edge detection
        edges = cv2.Canny(gray, 50, 150)
        
        # Count edge pixels
        edge_count = np.count_nonzero(edges)
        
        # Component labels are typically inside or near shapes with edges
        if edge_count > 0:
            edge_density = edge_count / (region.shape[0] * region.shape[1])
            if 0.05 < edge_density < 0.5:
                return True
        
        return False
    
    def _is_connection_label(self, element: TextElement, img: np.ndarray) -> bool:
        """
        Check if a text element is a connection label.
        
        Args:
            element: Text element to check
            img: Image as NumPy array
            
        Returns:
            bool: True if the element is a connection label
        """
        x, y, w, h = element.bbox
        
        # Extract a region around the text
        padding = 20
        x1 = max(0, x - padding)
        y1 = max(0, y - padding)
        x2 = min(img.shape[1], x + w + padding)
        y2 = min(img.shape[0], y + h + padding)
        
        region = img[y1:y2, x1:x2]
        
        # Convert to grayscale
        if len(region.shape) == 3:
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
        else:
            gray = region
        
        # Apply line detection
        edges = cv2.Canny(gray, 50, 150)
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, minLineLength=20, maxLineGap=10)
        
        # Connection labels are typically near lines
        if lines is not None and len(lines) > 0:
            return True
        
        return False
    
    def _is_annotation(self, element: TextElement, img: np.ndarray) -> bool:
        """
        Check if a text element is an annotation.
        
        Args:
            element: Text element to check
            img: Image as NumPy array
            
        Returns:
            bool: True if the element is an annotation
        """
        # Annotations are typically longer text
        if len(element.text.split()) > 3:
            return True
        
        return False
        
    def _determine_element_type(self, contour: np.ndarray) -> str:
        """
        Determine the type of a diagram element based on its contour.
        
        Args:
            contour: Contour of the element
            
        Returns:
            str: Type of the element (rectangle, circle, polygon, etc.)
        """
        # Approximate the contour to a polygon
        epsilon = 0.04 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # Get the number of vertices
        vertices = len(approx)
        
        # Determine shape based on number of vertices
        if vertices == 3:
            return "triangle"
        elif vertices == 4:
            # Check if it's a rectangle or square
            x, y, w, h = cv2.boundingRect(approx)
            aspect_ratio = float(w) / h
            if 0.95 <= aspect_ratio <= 1.05:
                return "square"
            else:
                return "rectangle"
        elif vertices == 5:
            return "pentagon"
        elif vertices == 6:
            return "hexagon"
        elif vertices > 6:
            # Check if it's a circle
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)
            circularity = 4 * np.pi * area / (perimeter * perimeter)
            if circularity > 0.8:
                return "circle"
            else:
                return "polygon"
        else:
            return "unknown"
    
    def _find_nearest_element(self, text_element: TextElement, diagram_elements: List[DiagramElement]) -> Optional[DiagramElement]:
        """
        Find the nearest diagram element to a text element.
        
        Args:
            text_element: Text element
            diagram_elements: List of diagram elements
            
        Returns:
            Optional[DiagramElement]: Nearest diagram element, or None if no elements are found
        """
        if not diagram_elements:
            return None
        
        # Get text element center
        tx, ty, tw, th = text_element.bbox
        text_center_x = tx + tw // 2
        text_center_y = ty + th // 2
        
        # Find the nearest element
        nearest_element = None
        min_distance = float('inf')
        
        for element in diagram_elements:
            # Get element center
            ex, ey, ew, eh = element.bbox
            element_center_x = ex + ew // 2
            element_center_y = ey + eh // 2
            
            # Calculate Euclidean distance between centers
            distance = np.sqrt((text_center_x - element_center_x) ** 2 + (text_center_y - element_center_y) ** 2)
            
            # Check if this element is closer
            if distance < min_distance:
                min_distance = distance
                nearest_element = element
        
        # Only return the element if it's reasonably close
        # (within 2x the diagonal of the text bounding box)
        max_distance = 2 * np.sqrt(tw ** 2 + th ** 2)
        if min_distance <= max_distance:
            return nearest_element
        else:
            return None
    
    def get_text_element_relationships(self, image_path: str) -> Dict[str, Any]:
        """
        Get relationships between text elements in a diagram.
        
        Args:
            image_path: Path to the diagram image
            
        Returns:
            Dict[str, Any]: Dictionary of text element relationships
        """
        try:
            # Extract text elements
            text_elements = self.extract_text_elements(image_path)
            
            # Group text elements by proximity
            groups = self._group_text_by_proximity(text_elements)
            
            # Identify hierarchical relationships
            hierarchies = self._identify_hierarchical_relationships(text_elements)
            
            # Create result dictionary
            result = {
                "groups": groups,
                "hierarchies": hierarchies
            }
            
            return result
        except Exception as e:
            logger.error(f"Error getting text element relationships: {str(e)}")
            return {"groups": [], "hierarchies": []}
    
    def _group_text_by_proximity(self, text_elements: List[TextElement]) -> List[Dict[str, Any]]:
        """
        Group text elements by proximity.
        
        Args:
            text_elements: List of text elements
            
        Returns:
            List[Dict[str, Any]]: List of text element groups
        """
        if not text_elements:
            return []
        
        # Use a simple clustering approach based on distance
        groups = []
        remaining_elements = text_elements.copy()
        
        while remaining_elements:
            # Start a new group with the first element
            current_group = [remaining_elements.pop(0)]
            
            # Find all elements close to this group
            i = 0
            while i < len(remaining_elements):
                # Check if this element is close to any element in the current group
                if self._is_close_to_group(remaining_elements[i], current_group):
                    # Add to the current group
                    current_group.append(remaining_elements.pop(i))
                else:
                    i += 1
            
            # Add the group to the list
            groups.append({
                "elements": [element.to_dict() for element in current_group],
                "text": " ".join(element.text for element in current_group)
            })
        
        return groups
    
    def _is_close_to_group(self, element: TextElement, group: List[TextElement]) -> bool:
        """
        Check if a text element is close to any element in a group.
        
        Args:
            element: Text element to check
            group: Group of text elements
            
        Returns:
            bool: True if the element is close to any element in the group
        """
        # Get element center
        ex, ey, ew, eh = element.bbox
        element_center_x = ex + ew // 2
        element_center_y = ey + eh // 2
        
        # Check distance to each element in the group
        for group_element in group:
            # Get group element center
            gx, gy, gw, gh = group_element.bbox
            group_center_x = gx + gw // 2
            group_center_y = gy + gh // 2
            
            # Calculate Euclidean distance between centers
            distance = np.sqrt((element_center_x - group_center_x) ** 2 + (element_center_y - group_center_y) ** 2)
            
            # Check if the elements are close
            # (within 3x the average height of the two elements)
            max_distance = 3 * ((eh + gh) / 2)
            if distance <= max_distance:
                return True
        
        return False
    
    def _identify_hierarchical_relationships(self, text_elements: List[TextElement]) -> List[Dict[str, Any]]:
        """
        Identify hierarchical relationships between text elements.
        
        Args:
            text_elements: List of text elements
            
        Returns:
            List[Dict[str, Any]]: List of hierarchical relationships
        """
        if not text_elements:
            return []
        
        # Sort elements by y-coordinate (top to bottom)
        sorted_elements = sorted(text_elements, key=lambda e: e.bbox[1])
        
        # Identify potential parent-child relationships
        hierarchies = []
        
        for i, parent in enumerate(sorted_elements):
            children = []
            
            # Check for potential children (elements below and indented)
            for j, child in enumerate(sorted_elements[i+1:], i+1):
                # Check if the child is below the parent
                if child.bbox[1] > parent.bbox[1] + parent.bbox[3]:
                    # Check if the child is indented relative to the parent
                    if child.bbox[0] > parent.bbox[0] + 10:
                        children.append(child)
                    
                    # Stop if we find an element that's not indented
                    # (likely a sibling or higher level)
                    elif child.bbox[0] <= parent.bbox[0] + 5:
                        break
            
            # Add the hierarchy if children were found
            if children:
                hierarchies.append({
                    "parent": parent.to_dict(),
                    "children": [child.to_dict() for child in children]
                })
        
        return hierarchies