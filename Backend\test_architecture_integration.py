"""
Integration test for the architecture analyzer with the orchestrator.
"""
import pytest
from unittest.mock import MagicMock

from image_analysis.orchestrator import AnalysisOrchestrator
from image_analysis.models import ImageAnalysisRequest, ImageType, AnalysisStatus
from image_analysis.diagram_components import DiagramComponent, DiagramConnection, ComponentType
from image_analysis.models import BoundingBox


class TestArchitectureIntegration:
    """Integration tests for architecture analysis."""
    
    @pytest.fixture
    def mock_services(self):
        """Create mock services for testing."""
        image_store = MagicMock()
        result_store = MagicMock()
        ocr_service = MagicMock()
        error_recognition_service = MagicMock()
        diagram_text_service = MagicMock()
        image_classifier = MagicMock()
        diagram_component_service = MagicMock()
        
        # Configure mock image_store
        image_store.image_exists.return_value = True
        
        # Configure mock result_store
        mock_request = ImageAnalysisRequest(
            id="test-request-id",
            original_filename="architecture.png",
            content_type="image/png",
            file_size=1024,
            storage_path="/tmp/architecture.png"
        )
        result_store.get_request.return_value = mock_request
        
        # Configure mock ocr_service
        ocr_service.extract_text.return_value = "Web Client API Service Database"
        ocr_service.extract_text_with_layout.return_value = {
            "text": "Web Client API Service Database",
            "blocks": []
        }
        ocr_service.extract_tables.return_value = []
        
        # Configure mock image_classifier
        image_classifier.classify_image.return_value = (
            ImageType.ARCHITECTURE,
            0.9,
            {"has_components": True, "has_connections": True}
        )
        
        # Configure mock diagram_component_service with realistic architecture components
        components = [
            DiagramComponent(
                component_id="web_client",
                component_type=ComponentType.BOX,
                bounding_box=BoundingBox(x=50, y=50, width=100, height=50),
                confidence=0.9,
                text="Web Client"
            ),
            DiagramComponent(
                component_id="api_gateway",
                component_type=ComponentType.DIAMOND,
                bounding_box=BoundingBox(x=50, y=150, width=100, height=50),
                confidence=0.85,
                text="API Gateway"
            ),
            DiagramComponent(
                component_id="user_service",
                component_type=ComponentType.BOX,
                bounding_box=BoundingBox(x=200, y=150, width=100, height=50),
                confidence=0.9,
                text="User Service"
            ),
            DiagramComponent(
                component_id="order_service",
                component_type=ComponentType.BOX,
                bounding_box=BoundingBox(x=350, y=150, width=100, height=50),
                confidence=0.9,
                text="Order Service"
            ),
            DiagramComponent(
                component_id="database",
                component_type=ComponentType.CIRCLE,
                bounding_box=BoundingBox(x=275, y=250, width=100, height=50),
                confidence=0.8,
                text="Database",
                properties={"is_database": True}
            )
        ]
        
        connections = [
            DiagramConnection(
                connection_id="client_to_gateway",
                source_id="web_client",
                target_id="api_gateway",
                connection_type="arrow",
                points=[(100, 100), (100, 150)],
                confidence=0.8,
                text="HTTP Request"
            ),
            DiagramConnection(
                connection_id="gateway_to_user_service",
                source_id="api_gateway",
                target_id="user_service",
                connection_type="arrow",
                points=[(150, 175), (200, 175)],
                confidence=0.8,
                text="Route"
            ),
            DiagramConnection(
                connection_id="gateway_to_order_service",
                source_id="api_gateway",
                target_id="order_service",
                connection_type="arrow",
                points=[(150, 175), (350, 175)],
                confidence=0.8,
                text="Route"
            ),
            DiagramConnection(
                connection_id="user_service_to_db",
                source_id="user_service",
                target_id="database",
                connection_type="arrow",
                points=[(250, 200), (300, 250)],
                confidence=0.8,
                text="Query"
            ),
            DiagramConnection(
                connection_id="order_service_to_db",
                source_id="order_service",
                target_id="database",
                connection_type="arrow",
                points=[(400, 200), (350, 250)],
                confidence=0.8,
                text="Query"
            )
        ]
        
        diagram_component_service.recognize_components.return_value = (components, connections)
        
        # Configure mock diagram_text_service
        diagram_text_service.extract_labels.return_value = {
            "title": [MagicMock(text="Microservices Architecture", confidence=0.9)]
        }
        
        return {
            "image_store": image_store,
            "result_store": result_store,
            "ocr_service": ocr_service,
            "error_recognition_service": error_recognition_service,
            "diagram_text_service": diagram_text_service,
            "image_classifier": image_classifier,
            "diagram_component_service": diagram_component_service
        }
    
    @pytest.fixture
    def orchestrator(self, mock_services):
        """Create an AnalysisOrchestrator instance for testing."""
        return AnalysisOrchestrator(
            image_store=mock_services["image_store"],
            result_store=mock_services["result_store"],
            ocr_service=mock_services["ocr_service"],
            error_recognition_service=mock_services["error_recognition_service"],
            diagram_text_service=mock_services["diagram_text_service"],
            image_classifier=mock_services["image_classifier"],
            diagram_component_service=mock_services["diagram_component_service"]
        )
    
    @pytest.mark.asyncio
    async def test_architecture_analysis_integration(self, orchestrator, mock_services):
        """Test end-to-end architecture analysis."""
        # Arrange
        request_id = "test-architecture-request"
        
        # Act
        result = await orchestrator.process_request(request_id)
        
        # Assert
        assert result is True
        
        # Verify that the result was stored
        mock_services["result_store"].store_result.assert_called()
        
        # Get the stored result
        stored_result = mock_services["result_store"].store_result.call_args[0][0]
        
        # Verify the analysis contains architecture-specific information
        assert stored_result.analysis_type == ImageType.ARCHITECTURE
        assert stored_result.confidence == 0.9
        
        # Verify the analysis contains sophisticated architecture insights
        analysis = stored_result.analysis
        assert "architecture" in analysis.summary.lower()
        
        # Should have component and relationship details
        component_details = [d for d in analysis.details if d.type == "component"]
        relationship_details = [d for d in analysis.details if d.type == "relationship"]
        
        assert len(component_details) > 0
        assert len(relationship_details) > 0
        
        # Should have architecture-specific recommendations
        assert len(analysis.recommendations) > 0
        
        # Verify that sophisticated analysis was performed
        # The architecture analyzer should identify patterns and roles
        assert any("service" in detail.content.lower() for detail in component_details)
    
    @pytest.mark.asyncio
    async def test_microservices_pattern_detection(self, orchestrator, mock_services):
        """Test that microservices pattern is detected correctly."""
        # Arrange
        request_id = "test-microservices-request"
        
        # Act - Process the request which has multiple services
        context = {
            "step_results": {
                "classification": {
                    "image_type": ImageType.ARCHITECTURE,
                    "confidence": 0.9
                },
                "feature_extraction": {
                    "components": mock_services["diagram_component_service"].recognize_components.return_value[0],
                    "connections": mock_services["diagram_component_service"].recognize_components.return_value[1],
                    "labels": {"title": [MagicMock(text="Microservices Architecture", confidence=0.9)]}
                },
                "text_extraction": {
                    "text_content": "Web Client API Gateway User Service Order Service Database"
                }
            }
        }
        
        success, analysis_result = await orchestrator._analyze_content(context)
        
        # Assert
        assert success is True
        assert "patterns" in analysis_result
        
        # Should detect microservices pattern due to multiple services
        patterns = analysis_result.get("patterns", [])
        pattern_values = [p.value if hasattr(p, 'value') else str(p) for p in patterns]
        assert "microservices" in pattern_values
        
        # Should have insights about microservices
        insights = analysis_result.get("insights", [])
        microservices_insights = [i for i in insights if "microservices" in i.lower()]
        assert len(microservices_insights) > 0
    
    @pytest.mark.asyncio
    async def test_component_role_classification(self, orchestrator, mock_services):
        """Test that component roles are classified correctly."""
        # Arrange
        context = {
            "step_results": {
                "classification": {
                    "image_type": ImageType.ARCHITECTURE,
                    "confidence": 0.9
                },
                "feature_extraction": {
                    "components": mock_services["diagram_component_service"].recognize_components.return_value[0],
                    "connections": mock_services["diagram_component_service"].recognize_components.return_value[1],
                    "labels": {}
                },
                "text_extraction": {
                    "text_content": "Web Client API Gateway User Service Order Service Database"
                }
            }
        }
        
        # Act
        success, analysis_result = await orchestrator._analyze_content(context)
        
        # Assert
        assert success is True
        assert "component_roles" in analysis_result
        
        component_roles = analysis_result["component_roles"]
        
        # Verify specific role classifications
        assert component_roles["web_client"].value == "client"
        assert component_roles["api_gateway"].value == "gateway"
        assert component_roles["user_service"].value == "service"
        assert component_roles["order_service"].value == "service"
        assert component_roles["database"].value == "database"
    
    @pytest.mark.asyncio
    async def test_relationship_analysis(self, orchestrator, mock_services):
        """Test that relationships are analyzed correctly."""
        # Arrange
        context = {
            "step_results": {
                "classification": {
                    "image_type": ImageType.ARCHITECTURE,
                    "confidence": 0.9
                },
                "feature_extraction": {
                    "components": mock_services["diagram_component_service"].recognize_components.return_value[0],
                    "connections": mock_services["diagram_component_service"].recognize_components.return_value[1],
                    "labels": {}
                },
                "text_extraction": {
                    "text_content": "Web Client API Gateway User Service Order Service Database"
                }
            }
        }
        
        # Act
        success, analysis_result = await orchestrator._analyze_content(context)
        
        # Assert
        assert success is True
        assert "relationships" in analysis_result
        
        relationships = analysis_result["relationships"]
        assert len(relationships) > 0
        
        # Verify specific relationship types
        client_to_gateway = next((r for r in relationships if r["source_id"] == "web_client"), None)
        assert client_to_gateway is not None
        assert client_to_gateway["relationship_type"] == "client_request"
        
        service_to_db = next((r for r in relationships if r["target_id"] == "database"), None)
        assert service_to_db is not None
        assert service_to_db["relationship_type"] == "data_access"