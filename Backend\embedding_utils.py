import torch
from PIL import Image
from transformers import CLIPProcessor, CLIPModel
from typing import List, Union
import base64
from io import BytesIO

class MultiModalEmbeddings:
    """
    A class to generate multi-modal embeddings for text and images using a CLIP model.
    """
    def __init__(self, model_name: str = "openai/clip-vit-base-patch32"):
        """
        Initializes the multi-modal embedding model and processor.

        Args:
            model_name (str): The name of the pre-trained CLIP model to use.
        """
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model = CLIPModel.from_pretrained(model_name).to(self.device)
        self.processor = CLIPProcessor.from_pretrained(model_name)

    def embed_text(self, texts: List[str]) -> List[List[float]]:
        """
        Generates embeddings for a list of text strings.

        Args:
            texts (List[str]): A list of text strings to embed.

        Returns:
            List[List[float]]: A list of embeddings, where each embedding is a list of floats.
        """
        inputs = self.processor(text=texts, return_tensors="pt", padding=True, truncation=True).to(self.device)
        with torch.no_grad():
            text_features = self.model.get_text_features(**inputs)
        return text_features.cpu().numpy().tolist()

    def embed_images(self, images: List[Union[str, Image.Image]]) -> List[List[float]]:
        """
        Generates embeddings for a list of images.

        Args:
            images (List[Union[str, Image.Image]]): A list of images. Each image can be a
                                                     file path, a PIL Image object, or a
                                                     base64-encoded string.

        Returns:
            List[List[float]]: A list of embeddings, where each embedding is a list of floats.
        """
        processed_images = []
        for image_data in images:
            if isinstance(image_data, str):
                if image_data.startswith('data:image'):
                    # Handle base64 data URL
                    header, encoded = image_data.split(",", 1)
                    image_bytes = base64.b64decode(encoded)
                    image = Image.open(BytesIO(image_bytes)).convert("RGB")
                elif len(image_data) % 4 == 0 and base64.b64decode(image_data, validate=True):
                     # Handle raw base64 string
                    image_bytes = base64.b64decode(image_data)
                    image = Image.open(BytesIO(image_bytes)).convert("RGB")
                else:
                    # Assume it's a file path
                    image = Image.open(image_data).convert("RGB")
            elif isinstance(image_data, Image.Image):
                image = image_data.convert("RGB")
            else:
                raise ValueError("Unsupported image format. Provide a file path, PIL Image, or base64 string.")
            processed_images.append(image)

        inputs = self.processor(images=processed_images, return_tensors="pt", padding=True).to(self.device)
        with torch.no_grad():
            image_features = self.model.get_image_features(**inputs)
        return image_features.cpu().numpy().tolist()

if __name__ == '__main__':
    # Example usage
    embedder = MultiModalEmbeddings()

    # Embed text
    texts = ["a photo of a cat", "a photo of a dog"]
    text_embeddings = embedder.embed_text(texts)
    print("Text embeddings shape:", (len(text_embeddings), len(text_embeddings[0])))

    # Embed an image from a file
    try:
        # Create a dummy image for testing
        dummy_image = Image.new('RGB', (60, 30), color = 'red')
        dummy_image.save('dummy_cat.png')
        
        images = ['dummy_cat.png']
        image_embeddings = embedder.embed_images(images)
        print("Image embeddings shape:", (len(image_embeddings), len(image_embeddings[0])))
    except Exception as e:
        print(f"Could not test image embedding. Is an image file available? Error: {e}")