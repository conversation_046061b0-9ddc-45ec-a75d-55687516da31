#!/usr/bin/env python3
"""
Debug the exact frontend call without chainlit.
"""

import asyncio
import aiohttp
import json
import os
import sys
from typing import Dict, Any, Optional
from PIL import Image, ImageDraw
from io import BytesIO

# Same imports as frontend
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend'))

# Same constants as frontend
TIMEOUT = aiohttp.ClientTimeout(total=120)
API_BASE_URL = "http://localhost:8888"

def create_test_image():
    """Create a test image."""
    img = Image.new('RGB', (200, 150), color='white')
    draw = ImageDraw.Draw(img)
    draw.text((20, 20), "AWS Test", fill='black')
    draw.rectangle([20, 50, 80, 90], outline='blue', width=2)
    draw.text((25, 65), "EC2", fill='blue')
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

async def analyze_image_with_ai_exact_copy(image_bytes: bytes, use_gemini: bool = False, custom_prompt: Optional[str] = None) -> Dict[str, Any]:
    """
    EXACT COPY of the frontend's analyze_image_with_ai function.
    """
    # Choose the appropriate endpoint based on the use_gemini flag
    if use_gemini:
        url = f"{API_BASE_URL}/analyze/image/gemini"
    else:
        url = f"{API_BASE_URL}/analyze/image"
        
    data = aiohttp.FormData()
    data.add_field('file', image_bytes, filename='upload.png', content_type='image/png')
    
    # Add custom prompt for Gemini
    if use_gemini and custom_prompt:
        data.add_field('prompt', custom_prompt)
    
    async with aiohttp.ClientSession(timeout=TIMEOUT) as session:
        async with session.post(url, data=data) as response:
            print(f"FRONTEND DEBUG: Response status: {response.status}")
            if response.status == 200:
                result = await response.json()
                print(f"FRONTEND DEBUG: Raw result keys: {list(result.keys())}")
                print(f"FRONTEND DEBUG: Raw analysis type: {type(result.get('analysis'))}")
                print(f"FRONTEND DEBUG: Raw analysis length: {len(result.get('analysis', ''))}")
                print(f"FRONTEND DEBUG: Raw analysis preview: {repr(result.get('analysis', '')[:100])}")
                return result
            else:
                error_text = await response.text()
                raise Exception(f"Image analysis failed: {error_text}")

async def simulate_frontend_exact():
    """Simulate the exact frontend flow."""
    print("=" * 60)
    print("SIMULATING EXACT FRONTEND FLOW")
    print("=" * 60)
    
    # Step 1: Create image (simulating file upload)
    file_content = create_test_image()
    print(f"Step 1: Created image: {len(file_content)} bytes")
    
    # Step 2: Set analysis choice (simulating user choice)
    analysis_choice_value = "gemini"
    print(f"Step 2: Analysis choice: {analysis_choice_value}")
    
    # Step 3: Determine use_gemini flag (exact same logic as frontend)
    use_gemini = analysis_choice_value in ["gemini", "openrouter"]  # Support both for backward compatibility
    print(f"Step 3: use_gemini flag: {use_gemini}")
    
    # Step 4: Call analyze_image_with_ai (exact same call as frontend)
    print("Step 4: Calling analyze_image_with_ai...")
    analysis_result = await analyze_image_with_ai_exact_copy(file_content, use_gemini)
    
    # Step 5: Debug what we got back (exact same debug as frontend)
    print(f"Step 5: Got result with keys: {list(analysis_result.keys()) if analysis_result else 'None'}")
    if analysis_result:
        print(f"Step 5: Status: {analysis_result.get('status')}, Analysis length: {len(analysis_result.get('analysis', ''))}")
    
    # Step 6: Process the result (exact same logic as frontend)
    if analysis_result.get("status") == "error":
        error_msg = analysis_result.get("error", "Unknown error")
        print(f"Step 6: ERROR - {error_msg}")
    else:
        # Extract text content if available
        extracted_text = analysis_result.get("extracted_text", "").strip()
        extracted_text_section = ""
        if extracted_text:
            extracted_text_section = f"""
## 📝 **Extracted Text**

```
{extracted_text[:500]}
```
{'' if len(extracted_text) <= 500 else '*(text truncated)*'}

"""
        
        # Format the analysis (exact same logic as frontend)
        analysis = analysis_result.get("analysis", "No analysis available")
        model_id = analysis_result.get("model_id", "Unknown model")

        # Debug logging (exact same as frontend)
        print(f"Step 6: Raw analysis type: {type(analysis)}")
        print(f"Step 6: Raw analysis length: {len(analysis) if analysis else 0}")
        print(f"Step 6: Raw analysis value: {repr(analysis[:100]) if analysis else 'None'}")
        print(f"Step 6: Analysis result keys: {list(analysis_result.keys())}")

        # Ensure analysis is a string and not empty (exact same as frontend)
        if not analysis or not isinstance(analysis, str):
            print(f"Step 6: Analysis failed validation - type: {type(analysis)}, empty: {not analysis}")
            analysis = "No analysis content received from the AI model."
        
        print(f"\nFINAL RESULT:")
        print(f"  Analysis type: {type(analysis)}")
        print(f"  Analysis length: {len(analysis)}")
        print(f"  Analysis preview: {analysis[:200]}...")

async def main():
    """Main function."""
    try:
        await simulate_frontend_exact()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
