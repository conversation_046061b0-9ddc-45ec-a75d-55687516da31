"""
Simple unit tests for the analysis orchestrator implementation.
"""
import pytest
from unittest.mock import MagicMock, patch

from image_analysis.models import (
    ImageAnalysisRequest, 
    ImageAnalysisResult, 
    ImageType, 
    AnalysisStatus
)
from image_analysis.orchestrator import AnalysisOrchestrator


class TestAnalysisOrchestratorSimple:
    """Simple test cases for the AnalysisOrchestrator class."""
    
    @pytest.fixture
    def mock_services(self):
        """Create mock services for testing."""
        image_store = MagicMock()
        result_store = MagicMock()
        ocr_service = MagicMock()
        error_recognition_service = MagicMock()
        diagram_text_service = MagicMock()
        image_classifier = MagicMock()
        diagram_component_service = MagicMock()
        
        # Configure mock image_store
        image_store.image_exists.return_value = True
        
        # Configure mock result_store
        mock_request = ImageAnalysisRequest(
            id="test-request-id",
            original_filename="test.png",
            content_type="image/png",
            file_size=1024,
            storage_path="/tmp/test.png"
        )
        result_store.get_request.return_value = mock_request
        
        # Configure mock ocr_service
        ocr_service.extract_text.return_value = "Sample text content"
        ocr_service.extract_text_with_layout.return_value = {
            "text": "Sample text content",
            "blocks": []
        }
        ocr_service.extract_tables.return_value = []
        
        # Configure mock image_classifier
        image_classifier.classify_image.return_value = (
            ImageType.ARCHITECTURE,
            0.85,
            {"feature1": 0.9, "feature2": 0.8}
        )
        
        # Configure mock diagram_component_service
        diagram_component_service.recognize_components.return_value = ([], [])
        
        # Configure mock diagram_text_service
        diagram_text_service.extract_labels.return_value = {}
        
        # Configure mock error_recognition_service
        error_recognition_service.identify_errors.return_value = []
        error_recognition_service.suggest_solutions.return_value = []
        error_recognition_service.classify_error_type.return_value = (None, None, 0.0)
        
        return {
            "image_store": image_store,
            "result_store": result_store,
            "ocr_service": ocr_service,
            "error_recognition_service": error_recognition_service,
            "diagram_text_service": diagram_text_service,
            "image_classifier": image_classifier,
            "diagram_component_service": diagram_component_service
        }
    
    @pytest.fixture
    def orchestrator(self, mock_services):
        """Create an AnalysisOrchestrator instance for testing."""
        return AnalysisOrchestrator(
            image_store=mock_services["image_store"],
            result_store=mock_services["result_store"],
            ocr_service=mock_services["ocr_service"],
            error_recognition_service=mock_services["error_recognition_service"],
            diagram_text_service=mock_services["diagram_text_service"],
            image_classifier=mock_services["image_classifier"],
            diagram_component_service=mock_services["diagram_component_service"]
        )
    
    @pytest.mark.asyncio
    async def test_process_request_success(self, orchestrator, mock_services):
        """Test successful processing of a request."""
        # Arrange
        request_id = "test-request-id"
        
        # Act
        result = await orchestrator.process_request(request_id)
        
        # Assert
        assert result is True
        mock_services["result_store"].update_request.assert_called()
        mock_services["result_store"].store_result.assert_called()
        
        # Verify the request status was updated to COMPLETED
        request = mock_services["result_store"].get_request.return_value
        assert request.status == AnalysisStatus.COMPLETED
    
    @pytest.mark.asyncio
    async def test_process_request_missing_request(self, orchestrator, mock_services):
        """Test processing when the request is not found."""
        # Arrange
        request_id = "missing-request-id"
        mock_services["result_store"].get_request.return_value = None
        
        # Act
        result = await orchestrator.process_request(request_id)
        
        # Assert
        assert result is False
        mock_services["result_store"].update_request.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_process_request_validation_failure(self, orchestrator, mock_services):
        """Test processing when validation fails."""
        # Arrange
        request_id = "test-request-id"
        mock_services["image_store"].image_exists.return_value = False
        
        # Act
        result = await orchestrator.process_request(request_id)
        
        # Assert
        assert result is False
        
        # Verify the request status was updated to FAILED
        request = mock_services["result_store"].get_request.return_value
        assert request.status == AnalysisStatus.FAILED
    
    @pytest.mark.asyncio
    async def test_process_request_text_extraction_failure(self, orchestrator, mock_services):
        """Test processing when text extraction fails."""
        # Arrange
        request_id = "test-request-id"
        mock_services["ocr_service"].extract_text.side_effect = Exception("OCR error")
        
        # Act
        result = await orchestrator.process_request(request_id)
        
        # Assert
        assert result is False
        
        # Verify the request status was updated to FAILED
        request = mock_services["result_store"].get_request.return_value
        assert request.status == AnalysisStatus.FAILED
        
    @pytest.mark.asyncio
    async def test_process_request_with_retries_success(self, orchestrator, mock_services):
        """Test successful processing of a request with retries."""
        # Arrange
        request_id = "test-request-id"
        
        # Act
        result = await orchestrator.process_request_with_retries(request_id)
        
        # Assert
        assert result is True
        mock_services["result_store"].update_request.assert_called()
        mock_services["result_store"].store_result.assert_called()
        
        # Verify the request status was updated to COMPLETED
        request = mock_services["result_store"].get_request.return_value
        assert request.status == AnalysisStatus.COMPLETED
    
    @pytest.mark.asyncio
    async def test_process_request_with_retries_missing_request(self, orchestrator, mock_services):
        """Test processing when the request is not found."""
        # Arrange
        request_id = "missing-request-id"
        mock_services["result_store"].get_request.return_value = None
        
        # Act
        result = await orchestrator.process_request_with_retries(request_id)
        
        # Assert
        assert result is False
        mock_services["result_store"].update_request.assert_not_called()