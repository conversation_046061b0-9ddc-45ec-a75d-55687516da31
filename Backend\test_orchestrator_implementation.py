"""
Unit tests for the analysis orchestrator implementation.
"""
import os
import pytest
import asyncio
from unittest.mock import MagicMock, patch
from datetime import datetime

from image_analysis.models import (
    ImageAnalysisRequest, 
    ImageAnalysisResult, 
    ImageType, 
    AnalysisStatus, 
    Analysis,
    AnalysisDetail,
    Recommendation,
    BoundingBox
)
from image_analysis.orchestrator import (
    AnalysisOrchestrator, 
    ProcessingStep, 
    ProcessingStage,
    ProcessingStatus
)


class TestAnalysisOrchestratorImplementation:
    """Test cases for the AnalysisOrchestrator implementation."""
    
    # Constants for testing
    TEST_REQUEST_ID = "test-request-id"
    TEST_IMAGE_PATH = "/tmp/test.png"
    
    @pytest.fixture
    def mock_services(self):
        """Create mock services for testing."""
        image_store = MagicMock()
        result_store = MagicMock()
        ocr_service = MagicMock()
        error_recognition_service = MagicMock()
        diagram_text_service = MagicMock()
        image_classifier = MagicMock()
        diagram_component_service = MagicMock()
        
        # Configure mock image_store
        image_store.image_exists.return_value = True
        
        # Configure mock result_store
        mock_request = ImageAnalysisRequest(
            id="test-request-id",
            original_filename="test.png",
            content_type="image/png",
            file_size=1024,
            storage_path="/tmp/test.png"
        )
        result_store.get_request.return_value = mock_request
        
        # Configure mock ocr_service
        ocr_service.extract_text.return_value = "Sample text content"
        ocr_service.extract_text_with_layout.return_value = {
            "text": "Sample text content",
            "blocks": []
        }
        ocr_service.extract_tables.return_value = []
        
        # Configure mock image_classifier
        image_classifier.classify_image.return_value = (
            ImageType.ARCHITECTURE,
            0.85,
            {"feature1": 0.9, "feature2": 0.8}
        )
        
        # Configure mock diagram_component_service
        diagram_component_service.recognize_components.return_value = ([], [])
        
        # Configure mock diagram_text_service
        diagram_text_service.extract_labels.return_value = {}
        
        # Configure mock error_recognition_service
        error_recognition_service.identify_errors.return_value = []
        error_recognition_service.suggest_solutions.return_value = []
        error_recognition_service.classify_error_type.return_value = (None, None, 0.0)
        
        return {
            "image_store": image_store,
            "result_store": result_store,
            "ocr_service": ocr_service,
            "error_recognition_service": error_recognition_service,
            "diagram_text_service": diagram_text_service,
            "image_classifier": image_classifier,
            "diagram_component_service": diagram_component_service
        }
    
    @pytest.fixture
    def orchestrator(self, mock_services):
        """Create an AnalysisOrchestrator instance for testing."""
        return AnalysisOrchestrator(
            image_store=mock_services["image_store"],
            result_store=mock_services["result_store"],
            ocr_service=mock_services["ocr_service"],
            error_recognition_service=mock_services["error_recognition_service"],
            diagram_text_service=mock_services["diagram_text_service"],
            image_classifier=mock_services["image_classifier"],
            diagram_component_service=mock_services["diagram_component_service"]
        )
    
    @pytest.mark.asyncio
    async def test_process_request_with_retries_success(self, orchestrator, mock_services):
        """Test successful processing of a request with retries."""
        # Arrange
        request_id = self.TEST_REQUEST_ID
        
        # Act
        result = await orchestrator.process_request_with_retries(request_id)
        
        # Assert
        assert result is True
        mock_services["result_store"].update_request.assert_called()
        mock_services["result_store"].store_result.assert_called()
        
        # Verify the request status was updated to COMPLETED
        request = mock_services["result_store"].get_request.return_value
        assert request.status == AnalysisStatus.COMPLETED
    
    @pytest.mark.asyncio
    async def test_process_request_with_retries_missing_request(self, orchestrator, mock_services):
        """Test processing when the request is not found."""
        # Arrange
        request_id = "missing-request-id"
        mock_services["result_store"].get_request.return_value = None
        
        # Act
        result = await orchestrator.process_request_with_retries(request_id)
        
        # Assert
        assert result is False
        mock_services["result_store"].update_request.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_process_request_with_retries_step_failure_with_retry(self, orchestrator, mock_services):
        """Test processing when a step fails but succeeds on retry."""
        # Arrange
        request_id = self.TEST_REQUEST_ID
        
        # Create a mock that will fail once then succeed
        mock_extract_text = MagicMock()
        mock_extract_text.side_effect = [
            Exception("Simulated failure"),  # First call fails
            (True, {  # Second call succeeds
                "text_content": "Sample text content",
                "text_with_layout": {"text": "Sample text content", "blocks": []},
                "tables": []
            })
        ]
        
        # Replace the extract_text method with our mock
        orchestrator._extract_text = mock_extract_text
        
        # Act
        result = await orchestrator.process_request_with_retries(request_id)
        
        # Assert
        assert result is True
        assert mock_extract_text.call_count == 2  # Called twice (failure + success)
        
        # Verify the request status was updated to COMPLETED
        request = mock_services["result_store"].get_request.return_value
        assert request.status == AnalysisStatus.COMPLETED
    
    @pytest.mark.asyncio
    async def test_process_request_with_retries_step_failure_with_fallback(self, orchestrator, mock_services):
        """Test processing when a step fails and uses fallback."""
        # Arrange
        request_id = self.TEST_REQUEST_ID
        
        # Create a mock for extract_text that always fails
        mock_extract_text = MagicMock(side_effect=Exception("Simulated failure"))
        
        # Replace the extract_text method with our mock
        orchestrator._extract_text = mock_extract_text
        
        # Create a mock for fallback that succeeds
        mock_fallback = MagicMock(return_value=(True, {
            "text_content": "Fallback text",
            "text_with_layout": {"text": "Fallback text", "blocks": []},
            "tables": [],
            "is_fallback": True
        }))
        
        # Replace the fallback method with our mock
        orchestrator._fallback_text_extraction = mock_fallback
        
        # Act
        result = await orchestrator.process_request_with_retries(request_id)
        
        # Assert
        assert result is True
        orchestrator._extract_text.assert_called()
        orchestrator._fallback_text_extraction.assert_called_once()
        
        # Verify the request status was updated to COMPLETED
        request = mock_services["result_store"].get_request.return_value
        assert request.status == AnalysisStatus.COMPLETED
    
    @pytest.mark.asyncio
    async def test_process_request_with_retries_all_steps_fail(self, orchestrator, mock_services):
        """Test processing when all steps fail including fallbacks."""
        # Arrange
        request_id = self.TEST_REQUEST_ID
        
        # Mock the validation step to fail without fallback
        # This will cause the process to fail immediately since validation has no fallback
        orchestrator._validate_request = MagicMock(return_value=(False, {"error": "Validation failed"}))
        
        # We don't need to mock other steps since validation will fail first
        
        # Act
        result = await orchestrator.process_request_with_retries(request_id)
        
        # Assert
        assert result is False
        
        # Verify the request status was updated to FAILED
        request = mock_services["result_store"].get_request.return_value
        assert request.status == AnalysisStatus.FAILED