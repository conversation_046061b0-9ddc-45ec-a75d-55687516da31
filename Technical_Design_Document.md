# Technical Design Document: RAG System for AWS Cloud Documentation

## 1. High-Level Architecture Overview

### 1.1. Architectural Pattern

The system is designed using a **Multi-tiered Architecture**, featuring a clear separation of concerns between the presentation layer (frontend), the application logic layer (backend), and the data layer (storage and external services). This pattern promotes modularity, scalability, and maintainability.

-   **Frontend:** A reactive, web-based UI built with Chainlit, responsible for user interaction and presenting data.
-   **Backend:** A stateless FastAPI application that serves as the central hub, orchestrating data processing, retrieval, and generation tasks.
-   **Data & Services Layer:** A collection of specialized components, including a Qdrant vector store for semantic search, AWS S3 for document storage, and large language models (LLMs) from AWS Bedrock and OpenRouter for text generation and analysis.

### 1.2. Primary Components

| Component | Responsibility |
| :--- | :--- |
| **FastAPI Backend** | Exposes a RESTful API for document ingestion, querying, and image analysis. It orchestrates the entire RAG pipeline and integrates with various services. |
| **Chainlit Frontend** | Provides an interactive chat interface for users to query the system, upload images, and view results, including source citations and diagrams. |
| **Document Ingestion (`ingest.py`)** | Fetches documents from an AWS S3 bucket, processes them using `unstructured`, extracts text and images, chunks the text, and stores the embeddings in the vector store. |
| **Query Engine (`query.py`)** | Manages the end-to-end process of handling user queries, including advanced retrieval, prompt engineering, LLM interaction, and response parsing. |
| **Image Analysis (`image_analysis/`)** | A dedicated sub-module for processing and analyzing uploaded images. It can classify images, perform OCR, and analyze both architecture diagrams and error screenshots. |
| **Qdrant Vector Store** | The primary data store for document embeddings. It enables efficient semantic search and retrieval of relevant document chunks. |
| **AWS S3** | The source for documents to be ingested into the RAG system. |
| **AWS Bedrock & OpenRouter** | Provide the LLMs for embedding generation, question answering, and advanced image analysis. |

### 1.3. Key Technologies & Dependencies

| Technology | Role |
| :--- | :--- |
| **Python 3.8+** | The primary programming language for the entire application. |
| **FastAPI** | A modern, high-performance web framework for building the backend API. |
| **Chainlit** | A framework for building conversational AI applications and providing the frontend UI. |
| **LangChain** | A comprehensive framework used for orchestrating the RAG pipeline, managing prompts, and interacting with LLMs and vector stores. |
| **Qdrant** | A vector database used for storing document embeddings and performing similarity searches. |
| **Boto3** | The AWS SDK for Python, used for interacting with S3 and Bedrock. |
| **Unstructured.io** | A library for parsing various document formats (PDF, DOCX) and extracting text and images. |
| **Pydantic** | Used for data validation and settings management throughout the application. |
| **PyTesseract & OpenCV** | Used for Optical Character Recognition (OCR) and image processing within the image analysis module. |
| **Uvicorn** | An ASGI server used to run the FastAPI application. |

## 2. Detailed Module & File Analysis

### 2.1. `Backend/` Module

This directory contains the core application logic for the RAG system.

-   **`main.py`**: The entry point for the FastAPI application.
    -   **`app`**: The main `FastAPI` instance.
    -   **`ingest_documents()`**: API endpoint (`POST /ingest`) to trigger the document ingestion process.
    -   **`advanced_query_endpoint()`**: API endpoint (`POST /query/advanced`) to handle user queries with advanced retrieval configurations.
    -   **`image_query_endpoint()`**: API endpoint (`POST /query/image`) to handle queries based on text extracted from an uploaded image.
    -   **`image_analysis_endpoint()`**: API endpoint (`POST /analyze/image`) for detailed analysis of an uploaded image using AWS Bedrock.
    -   **`openrouter_image_analysis_endpoint()`**: API endpoint (`POST /analyze/image/openrouter`) for image analysis using OpenRouter.

-   **`ingest.py`**: Manages the document ingestion pipeline.
    -   **`DocumentIngester` class**:
        -   `process_s3_documents()`: Lists files in the configured S3 bucket, reads and parses them, extracts text and images, chunks the text, generates embeddings, and stores them in the Qdrant vector store.

-   **`query.py`**: The core query engine.
    -   **`QueryEngine` class**:
        -   `query_advanced()`: Orchestrates the query process, including selecting a retriever, fetching documents, compressing context, selecting and formatting a prompt, invoking the LLM, and parsing the response.
        -   `get_architecture_diagrams()`: A specialized function to retrieve image documents that are likely to be architecture diagrams.

-   **`aws_utils.py`**: Utilities for interacting with AWS.
    -   **`AWSClient` class**:
        -   `read_s3_file()`: Reads a file from S3 and uses `unstructured` or other parsers to extract text and images.
    -   **`extract_text_from_image_bytes()`**: Extracts text from an image using Tesseract OCR.
    -   **`analyze_image_with_bedrock()`**: Analyzes an image using the AWS Bedrock vision model.

-   **`prompts.py`**: Manages prompt engineering.
    -   **`AWSPromptFactory` class**: A factory for creating specialized `ChatPromptTemplate` instances based on the type of query (e.g., general, comparison, troubleshooting).
    -   **`SmartQueryClassifier` class**: Classifies user queries to select the most appropriate prompt template.

-   **`advanced_retrieval.py`**: Implements advanced document retrieval strategies.
    -   **`ConfigurableRetriever` class**: A highly configurable retriever that can use a combination of semantic search, keyword search (BM25), Maximal Marginal Relevance (MMR), and query expansion.

-   **`vectorstore_utils.py`**: Manages the Qdrant vector store.
    -   **`QdrantVectorStoreWrapper` class**: A LangChain-compatible wrapper for the Qdrant client.
    -   **`build_vectorstore()`**: Creates and populates the Qdrant collection.
    -   **`load_vectorstore()`**: Loads the existing Qdrant vector store.

### 2.2. `Frontend/` Module

This directory contains the user interface for the application.

-   **`chainlit_app.py`**: The main Chainlit application.
    -   **`@cl.on_chat_start`**: Initializes the chat session, checks API health, and displays a welcome message.
    -   **`@cl.on_message`**: Handles incoming user messages, routes commands (e.g., `/help`, `/diagrams`), and calls the backend API for queries.
    -   **`handle_query()`**: Manages the interaction with the backend for a user query and displays the formatted response, sources, and images.
    -   **`display_images()`**: Renders images in the chat interface.

### 2.3. `Backend/image_analysis/` Sub-module

A self-contained module for all image-related processing and analysis.

-   **`api.py`**: Defines the FastAPI router and endpoints for this sub-module.
-   **`processor.py`**:
    -   **`ImageProcessor` class**: The central orchestrator for the image analysis pipeline. It classifies the image type and then delegates to the appropriate service for detailed analysis.
-   **`classifier.py`**:
    -   **`ImageTypeClassifier` class**: Classifies an image as an architecture diagram, an error screenshot, or unknown, based on a weighted analysis of various features (text density, color diversity, edge density, keywords, etc.).
-   **`models.py`**: Contains the Pydantic data models for image analysis requests, results, and components.
-   **`ocr.py`**:
    -   **`OCRService` class**: A wrapper around Tesseract for extracting text from images, with support for various preprocessing steps.
-   **`error_recognition.py`**:
    -   **`ErrorRecognitionService` class**: Identifies and classifies error messages in text using a predefined set of regex patterns.
-   **`diagram_text.py`**:
    -   **`DiagramTextRecognitionService` class**: Extracts and categorizes text elements from diagrams into labels, titles, and annotations.
-   **`diagram_components.py`**:
    -   **`DiagramComponentRecognitionService` class**: Detects shapes (boxes, circles, etc.) and lines in diagrams and associates them with text labels.
-   **`storage.py`**:
    -   **`TemporaryImageStore` class**: Manages the temporary storage of uploaded images, with optional encryption.
-   **`validators.py`**:
    -   **`ImageValidator` class**: Provides a set of validation rules for uploaded images, checking file type, size, and content to ensure they are safe and valid.

## 3. Control Flow and Workflow Tracing

### 3.1. Workflow 1: Document Ingestion

This workflow describes the process of ingesting documents from S3 into the vector store.

1.  **Trigger**: The `/ingest` API endpoint is called.
2.  **List Files**: `DocumentIngester` lists all files in the configured S3 bucket.
3.  **Process Files**: For each file, `AWSClient` reads the content.
4.  **Parse Content**: Based on the file type, the content is parsed using `unstructured` (for PDF/DOCX) or a plain text parser. Both text and images are extracted.
5.  **Create Documents**: LangChain `Document` objects are created for the extracted text and for each image.
6.  **Chunk Text**: The text documents are split into smaller chunks using a semantic text splitter.
7.  **Generate Embeddings**: `BedrockEmbeddings` is used to generate a vector embedding for each chunk.
8.  **Store in Vector Store**: The chunks and their embeddings are stored in the Qdrant vector store via `build_vectorstore`.

```mermaid
sequenceDiagram
    participant User
    participant FastAPI
    participant DocumentIngester
    participant AWSClient
    participant Qdrant

    User->>FastAPI: POST /ingest
    FastAPI->>DocumentIngester: process_s3_documents()
    DocumentIngester->>AWSClient: list_s3_files()
    AWSClient-->>DocumentIngester: List of files
    loop For each file
        DocumentIngester->>AWSClient: read_s3_file()
        AWSClient-->>DocumentIngester: Parsed text and images
    end
    DocumentIngester->>DocumentIngester: Chunk documents
    DocumentIngester->>Qdrant: build_vectorstore(chunks)
    Qdrant-->>DocumentIngester: Success
    DocumentIngester-->>FastAPI: Ingestion complete
    FastAPI-->>User: 200 OK
```

### 3.2. Workflow 2: Advanced Query Processing

This workflow describes how a user query is processed to generate a response.

1.  **Trigger**: The user sends a message to the Chainlit frontend.
2.  **API Call**: `chainlit_app.py` calls the `/query/advanced` API endpoint on the backend.
3.  **Get Query Engine**: The `QueryEngine` is initialized.
4.  **Retrieve Documents**: `ConfigurableRetriever` retrieves relevant document chunks from Qdrant using a hybrid search strategy (semantic + keyword).
5.  **Compress Context**: The retrieved documents are compressed to keep only the most relevant parts.
6.  **Select Prompt**: `SmartQueryClassifier` classifies the query, and `AWSPromptFactory` selects the appropriate prompt template.
7.  **Invoke LLM**: The formatted prompt (containing the user's question and the retrieved context) is sent to the AWS Bedrock LLM.
8.  **Parse Response**: The LLM's response is parsed by `AWSResponseParser` to extract the answer, key findings, and other structured data.
9.  **Return Response**: The final, structured response is returned to the Chainlit frontend.
10. **Display Response**: The frontend displays the formatted answer, sources, and any extracted images.

```mermaid
sequenceDiagram
    participant User
    participant Chainlit
    participant FastAPI
    participant QueryEngine
    participant Qdrant
    participant BedrockLLM

    User->>Chainlit: Sends query
    Chainlit->>FastAPI: POST /query/advanced
    FastAPI->>QueryEngine: query_advanced(question)
    QueryEngine->>Qdrant: Retrieve relevant documents
    Qdrant-->>QueryEngine: Document chunks
    QueryEngine->>QueryEngine: Compress context
    QueryEngine->>QueryEngine: Select prompt template
    QueryEngine->>BedrockLLM: Invoke with prompt
    BedrockLLM-->>QueryEngine: LLM response
    QueryEngine->>QueryEngine: Parse response
    QueryEngine-->>FastAPI: Formatted response
    FastAPI-->>Chainlit: JSON response
    Chainlit->>User: Display answer, sources, and images
```

## 4. Data Model and State Management

### 4.1. Primary Data Structures

The application heavily relies on **Pydantic models** for data validation and structuring. Key models include:

-   **`ImageAnalysisRequest`**: Represents a request to analyze an image, tracking its status and metadata.
-   **`ImageAnalysisResult`**: Stores the detailed results of an image analysis, including the classified type, extracted text, and a structured `Analysis` object.
-   **`Analysis`**: A nested model containing a summary, detailed findings, and recommendations for an analyzed image.
-   **`AWSResponse`**: A structured model for the final answer from the RAG system, including the answer text, confidence, key findings, and mentioned AWS services.

### 4.2. Vector Store Schema

The data in the **Qdrant vector store** is structured as follows for each document chunk:

-   **`id`**: A unique UUID for the chunk.
-   **`vector`**: The 1024-dimensional embedding generated by AWS Bedrock.
-   **`payload`**: A JSON object containing the metadata:
    -   **`content`**: The raw text of the chunk.
    -   **`source`**: The original filename of the document.
    -   **`file_type`**: The type of the source file (e.g., `pdf`, `docx`).
    -   **`file_path`**: The full key of the file in the S3 bucket.
    -   **`is_image`**: A boolean flag indicating if the document represents an image.
    -   (For images) **`base64_image`**, **`image_caption`**, etc.

### 4.3. State Management

-   **Backend**: The FastAPI backend is designed to be **stateless**. Each API request is independent and self-contained. The state of the knowledge base is persisted in the Qdrant vector store.
-   **Frontend**: The Chainlit frontend manages user session state using its built-in `cl.user_session`. This is used to store session-specific data, such as the session ID and token usage statistics, for the duration of a user's interaction.

## 5. Dependency and Call Graph

### 5.1. Internal Dependencies

-   **`Frontend`** depends on **`Backend`** (via HTTP API calls).
-   **`Backend`** depends on:
    -   `ingest.py`
    -   `query.py`
    -   `aws_utils.py`
    -   `vectorstore_utils.py`
    -   `image_analysis/` (via its API router).
-   **`query.py`** depends on:
    -   `vectorstore_utils.py`
    -   `prompts.py`
    -   `advanced_retrieval.py`
    -   `token_utils.py`.
-   **`image_analysis/`** sub-module:
    -   `processor.py` is the central orchestrator, depending on `classifier.py`, `ocr.py`, `error_recognition.py`, and `diagram_components.py`.
    -   The other files in the sub-module are largely independent services.

### 5.2. Call Graphs for Critical Functions

#### `QueryEngine.query_advanced()`

This function is the heart of the query processing pipeline.

```mermaid
graph TD
    A[query_advanced] --> B{validate_prompt_inputs};
    A --> C{ConfigurableRetriever.retrieve};
    C --> D[VectorStore.similarity_search];
    C --> E[AWSBM25Retriever.get_relevant_documents];
    A --> F{compress_document};
    A --> G{AWSPromptSelector.select_template};
    A --> H{ChatBedrock.invoke};
    A --> I{AWSResponseParser.parse};
    A --> J{extract_aws_services};
    A --> K{get_architecture_diagrams};
```

#### `ImageProcessor.process_image()`

This function orchestrates the image analysis workflow.

```mermaid
graph TD
    A[process_image] --> B{extract_text};
    B --> C[OCRService.extract_text];
    A --> D{ImageTypeClassifier.classify_image};
    A --> E{analyze_image};
    E --> F{_analyze_architecture};
    F --> G[DiagramComponentRecognitionService.recognize_components];
    E --> H{_analyze_error};
    H --> I[ErrorRecognitionService.identify_errors];