# RAG System Architecture Diagram

## Overview

This directory contains a detailed architectural workflow diagram for the RAG (Retrieval Augmented Generation) system. The diagram illustrates all services, their interconnections, data flows, and dependencies in an AWS-style format with high-resolution icons.

## Diagram Contents

The architecture diagram (`rag_architecture_detailed.xml`) provides a comprehensive visualization of the system with three main sections:

1. **Document Ingestion Pipeline**
   - Document upload flow from user to S3 bucket
   - Document processing with Unstructured.io
   - Text chunking with LangChain
   - Embedding generation with AWS Bedrock
   - Vector storage in Qdrant
   - Document metadata handling

2. **Query Processing Pipeline**
   - User query flow through Chainlit frontend
   - API request handling by FastAPI backend
   - Advanced retrieval with hybrid search
   - Vector search in Qdrant
   - Context preparation with prompt templates
   - Response generation with AWS Bedrock LLM
   - Result formatting and display

3. **Deployment Architecture**
   - Systemd service configuration
   - Service dependencies
   - File locations and directory structure
   - Log management
   - Vector store persistence

## How to View the Diagram

The architecture diagram is provided in draw.io XML format. To view and edit the diagram:

1. **Option 1: Using draw.io website**
   - Visit [draw.io](https://app.diagrams.net/)
   - Click on "Open Existing Diagram"
   - Select "Open from Device"
   - Navigate to and open `rag_architecture_detailed.xml`

2. **Option 2: Using VS Code with draw.io extension**
   - Install the "Draw.io Integration" extension in VS Code
   - Open the `rag_architecture_detailed.xml` file
   - The diagram will render in the editor

3. **Option 3: Using desktop draw.io application**
   - Download and install draw.io desktop from [diagrams.net](https://www.diagrams.net/)
   - Open the application and select "Open Existing Diagram"
   - Navigate to and open `rag_architecture_detailed.xml`

## Diagram Components

The diagram uses color-coding to distinguish different system aspects:

- **Yellow components**: Document ingestion pipeline
- **Blue components**: Query processing pipeline
- **Green components**: Deployment architecture
- **AWS-style icons**: Represent different service types and functions

## System Architecture Summary

This RAG system architecture consists of:

- **Frontend**: Chainlit-based interactive chat interface
- **Backend**: FastAPI service for query processing and document ingestion
- **Storage**: Qdrant vector database for document embeddings
- **AI Services**: AWS Bedrock for embeddings and LLM capabilities
- **Document Processing**: Unstructured.io for parsing various document formats
- **Deployment**: Systemd services for reliable operation

The system follows a typical RAG pattern with separate pipelines for document ingestion and query processing, both leveraging the same vector store for knowledge retrieval.