#!/usr/bin/env python3
"""
Test Nova Lite with the correct inference profile format.
"""

import os
import sys
import json
import boto3
import base64
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
from dotenv import load_dotenv

def create_test_image():
    """Create a simple test image with text for testing."""
    img = Image.new('RGB', (400, 300), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        font = ImageFont.load_default()
    
    draw.text((50, 50), "AWS Architecture Diagram", fill='black', font=font)
    draw.rectangle([50, 100, 150, 150], outline='blue', width=2)
    draw.text((60, 115), "EC2", fill='blue', font=font)
    
    draw.rectangle([200, 100, 300, 150], outline='green', width=2)
    draw.text((210, 115), "RDS", fill='green', font=font)
    
    draw.line([150, 125, 200, 125], fill='red', width=3)
    draw.polygon([(195, 120), (200, 125), (195, 130)], fill='red')
    
    draw.text((50, 200), "Test Architecture", fill='black', font=font)
    
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def test_nova_with_inference_profile():
    """Test Nova Lite using the inference profile ARN."""
    load_dotenv()
    
    aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
    aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
    aws_region = os.getenv('AWS_REGION')
    
    if not aws_access_key or not aws_secret_key or not aws_region:
        print("❌ AWS credentials not configured")
        return
    
    print("Testing Nova Lite with inference profile...")
    print("=" * 60)
    
    try:
        # Create test image
        image_bytes = create_test_image()
        base64_image = base64.b64encode(image_bytes).decode('utf-8')
        
        # Create Bedrock client
        bedrock_runtime = boto3.client(
            'bedrock-runtime',
            region_name=aws_region,
            aws_access_key_id=aws_access_key,
            aws_secret_access_key=aws_secret_key
        )
        
        # Try different inference profile formats
        inference_profiles_to_try = [
            f"arn:aws:bedrock:{aws_region}:337909778990:inference-profile/amazon.nova-lite-v1:0",
            "amazon.nova-lite-v1:0",
            f"us.amazon.nova-lite-v1:0",
            f"apac.amazon.nova-lite-v1:0"
        ]
        
        for profile_arn in inference_profiles_to_try:
            print(f"\nTrying inference profile: {profile_arn}")
            
            try:
                # Nova format payload
                payload = {
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {"text": "Please analyze this image and describe what you see."},
                                {
                                    "image": {
                                        "format": "png",
                                        "source": {"bytes": base64_image}
                                    }
                                }
                            ]
                        }
                    ],
                    "inferenceConfig": {
                        "maxTokens": 500,
                        "temperature": 0.2
                    }
                }
                
                response = bedrock_runtime.invoke_model(
                    modelId=profile_arn,
                    body=json.dumps(payload)
                )
                
                response_body = json.loads(response['body'].read())
                print(f"✅ SUCCESS with {profile_arn}")
                print(f"Response: {response_body}")
                
                # Try to extract the text
                try:
                    analysis = response_body['output']['message']['content'][0]['text']
                    print(f"Analysis: {analysis[:200]}...")
                    return True, profile_arn, analysis
                except (KeyError, IndexError) as e:
                    print(f"⚠️ Success but couldn't parse response: {e}")
                    print(f"Raw response: {response_body}")
                    return True, profile_arn, str(response_body)
                
            except Exception as e:
                print(f"❌ Failed with {profile_arn}: {e}")
                continue
        
        print("\n❌ All inference profiles failed")
        return False, None, None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, None, None

def main():
    """Main test function."""
    print("NOVA LITE INFERENCE PROFILE TEST")
    print("=" * 60)
    
    success, working_profile, analysis = test_nova_with_inference_profile()
    
    if success:
        print(f"\n✅ WORKING INFERENCE PROFILE: {working_profile}")
        print(f"Analysis: {analysis}")
        
        # Update the .env file with the working profile
        print(f"\n🔧 Update your .env file with:")
        print(f"BEDROCK_VISION_MODEL_ID={working_profile}")
    else:
        print("\n❌ No working inference profiles found")
        print("🔧 Recommendations:")
        print("   • Check if you have access to Nova Lite in your AWS account")
        print("   • Try using OpenRouter as the primary image analysis service")
        print("   • Contact AWS support to enable Nova Lite access")

if __name__ == "__main__":
    main()
