<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="800px" height="600px" viewBox="-0.5 -0.5 800 600">
  <defs/>
  <g>
    <!-- Title -->
    <rect x="0" y="0" width="800" height="40" fill="none" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 798px; height: 1px; padding-top: 20px; margin-left: 1px;">
            <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">RAG System Architecture</div>
            </div>
          </div>
        </foreignObject>
        <text x="400" y="27" fill="#232F3E" font-family="Helvetica" font-size="24px" text-anchor="middle" font-weight="bold">RAG System Architecture</text>
      </switch>
    </g>
    
    <!-- Document Ingestion Section -->
    <rect x="40" y="60" width="720" height="200" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 718px; height: 1px; padding-top: 67px; margin-left: 41px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Document Ingestion Pipeline</div>
            </div>
          </div>
        </foreignObject>
        <text x="400" y="83" fill="#000000" font-family="Helvetica" font-size="16px" text-anchor="middle" font-weight="bold">Document Ingestion Pipeline</text>
      </switch>
    </g>
    
    <!-- User Upload -->
    <ellipse cx="100" cy="150" rx="30" ry="30" fill="#232F3D" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 190px; margin-left: 71px;">
            <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">User</div>
            </div>
          </div>
        </foreignObject>
        <text x="100" y="190" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">User</text>
      </switch>
    </g>
    
    <!-- S3 Bucket -->
    <rect x="180" y="120" width="60" height="60" fill="#277116" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 190px; margin-left: 181px;">
            <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">S3 Bucket</div>
            </div>
          </div>
        </foreignObject>
        <text x="210" y="190" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">S3 Bucket</text>
      </switch>
    </g>
    
    <!-- Document Processor -->
    <rect x="290" y="120" width="60" height="60" fill="#D05C17" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 190px; margin-left: 291px;">
            <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Unstructured.io</div>
            </div>
          </div>
        </foreignObject>
        <text x="320" y="190" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">Unstructur...</text>
      </switch>
    </g>
    
    <!-- Bedrock Embedding -->
    <rect x="400" y="120" width="60" height="60" fill="#116D5B" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 190px; margin-left: 401px;">
            <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Bedrock Embedding</div>
            </div>
          </div>
        </foreignObject>
        <text x="430" y="190" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">Bedrock Em...</text>
      </switch>
    </g>
    
    <!-- Qdrant Vector Store -->
    <rect x="510" y="120" width="60" height="60" fill="#3334B9" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 190px; margin-left: 511px;">
            <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Qdrant Vector Store</div>
            </div>
          </div>
        </foreignObject>
        <text x="540" y="190" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">Qdrant Vec...</text>
      </switch>
    </g>
    
    <!-- Document Ingestion Flow -->
    <path d="M 130 150 L 180 150" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 240 150 L 290 150" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 350 150 L 400 150" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 460 150 L 510 150" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
    
    <!-- Query Processing Section -->
    <rect x="40" y="300" width="720" height="200" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 718px; height: 1px; padding-top: 307px; margin-left: 41px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Query Processing Pipeline</div>
            </div>
          </div>
        </foreignObject>
        <text x="400" y="323" fill="#000000" font-family="Helvetica" font-size="16px" text-anchor="middle" font-weight="bold">Query Processing Pipeline</text>
      </switch>
    </g>
    
    <!-- User Query -->
    <ellipse cx="100" cy="390" rx="30" ry="30" fill="#232F3D" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 430px; margin-left: 71px;">
            <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">User</div>
            </div>
          </div>
        </foreignObject>
        <text x="100" y="430" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">User</text>
      </switch>
    </g>
    
    <!-- Chainlit Frontend -->
    <rect x="180" y="360" width="60" height="60" fill="#5A30B5" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 430px; margin-left: 181px;">
            <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Chainlit Frontend</div>
            </div>
          </div>
        </foreignObject>
        <text x="210" y="430" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">Chainlit F...</text>
      </switch>
    </g>
    
    <!-- FastAPI Backend -->
    <rect x="290" y="360" width="60" height="60" fill="#D05C17" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 430px; margin-left: 291px;">
            <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FastAPI Backend</div>
            </div>
          </div>
        </foreignObject>
        <text x="320" y="430" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">FastAPI Ba...</text>
      </switch>
    </g>
    
    <!-- Advanced Retrieval -->
    <rect x="400" y="360" width="60" height="60" fill="#3334B9" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 430px; margin-left: 401px;">
            <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Advanced Retrieval</div>
            </div>
          </div>
        </foreignObject>
        <text x="430" y="430" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">Advanced R...</text>
      </switch>
    </g>
    
    <!-- Qdrant Vector Store (Query) -->
    <rect x="510" y="360" width="60" height="60" fill="#3334B9" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 430px; margin-left: 511px;">
            <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Qdrant Vector Store</div>
            </div>
          </div>
        </foreignObject>
        <text x="540" y="430" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">Qdrant Vec...</text>
      </switch>
    </g>
    
    <!-- Bedrock LLM -->
    <rect x="620" y="360" width="60" height="60" fill="#116D5B" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 430px; margin-left: 621px;">
            <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Bedrock LLM</div>
            </div>
          </div>
        </foreignObject>
        <text x="650" y="430" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">Bedrock LLM</text>
      </switch>
    </g>
    
    <!-- Query Flow -->
    <path d="M 130 390 L 180 390" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 240 390 L 290 390" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 350 390 L 400 390" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 460 390 L 510 390" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 570 390 L 620 390" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
    
    <!-- Response Flow -->
    <path d="M 620 420 L 130 420" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 420px; margin-left: 375px;">
            <div data-drawio-colors="color: #000000; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">Response Flow</div>
            </div>
          </div>
        </foreignObject>
        <text x="375" y="423" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">Response Flow</text>
      </switch>
    </g>
    
    <!-- Deployment Section -->
    <rect x="40" y="540" width="720" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 718px; height: 1px; padding-top: 560px; margin-left: 41px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Deployment: Systemd Services (rag-api.service, rag-frontend.service)</div>
            </div>
          </div>
        </foreignObject>
        <text x="400" y="565" fill="#000000" font-family="Helvetica" font-size="16px" text-anchor="middle" font-weight="bold">Deployment: Systemd Services (rag-api.service, rag-frontend.service)</text>
      </switch>
    </g>
    
    <!-- Vector Store Connection -->
    <path d="M 540 180 L 540 360" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 270px; margin-left: 540px;">
            <div data-drawio-colors="color: #000000; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">Shared Vector Store</div>
            </div>
          </div>
        </foreignObject>
        <text x="540" y="273" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">Shared Vector Store</text>
      </switch>
    </g>
  </g>
</svg>