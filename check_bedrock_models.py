#!/usr/bin/env python3
"""
Check available AWS Bedrock models in the current region.
"""

import boto3
import os
import sys
from dotenv import load_dotenv

def check_bedrock_models():
    """Check what Bedrock models are available in the current region."""
    load_dotenv()
    
    # Get AWS credentials
    aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
    aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
    aws_region = os.getenv('AWS_REGION')
    
    if not aws_access_key or not aws_secret_key or not aws_region:
        print("❌ AWS credentials not configured")
        return
    
    print(f"Checking Bedrock models in region: {aws_region}")
    print("=" * 60)
    
    try:
        # Create Bedrock client
        bedrock = boto3.client(
            'bedrock',
            region_name=aws_region,
            aws_access_key_id=aws_access_key,
            aws_secret_access_key=aws_secret_key
        )
        
        # List foundation models
        response = bedrock.list_foundation_models()
        
        print("Available Foundation Models:")
        print("-" * 40)
        
        vision_models = []
        text_models = []
        
        for model in response['modelSummaries']:
            model_id = model['modelId']
            model_name = model['modelName']
            provider = model['providerName']
            input_modalities = model.get('inputModalities', [])
            output_modalities = model.get('outputModalities', [])
            
            # Check if it's a vision model (accepts images)
            if 'IMAGE' in input_modalities:
                vision_models.append({
                    'id': model_id,
                    'name': model_name,
                    'provider': provider,
                    'input': input_modalities,
                    'output': output_modalities
                })
            else:
                text_models.append({
                    'id': model_id,
                    'name': model_name,
                    'provider': provider,
                    'input': input_modalities,
                    'output': output_modalities
                })
        
        print(f"\n🖼️  VISION MODELS (Accept Images): {len(vision_models)}")
        print("-" * 40)
        for model in vision_models:
            print(f"  📷 {model['id']}")
            print(f"     Name: {model['name']}")
            print(f"     Provider: {model['provider']}")
            print(f"     Input: {', '.join(model['input'])}")
            print(f"     Output: {', '.join(model['output'])}")
            print()
        
        print(f"\n📝 TEXT-ONLY MODELS: {len(text_models)}")
        print("-" * 40)
        for model in text_models[:10]:  # Show first 10 only
            print(f"  📝 {model['id']}")
            print(f"     Name: {model['name']}")
            print(f"     Provider: {model['provider']}")
            print()
        
        if len(text_models) > 10:
            print(f"     ... and {len(text_models) - 10} more text models")
        
        # Check specific models mentioned in the code
        print("\n🔍 CHECKING SPECIFIC MODELS FROM CODE:")
        print("-" * 40)
        
        models_to_check = [
            'anthropic.claude-3-sonnet-20240229-v1:0',
            'anthropic.claude-3-haiku-20240307-v1:0',
            'meta.llama3-8b-vision-v1:0',
            'apac.amazon.nova-lite-v1:0',
            'amazon.nova-lite-v1:0'
        ]
        
        available_model_ids = [m['modelId'] for m in response['modelSummaries']]
        
        for model_id in models_to_check:
            if model_id in available_model_ids:
                model_info = next(m for m in response['modelSummaries'] if m['modelId'] == model_id)
                input_modalities = model_info.get('inputModalities', [])
                is_vision = 'IMAGE' in input_modalities
                status = "✅ Available" + (" (Vision)" if is_vision else " (Text-only)")
            else:
                status = "❌ Not Available"
            
            print(f"  {model_id}: {status}")
        
        return vision_models
        
    except Exception as e:
        print(f"❌ Error checking Bedrock models: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_specific_model(model_id):
    """Test if we can access a specific model."""
    load_dotenv()
    
    aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
    aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
    aws_region = os.getenv('AWS_REGION')
    
    try:
        bedrock_runtime = boto3.client(
            'bedrock-runtime',
            region_name=aws_region,
            aws_access_key_id=aws_access_key,
            aws_secret_access_key=aws_secret_key
        )
        
        # Try a simple text-only request to test access
        if 'anthropic' in model_id:
            payload = {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 10,
                "messages": [{"role": "user", "content": "Hello"}]
            }
        else:
            payload = {
                "prompt": "Hello",
                "max_gen_len": 10
            }
        
        response = bedrock_runtime.invoke_model(
            modelId=model_id,
            body=json.dumps(payload)
        )
        
        return True, "Access granted"
        
    except Exception as e:
        return False, str(e)

if __name__ == "__main__":
    vision_models = check_bedrock_models()
    
    print("\n" + "=" * 60)
    print("RECOMMENDATIONS:")
    print("=" * 60)
    
    if vision_models:
        print("✅ Vision models are available in your region!")
        print("🔧 Recommended vision model to use:")
        for model in vision_models[:3]:  # Show top 3
            print(f"   • {model['id']}")
    else:
        print("❌ No vision models available in your region")
        print("🔧 Consider:")
        print("   • Switching to a different AWS region")
        print("   • Requesting access to vision models")
        print("   • Using OpenRouter as the primary image analysis service")
