# Image Generation and Analysis Feature - Debugging and Resolution Plan

This document outlines the steps to diagnose and fix the critical failure in the image and diagram generation/analysis functionality.

## Phase 1: Root Cause Analysis & Information Gathering

- [ ] **1.1. Confirm Technology Stack:** Verify Python and Boto3 versions.
- [ ] **1.2. Analyze Existing Code for Image Analysis:**
    - [ ] Review `Backend/main.py` to understand the API endpoints for image analysis.
    - [ ] Deeply analyze `Backend/aws_utils.py`, specifically the `analyze_image_with_bedrock` function, to understand the exact logic for model selection and payload construction.
- [ ] **1.3. Identify Correct Bedrock Model:** Research and identify the correct AWS Bedrock model ID for multi-modal image *analysis* (e.g., Claude 3 Sonnet/Haiku, not Titan Image Generator). The user mentioned "nova lite", but this seems to be a text model. I will clarify this.
- [ ] **1.4. Gather Missing Information (Simulated):** Since I cannot get direct logs, I will proceed based on the high probability of a model mismatch. I will ask the user to confirm the error messages they are seeing, which likely relate to `ValidationException` or similar errors from Bedrock.

## Phase 2: Implementation of the Fix

- [ ] **2.1. Refactor `Backend/aws_utils.py`:**
    - [ ] Modify `analyze_image_with_bedrock` to use the correct model ID for image analysis (e.g., `anthropic.claude-3-sonnet-20240229-v1:0`).
    - [ ] Ensure the payload sent to `invoke_model` matches the requirements for the selected vision model. The current code has logic for different providers (Anthropic, Meta), which is good, but the model selection logic is flawed.
    - [ ] Add robust error handling to catch `ClientError` from `boto3` and provide more descriptive error messages.
    - [ ] Improve logging to include the `model_id` being used in every request.
- [ ] **2.2. Create a new endpoint for Image Generation:** The original problem description mentions "image and diagram generation". The current endpoints are for analysis. I will add a new endpoint `/generate/diagram` that takes a text prompt and uses the `amazon.titan-image-generator-v1` model to generate an image.
    - [ ] Create a new function in `Backend/aws_utils.py` called `generate_image_with_bedrock`.
    - [ ] Implement the `/generate/diagram` endpoint in `Backend/main.py`.
- [ ] **2.3. Update `requirements.txt`:** Ensure all necessary dependencies are listed and pinned to stable versions.

## Phase 3: Verification and Documentation

- [ ] **3.1. Create a Verification Plan:**
    - [ ] Write a `curl` command to test the fixed `/analyze/image` endpoint with a sample image.
    - [ ] Write a `curl` command to test the new `/generate/diagram` endpoint with a sample text prompt.
- [ ] **3.2. Update Documentation:**
    - [ ] Create a `README.md` section explaining the new image generation endpoint and the corrected analysis endpoint.
    - [ ] Provide a clear, step-by-step guide for deploying the fix.
- [ ] **3.3. Final Review:** Review all changes for correctness, security, and adherence to best practices.