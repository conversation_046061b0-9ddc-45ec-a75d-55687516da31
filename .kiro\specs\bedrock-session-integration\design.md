# Design Document: AWS Bedrock Session Integration with Supabase

## Overview

This design outlines the integration of AWS Bedrock Session Management APIs and Supabase into our existing RAG application. The goal is to enhance our session management capabilities by leveraging AWS Bedrock's managed session services for AI conversations while using Supabase as a more scalable and robust database backend compared to our current SQLite implementation.

The design follows a layered architecture with abstraction interfaces that allow for graceful fallbacks between different session management implementations. This ensures the application remains functional even when external services are unavailable.

## Architecture

### High-Level Architecture

```mermaid
graph TD
    Client[Client Application] --> API[FastAPI Backend]
    API --> SM[Session Manager]
    SM --> BSM[Bedrock Session Manager]
    SM --> SSM[Supabase Session Manager]
    SM --> LSM[Local Session Manager]
    BSM --> AWS[AWS Bedrock]
    SSM --> Supabase[Supabase Database]
    LSM --> SQLite[SQLite Database]
```

The architecture follows a provider pattern with a unified interface for session management. The system will attempt to use AWS Bedrock as the primary session manager, with Supa<PERSON> as the persistent storage layer, and SQLite as the fallback option.

### Session Management Flow

```mermaid
sequenceDiagram
    participant Client
    participant API as FastAPI Backend
    participant SM as Session Manager
    participant BSM as Bedrock Session Manager
    participant SSM as Supabase Session Manager
    participant LSM as Local Session Manager
    
    Client->>API: Send message
    API->>SM: Process message
    
    alt AWS Bedrock Available
        SM->>BSM: Create/Update session
        BSM->>AWS: API Call
        AWS-->>BSM: Response
        BSM-->>SM: Session data
        SM->>SSM: Sync session data
    else AWS Bedrock Unavailable
        SM->>SSM: Create/Update session
        SSM->>Supabase: Database operation
        Supabase-->>SSM: Response
        SSM-->>SM: Session data
    else Supabase Unavailable
        SM->>LSM: Create/Update session
        LSM->>SQLite: Database operation
        SQLite-->>LSM: Response
        LSM-->>SM: Session data
    end
    
    SM-->>API: Updated session
    API-->>Client: Response
```

## Components and Interfaces

### 1. Session Manager Interface

The core interface that all session manager implementations will adhere to:

```python
class SessionManagerInterface:
    def create_user(self, username: str, email: str, password: str) -> Optional[User]:
        """Create a new user"""
        pass
        
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate user and return user object"""
        pass
        
    def create_chat_session(self, user_id: str, title: str = None) -> ChatSession:
        """Create a new chat session"""
        pass
        
    def get_user_sessions(self, user_id: str, limit: int = 50) -> List[ChatSession]:
        """Get all chat sessions for a user"""
        pass
        
    def add_message(self, session_id: str, user_id: str, role: str, content: str, 
                   metadata: Optional[Dict[str, Any]] = None, 
                   token_usage: Optional[Dict[str, Any]] = None) -> ChatMessage:
        """Add a message to a chat session"""
        pass
        
    def get_session_messages(self, session_id: str, limit: int = 100) -> List[ChatMessage]:
        """Get all messages for a chat session"""
        pass
        
    # Additional methods...
```

### 2. Bedrock Session Manager

Implements the SessionManagerInterface using AWS Bedrock Session Management APIs:

```python
class BedrockSessionManager(SessionManagerInterface):
    def __init__(self, config: Dict[str, Any], fallback_manager: SessionManagerInterface = None):
        """Initialize with configuration and optional fallback manager"""
        self.bedrock_client = boto3.client('bedrock-runtime')
        self.fallback_manager = fallback_manager
        self.config = config
        
    def create_chat_session(self, user_id: str, title: str = None) -> ChatSession:
        """Create a new chat session in Bedrock"""
        try:
            # Create Bedrock session
            response = self.bedrock_client.create_session(
                modelId=self.config['model_id'],
                sessionConfiguration={
                    'timeToLiveInSeconds': self.config['ttl_seconds']
                }
            )
            bedrock_session_id = response['sessionId']
            
            # Create local record with mapping to Bedrock session
            if self.fallback_manager:
                local_session = self.fallback_manager.create_chat_session(user_id, title)
                # Store mapping between local session ID and Bedrock session ID
                self._store_session_mapping(local_session.session_id, bedrock_session_id)
                return local_session
                
        except Exception as e:
            logger.error(f"Failed to create Bedrock session: {e}")
            if self.fallback_manager:
                return self.fallback_manager.create_chat_session(user_id, title)
            raise
    
    # Additional methods...
```

### 3. Supabase Session Manager

Implements the SessionManagerInterface using Supabase:

```python
class SupabaseSessionManager(SessionManagerInterface):
    def __init__(self, url: str, key: str, fallback_manager: SessionManagerInterface = None):
        """Initialize with Supabase credentials and optional fallback manager"""
        self.supabase = create_client(url, key)
        self.fallback_manager = fallback_manager
        
    def create_user(self, username: str, email: str, password: str) -> Optional[User]:
        """Create a new user in Supabase"""
        try:
            user_id = str(uuid.uuid4())
            password_hash = self._hash_password(password)
            
            # Insert into Supabase
            data = {
                'user_id': user_id,
                'username': username,
                'email': email,
                'password_hash': password_hash,
                'created_at': datetime.now().isoformat(),
                'is_active': True
            }
            
            result = self.supabase.table('users').insert(data).execute()
            
            return User(
                user_id=user_id,
                username=username,
                email=email,
                password_hash=password_hash,
                created_at=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Failed to create user in Supabase: {e}")
            if self.fallback_manager:
                return self.fallback_manager.create_user(username, email, password)
            return None
    
    # Additional methods...
```

### 4. Composite Session Manager

The main session manager that orchestrates between different implementations:

```python
class CompositeSessionManager(SessionManagerInterface):
    def __init__(self, config: Dict[str, Any]):
        """Initialize with configuration"""
        self.config = config
        self.local_manager = SQLiteSessionManager(config.get('sqlite_path', 'chat_sessions.db'))
        
        # Initialize Supabase if configured
        self.supabase_manager = None
        if config.get('supabase_url') and config.get('supabase_key'):
            try:
                self.supabase_manager = SupabaseSessionManager(
                    config['supabase_url'], 
                    config['supabase_key'],
                    self.local_manager
                )
            except Exception as e:
                logger.error(f"Failed to initialize Supabase: {e}")
        
        # Initialize Bedrock if configured
        self.bedrock_manager = None
        if config.get('use_bedrock', False):
            try:
                self.bedrock_manager = BedrockSessionManager(
                    config.get('bedrock_config', {}),
                    self.supabase_manager or self.local_manager
                )
            except Exception as e:
                logger.error(f"Failed to initialize Bedrock: {e}")
    
    def get_primary_manager(self) -> SessionManagerInterface:
        """Get the highest priority available manager"""
        if self.bedrock_manager:
            return self.bedrock_manager
        if self.supabase_manager:
            return self.supabase_manager
        return self.local_manager
    
    # Delegate all interface methods to the primary manager
    def create_user(self, username: str, email: str, password: str) -> Optional[User]:
        return self.get_primary_manager().create_user(username, email, password)
    
    # Additional delegated methods...
```

## Data Models

### Session Mapping

To maintain the relationship between local sessions and AWS Bedrock sessions:

```python
@dataclass
class SessionMapping:
    local_session_id: str
    bedrock_session_id: str
    created_at: datetime
    updated_at: datetime
    is_active: bool = True
```

### Supabase Schema

The Supabase database will have the following tables:

1. `users` - User information
2. `chat_sessions` - Chat session metadata
3. `chat_messages` - Individual messages in chat sessions
4. `user_sessions` - Authentication tokens
5. `session_mappings` - Mapping between local and Bedrock sessions

## Error Handling

### Retry Mechanism

For transient errors with external services:

```python
def retry_with_backoff(max_retries=3, base_delay=1):
    """Decorator for retrying operations with exponential backoff"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    retries += 1
                    if retries >= max_retries:
                        raise
                    delay = base_delay * (2 ** (retries - 1))
                    logger.warning(f"Retry {retries}/{max_retries} after error: {e}. Waiting {delay}s")
                    time.sleep(delay)
        return wrapper
    return decorator
```

### Circuit Breaker

To prevent cascading failures when external services are down:

```python
class CircuitBreaker:
    def __init__(self, failure_threshold=5, reset_timeout=60):
        self.failure_count = 0
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        
    def execute(self, func, *args, **kwargs):
        if self.state == "OPEN":
            # Check if it's time to try again
            if time.time() - self.last_failure_time > self.reset_timeout:
                self.state = "HALF_OPEN"
            else:
                raise Exception("Circuit breaker is open")
                
        try:
            result = func(*args, **kwargs)
            if self.state == "HALF_OPEN":
                self.state = "CLOSED"
                self.failure_count = 0
            return result
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            if self.failure_count >= self.failure_threshold:
                self.state = "OPEN"
            raise e
```

## Testing Strategy

### Unit Tests

1. Test each session manager implementation independently
2. Mock external services (AWS Bedrock, Supabase)
3. Test fallback mechanisms
4. Test error handling and retry logic

### Integration Tests

1. Test with actual AWS Bedrock and Supabase services in a staging environment
2. Test failover scenarios
3. Test data synchronization between different storage layers
4. Test migration from SQLite to Supabase

### Performance Tests

1. Measure response times with different session managers
2. Test under load to ensure scalability
3. Compare performance between SQLite and Supabase implementations

## Migration Strategy

### SQLite to Supabase Migration

1. Create Supabase tables with compatible schema
2. Implement a migration utility:

```python
def migrate_sqlite_to_supabase(sqlite_path, supabase_client):
    """Migrate data from SQLite to Supabase"""
    with sqlite3.connect(sqlite_path) as conn:
        # Migrate users
        cursor = conn.execute("SELECT * FROM users")
        users = [dict(row) for row in cursor.fetchall()]
        for batch in chunks(users, 100):
            supabase_client.table('users').insert(batch).execute()
            
        # Migrate other tables...
```

## Configuration Management

Configuration will be managed through environment variables and an optional configuration file:

```python
def load_config():
    """Load configuration from environment variables and config file"""
    config = {
        # SQLite configuration
        'sqlite_path': os.getenv('SQLITE_PATH', 'chat_sessions.db'),
        
        # Supabase configuration
        'supabase_url': os.getenv('SUPABASE_URL'),
        'supabase_key': os.getenv('SUPABASE_KEY'),
        
        # Bedrock configuration
        'use_bedrock': os.getenv('USE_BEDROCK', 'false').lower() == 'true',
        'bedrock_config': {
            'model_id': os.getenv('BEDROCK_MODEL_ID', 'anthropic.claude-v2'),
            'ttl_seconds': int(os.getenv('BEDROCK_SESSION_TTL', '86400')),
            'max_session_size': int(os.getenv('BEDROCK_MAX_SESSION_SIZE', '100000')),
        }
    }
    
    # Load from config file if exists
    config_path = os.getenv('CONFIG_PATH')
    if config_path and os.path.exists(config_path):
        with open(config_path, 'r') as f:
            file_config = json.load(f)
            config.update(file_config)
            
    return config
```