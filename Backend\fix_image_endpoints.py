#!/usr/bin/env python3
"""
Quick fix script for critical image endpoint issues identified in testing.

This script applies the most important fixes to ensure proper error handling
and parameter passing in the image endpoints.
"""

import os
import sys
import shutil
from datetime import datetime

def backup_file(file_path):
    """Create a backup of the original file."""
    backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(file_path, backup_path)
    print(f"✓ Created backup: {backup_path}")
    return backup_path

def fix_main_py():
    """Apply fixes to main.py for proper error handling."""
    main_py_path = "main.py"
    
    if not os.path.exists(main_py_path):
        print(f"❌ {main_py_path} not found")
        return False
    
    # Create backup
    backup_path = backup_file(main_py_path)
    
    try:
        with open(main_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix 1: Update imports to include Form
        if "from fastapi import FastAPI, HTTPException, File, UploadFile, Depends" in content:
            content = content.replace(
                "from fastapi import FastAPI, HTTPException, File, UploadFile, Depends",
                "from fastapi import FastAPI, HTTPException, File, UploadFile, Depends, Form"
            )
            print("✓ Added Form import")
        
        # Fix 2: Update image query endpoint signature
        old_signature = """@app.post("/query/image")
async def image_query_endpoint(file: UploadFile = File(...), request: ImageQueryRequest = None):"""
        
        new_signature = """@app.post("/query/image")
async def image_query_endpoint(
    file: UploadFile = File(...), 
    retrieval_config: Optional[str] = Form(None)
):"""
        
        if old_signature in content:
            content = content.replace(old_signature, new_signature)
            print("✓ Updated image query endpoint signature")
        
        # Fix 3: Update exception handling in image_query_endpoint
        old_exception_block = """    except Exception as e:
        import traceback
        stack_trace = traceback.format_exc()
        logger.error(f"Image query error: {e}")
        logger.error(f"Stack trace: {stack_trace}")
        logger.error(f"File info: name='{file.filename}', content_type='{file.content_type}'")
        raise HTTPException(status_code=500, detail=f"Image query failed: {e}")"""
        
        new_exception_block = """    except HTTPException:
        # Re-raise HTTP exceptions without modification
        raise
    except Exception as e:
        import traceback
        stack_trace = traceback.format_exc()
        logger.error(f"Image query error: {e}")
        logger.error(f"Stack trace: {stack_trace}")
        logger.error(f"File info: name='{file.filename}', content_type='{file.content_type}'")
        raise HTTPException(status_code=500, detail=f"Image query failed: {e}")"""
        
        if old_exception_block in content:
            content = content.replace(old_exception_block, new_exception_block)
            print("✓ Fixed exception handling in image query endpoint")
        
        # Fix 4: Add retrieval config parsing
        old_query_call = """        # Get the query engine and run the query
        engine = get_query_engine()
        result = engine.query_advanced(
            question=extracted_text,
            retrieval_config=request.retrieval_config if request else None
        )"""
        
        new_query_call = """        # Parse retrieval config if provided
        parsed_config = None
        if retrieval_config:
            try:
                import json
                parsed_config = json.loads(retrieval_config)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid retrieval_config JSON")
        
        # Get the query engine and run the query
        engine = get_query_engine()
        result = engine.query_advanced(
            question=extracted_text,
            retrieval_config=parsed_config
        )"""
        
        if old_query_call in content:
            content = content.replace(old_query_call, new_query_call)
            print("✓ Added retrieval config parsing")
        
        # Fix 5: Update OpenRouter endpoint signature
        old_openrouter_signature = """@app.post("/analyze/image/openrouter")
async def openrouter_image_analysis_endpoint(file: UploadFile = File(...), prompt: Optional[str] = None):"""
        
        new_openrouter_signature = """@app.post("/analyze/image/openrouter")
async def openrouter_image_analysis_endpoint(
    file: UploadFile = File(...), 
    prompt: Optional[str] = Form(None)
):"""
        
        if old_openrouter_signature in content:
            content = content.replace(old_openrouter_signature, new_openrouter_signature)
            print("✓ Updated OpenRouter endpoint signature")
        
        # Write the fixed content back
        with open(main_py_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Successfully applied fixes to {main_py_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error applying fixes: {e}")
        # Restore backup
        shutil.copy2(backup_path, main_py_path)
        print(f"🔄 Restored backup from {backup_path}")
        return False

def main():
    """Main function to apply all fixes."""
    print("=" * 60)
    print("IMAGE ENDPOINT QUICK FIX SCRIPT")
    print("=" * 60)
    
    # Change to Backend directory if not already there
    if not os.path.exists("main.py"):
        if os.path.exists("Backend/main.py"):
            os.chdir("Backend")
            print("📁 Changed to Backend directory")
        else:
            print("❌ Could not find main.py file")
            return 1
    
    success = True
    
    # Apply fixes to main.py
    print("\n🔧 Applying fixes to main.py...")
    if not fix_main_py():
        success = False
    
    if success:
        print("\n✅ All fixes applied successfully!")
        print("\n📋 Summary of changes:")
        print("  • Added Form import to FastAPI imports")
        print("  • Updated image query endpoint to accept retrieval_config as Form parameter")
        print("  • Fixed exception handling to properly re-raise HTTPExceptions")
        print("  • Added JSON parsing for retrieval configuration")
        print("  • Updated OpenRouter endpoint to accept prompt as Form parameter")
        print("\n🧪 Run the tests again to verify the fixes:")
        print("  python run_image_tests.py")
        return 0
    else:
        print("\n❌ Some fixes failed. Check the error messages above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
