#!/usr/bin/env python3
"""
Test script that exactly simulates the frontend's analyze_image_with_ai function call.
"""

import requests
import os

def simulate_frontend_call():
    """Simulate the exact frontend call to analyze_image_with_ai."""
    
    # Find a test image
    image_path = "test_diagrams/aws_architecture_complex.png"
    if not os.path.exists(image_path):
        print(f"❌ Test image not found: {image_path}")
        return False
    
    print(f"📸 Using test image: {image_path}")
    
    # Read the image bytes (simulating file_content from frontend)
    with open(image_path, 'rb') as f:
        image_bytes = f.read()
    
    print(f"📊 Image size: {len(image_bytes)} bytes")
    
    # Simulate the exact frontend logic
    use_gemini = True
    custom_prompt = None
    
    # Choose the appropriate endpoint based on the use_gemini flag
    if use_gemini:
        url = "http://localhost:8888/analyze/image/gemini"
    else:
        url = "http://localhost:8888/analyze/image"
    
    files = {'file': ('test_image.png', image_bytes, 'image/png')}
    data = {}
    
    if use_gemini and custom_prompt:
        data['prompt'] = custom_prompt
    
    try:
        print(f"🚀 Sending request to {url}")
        response = requests.post(url, files=files, data=data, timeout=120)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Response keys: {list(result.keys())}")
            
            # Simulate the exact frontend processing
            print("\n=== SIMULATING FRONTEND PROCESSING ===")
            
            # Check if it's an error
            if result.get("status") == "error":
                error_msg = result.get("error", "Unknown error")
                print(f"❌ Error case: {error_msg}")
                return False
            else:
                # Extract text content if available
                extracted_text = result.get("extracted_text", "").strip()
                extracted_text_section = ""
                if extracted_text:
                    extracted_text_section = f"""
## 📝 **Extracted Text**

```
{extracted_text[:500]}
```
{'' if len(extracted_text) <= 500 else '*(text truncated)*'}

"""
                
                # Format the analysis
                analysis = result.get("analysis", "No analysis available")
                model_id = result.get("model_id", "Unknown model")
                
                print(f"📄 Raw analysis type: {type(analysis)}")
                print(f"📏 Raw analysis length: {len(analysis) if analysis else 0}")
                print(f"📖 Raw analysis preview: {repr(analysis[:100]) if analysis else 'None'}")
                
                # Ensure analysis is a string and not empty
                if not analysis or not isinstance(analysis, str):
                    print("⚠️  Analysis failed validation - using fallback")
                    analysis = "No analysis content received from the AI model."
                else:
                    print("✅ Analysis passed validation")
                
                # Clean the analysis content (only normalize line endings)
                analysis_clean = analysis.replace('\r\n', '\n').replace('\r', '\n')
                
                result_content = f"""# 🔍 Image Analysis Results

## 🤖 AI Analysis

{analysis_clean}

{extracted_text_section}---
*Analysis performed by: {model_id}*"""
                
                print("\n=== FINAL RESULT CONTENT ===")
                print(result_content)
                
                return True
        else:
            error_text = response.text
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {error_text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

if __name__ == "__main__":
    success = simulate_frontend_call()
    if success:
        print("\n✅ Frontend call simulation successful!")
    else:
        print("\n❌ Frontend call simulation failed!")
