#!/usr/bin/env python3
"""
Quick test to verify the backend Gemini endpoint is still working correctly.
"""

import requests
import os

def test_gemini_endpoint():
    """Test the Gemini endpoint with a sample image."""
    
    # Use a sample image file if available
    image_files = [
        "test_diagrams/aws_architecture_complex.png",
        "test_diagrams/aws_architecture_simple.png",
        "test_diagrams/process_map_simple.png",
        "test_data/test_diagram_components.png",
        "AWS_Architecture.png",
        "test_image.png",
        "sample.png"
    ]
    
    image_path = None
    for img_file in image_files:
        if os.path.exists(img_file):
            image_path = img_file
            break
    
    if not image_path:
        print("❌ No test image found. Please ensure you have an image file available.")
        return False
    
    print(f"📸 Using image: {image_path}")
    
    try:
        with open(image_path, 'rb') as f:
            files = {'file': (image_path, f, 'image/png')}
            
            print("🚀 Sending request to Gemini endpoint...")
            response = requests.post(
                'http://localhost:8888/analyze/image/gemini',
                files=files,
                timeout=60
            )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Response keys: {list(result.keys())}")
            
            analysis = result.get('analysis', '')
            status = result.get('status', '')
            model_id = result.get('model_id', '')
            
            print(f"📝 Status: {status}")
            print(f"🤖 Model: {model_id}")
            print(f"📄 Analysis type: {type(analysis)}")
            print(f"📏 Analysis length: {len(analysis) if analysis else 0}")
            
            if analysis:
                print(f"📖 Analysis preview: {analysis[:100]}...")
                return True
            else:
                print("❌ Analysis is empty!")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

if __name__ == "__main__":
    success = test_gemini_endpoint()
    if success:
        print("\n✅ Backend Gemini endpoint is working correctly!")
    else:
        print("\n❌ Backend Gemini endpoint has issues!")
