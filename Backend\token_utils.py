"""
Token usage tracking utilities for the RAG system.

This module provides utilities for tracking token usage from AWS Bedrock ChatBedrock responses,
including input/output token counting and session-based tracking.
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
except ImportError:
    TIKTOKEN_AVAILABLE = False
    tiktoken = None

logger = logging.getLogger(__name__)


@dataclass
class TokenUsage:
    """Data class for tracking token usage information."""
    input_tokens: int = 0
    output_tokens: int = 0
    total_tokens: int = 0
    model_name: Optional[str] = None
    timestamp: Optional[str] = None
    
    def __post_init__(self):
        """Calculate total tokens if not provided."""
        if self.total_tokens == 0:
            self.total_tokens = self.input_tokens + self.output_tokens
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
    
    def __add__(self, other: 'TokenUsage') -> 'TokenUsage':
        """Add two TokenUsage objects together."""
        if not isinstance(other, TokenUsage):
            return NotImplemented
        
        return TokenUsage(
            input_tokens=self.input_tokens + other.input_tokens,
            output_tokens=self.output_tokens + other.output_tokens,
            total_tokens=self.total_tokens + other.total_tokens,
            model_name=self.model_name or other.model_name,
            timestamp=self.timestamp  # Keep the original timestamp
        )


@dataclass
class SessionTokenUsage:
    """Data class for tracking cumulative token usage in a session."""
    session_id: str
    total_queries: int = 0
    cumulative_input_tokens: int = 0
    cumulative_output_tokens: int = 0
    cumulative_total_tokens: int = 0
    query_history: List[TokenUsage] = None
    session_start: Optional[str] = None
    last_updated: Optional[str] = None
    
    def __post_init__(self):
        """Initialize session tracking."""
        if self.query_history is None:
            self.query_history = []
        if self.session_start is None:
            self.session_start = datetime.now().isoformat()
        if self.last_updated is None:
            self.last_updated = datetime.now().isoformat()
    
    def add_query_usage(self, token_usage: TokenUsage) -> None:
        """Add token usage from a new query to the session."""
        self.query_history.append(token_usage)
        self.total_queries += 1
        self.cumulative_input_tokens += token_usage.input_tokens
        self.cumulative_output_tokens += token_usage.output_tokens
        self.cumulative_total_tokens += token_usage.total_tokens
        self.last_updated = datetime.now().isoformat()
    
    def get_average_tokens_per_query(self) -> Dict[str, float]:
        """Calculate average token usage per query."""
        if self.total_queries == 0:
            return {"input": 0.0, "output": 0.0, "total": 0.0}
        
        return {
            "input": self.cumulative_input_tokens / self.total_queries,
            "output": self.cumulative_output_tokens / self.total_queries,
            "total": self.cumulative_total_tokens / self.total_queries
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data["query_history"] = [usage.to_dict() for usage in self.query_history]
        data["average_per_query"] = self.get_average_tokens_per_query()
        return data


class TokenTracker:
    """Utility class for tracking token usage across the RAG system."""
    
    def __init__(self):
        """Initialize the token tracker."""
        self.sessions: Dict[str, SessionTokenUsage] = {}
        self.encoding = None
        self._initialize_encoding()
    
    def _initialize_encoding(self):
        """Initialize tiktoken encoding for fallback token counting."""
        if not TIKTOKEN_AVAILABLE:
            logger.info("tiktoken not available, using fallback token estimation")
            self.encoding = None
            return

        try:
            # Use cl100k_base encoding which is used by GPT-4 and similar models
            self.encoding = tiktoken.get_encoding("cl100k_base")
        except Exception as e:
            logger.warning(f"Failed to initialize tiktoken encoding: {e}")
            self.encoding = None
    
    def extract_token_usage_from_response(self, response: Any, model_name: Optional[str] = None) -> TokenUsage:
        """
        Extract token usage information from a ChatBedrock response.
        
        Args:
            response: The response object from ChatBedrock.invoke()
            model_name: Optional model name for tracking
            
        Returns:
            TokenUsage object with extracted information
        """
        token_usage = TokenUsage(model_name=model_name)
        
        try:
            # Check if response has usage_metadata attribute (LangChain standard)
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                usage_meta = response.usage_metadata
                token_usage.input_tokens = usage_meta.get('input_tokens', 0)
                token_usage.output_tokens = usage_meta.get('output_tokens', 0)
                token_usage.total_tokens = usage_meta.get('total_tokens', 0)
                logger.debug(f"Extracted token usage from usage_metadata: {token_usage.to_dict()}")
                return token_usage
            
            # Check if response has response_metadata with token information
            if hasattr(response, 'response_metadata') and response.response_metadata:
                meta = response.response_metadata
                if 'usage' in meta:
                    usage = meta['usage']
                    token_usage.input_tokens = usage.get('input_tokens', 0)
                    token_usage.output_tokens = usage.get('output_tokens', 0)
                    token_usage.total_tokens = usage.get('total_tokens', 0)
                    logger.debug(f"Extracted token usage from response_metadata: {token_usage.to_dict()}")
                    return token_usage
            
            # If no usage metadata is available, log a warning
            logger.warning("No token usage metadata found in response. Token tracking may be incomplete.")
            
        except Exception as e:
            logger.error(f"Error extracting token usage from response: {e}")
        
        return token_usage
    
    def estimate_input_tokens(self, text: str) -> int:
        """
        Estimate input tokens for a given text using tiktoken.
        
        Args:
            text: Input text to estimate tokens for
            
        Returns:
            Estimated number of tokens
        """
        if not text:
            return 0
        
        if self.encoding is None:
            # Fallback: rough estimation (1 token ≈ 4 characters for English)
            return len(text) // 4
        
        try:
            return len(self.encoding.encode(text))
        except Exception as e:
            logger.warning(f"Error estimating tokens with tiktoken: {e}")
            # Fallback estimation
            return len(text) // 4
    
    def create_token_usage_from_text(self, input_text: str, output_text: str, 
                                   model_name: Optional[str] = None) -> TokenUsage:
        """
        Create TokenUsage object by estimating tokens from input and output text.
        
        Args:
            input_text: Input text (query)
            output_text: Output text (response)
            model_name: Optional model name
            
        Returns:
            TokenUsage object with estimated counts
        """
        input_tokens = self.estimate_input_tokens(input_text)
        output_tokens = self.estimate_input_tokens(output_text)  # Same estimation method
        
        return TokenUsage(
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            model_name=model_name
        )
    
    def get_or_create_session(self, session_id: str) -> SessionTokenUsage:
        """
        Get existing session or create a new one.
        
        Args:
            session_id: Unique session identifier
            
        Returns:
            SessionTokenUsage object
        """
        if session_id not in self.sessions:
            self.sessions[session_id] = SessionTokenUsage(session_id=session_id)
        return self.sessions[session_id]
    
    def track_query_usage(self, session_id: str, token_usage: TokenUsage) -> SessionTokenUsage:
        """
        Track token usage for a query in a specific session.
        
        Args:
            session_id: Session identifier
            token_usage: Token usage for the query
            
        Returns:
            Updated SessionTokenUsage object
        """
        session = self.get_or_create_session(session_id)
        session.add_query_usage(token_usage)
        return session
    
    def get_session_summary(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get summary of token usage for a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Dictionary with session summary or None if session doesn't exist
        """
        if session_id not in self.sessions:
            return None
        
        return self.sessions[session_id].to_dict()
    
    def clear_session(self, session_id: str) -> bool:
        """
        Clear a specific session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if session was cleared, False if it didn't exist
        """
        if session_id in self.sessions:
            del self.sessions[session_id]
            return True
        return False


# Global token tracker instance
_global_token_tracker = TokenTracker()


def get_token_tracker() -> TokenTracker:
    """Get the global token tracker instance."""
    return _global_token_tracker


def extract_token_usage(response: Any, model_name: Optional[str] = None) -> TokenUsage:
    """
    Convenience function to extract token usage from a response.
    
    Args:
        response: Response object from LLM
        model_name: Optional model name
        
    Returns:
        TokenUsage object
    """
    return get_token_tracker().extract_token_usage_from_response(response, model_name)


def track_session_usage(session_id: str, token_usage: TokenUsage) -> SessionTokenUsage:
    """
    Convenience function to track token usage for a session.
    
    Args:
        session_id: Session identifier
        token_usage: Token usage to track
        
    Returns:
        Updated SessionTokenUsage object
    """
    return get_token_tracker().track_query_usage(session_id, token_usage)
