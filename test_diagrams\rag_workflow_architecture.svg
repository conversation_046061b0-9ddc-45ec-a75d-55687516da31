<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1200px" height="700px" viewBox="-0.5 -0.5 1200 700">
  <defs/>
  <g>
    <!-- Title -->
    <rect x="0" y="0" width="1200" height="60" fill="none" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1198px; height: 1px; padding-top: 30px; margin-left: 1px;">
            <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 28px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">RAG System Architecture - AWS Cloud Documentation</div>
            </div>
          </div>
        </foreignObject>
        <text x="600" y="39" fill="#232F3E" font-family="Helvetica" font-size="28px" text-anchor="middle" font-weight="bold">RAG System Architecture - AWS Cloud Documentation</text>
      </switch>
    </g>
    
    <!-- Document Ingestion Section -->
    <rect x="40" y="80" width="1120" height="240" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1118px; height: 1px; padding-top: 87px; margin-left: 41px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Document Ingestion Pipeline</div>
            </div>
          </div>
        </foreignObject>
        <text x="600" y="107" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle" font-weight="bold">Document Ingestion Pipeline</text>
      </switch>
    </g>
    
    <!-- User Upload -->
    <ellipse cx="120" cy="200" rx="40" ry="40" fill="#232F3D" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 250px; margin-left: 81px;">
            <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">User</div>
            </div>
          </div>
        </foreignObject>
        <text x="120" y="250" fill="#FFFFFF" font-family="Helvetica" font-size="16px" text-anchor="middle">User</text>
      </switch>
    </g>
    
    <!-- S3 Bucket -->
    <rect x="240" y="160" width="80" height="80" fill="#277116" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 250px; margin-left: 241px;">
            <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">S3 Bucket</div>
            </div>
          </div>
        </foreignObject>
        <text x="280" y="250" fill="#FFFFFF" font-family="Helvetica" font-size="16px" text-anchor="middle">S3 Bucket</text>
      </switch>
    </g>
    
    <!-- Document Processor -->
    <rect x="400" y="160" width="80" height="80" fill="#D05C17" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 250px; margin-left: 401px;">
            <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Unstructured.io</div>
            </div>
          </div>
        </foreignObject>
        <text x="440" y="250" fill="#FFFFFF" font-family="Helvetica" font-size="16px" text-anchor="middle">Unstructur...</text>
      </switch>
    </g>
    
    <!-- Text Chunker -->
    <rect x="560" y="160" width="80" height="80" fill="#3334B9" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 250px; margin-left: 561px;">
            <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Text Chunker (LangChain)</div>
            </div>
          </div>
        </foreignObject>
        <text x="600" y="250" fill="#FFFFFF" font-family="Helvetica" font-size="16px" text-anchor="middle">Text Chunk...</text>
      </switch>
    </g>
    
    <!-- Bedrock Embedding -->
    <rect x="720" y="160" width="80" height="80" fill="#116D5B" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 250px; margin-left: 721px;">
            <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Bedrock Embedding</div>
            </div>
          </div>
        </foreignObject>
        <text x="760" y="250" fill="#FFFFFF" font-family="Helvetica" font-size="16px" text-anchor="middle">Bedrock Em...</text>
      </switch>
    </g>
    
    <!-- Qdrant Vector Store -->
    <rect x="880" y="160" width="80" height="80" fill="#3334B9" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 250px; margin-left: 881px;">
            <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Qdrant Vector Store</div>
            </div>
          </div>
        </foreignObject>
        <text x="920" y="250" fill="#FFFFFF" font-family="Helvetica" font-size="16px" text-anchor="middle">Qdrant Vec...</text>
      </switch>
    </g>
    
    <!-- Document Ingestion Flow -->
    <path d="M 160 200 L 240 200" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 320 200 L 400 200" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 480 200 L 560 200" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 640 200 L 720 200" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 800 200 L 880 200" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
    
    <!-- Flow Labels -->
    <rect x="160" y="160" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 175px; margin-left: 161px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">1. Upload</div>
            </div>
          </div>
        </foreignObject>
        <text x="200" y="179" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">1. Upload</text>
      </switch>
    </g>
    
    <rect x="320" y="160" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 175px; margin-left: 321px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">2. Process</div>
            </div>
          </div>
        </foreignObject>
        <text x="360" y="179" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">2. Process</text>
      </switch>
    </g>
    
    <rect x="480" y="160" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 175px; margin-left: 481px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">3. Chunk</div>
            </div>
          </div>
        </foreignObject>
        <text x="520" y="179" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">3. Chunk</text>
      </switch>
    </g>
    
    <rect x="640" y="160" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 175px; margin-left: 641px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">4. Embed</div>
            </div>
          </div>
        </foreignObject>
        <text x="680" y="179" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">4. Embed</text>
      </switch>
    </g>
    
    <rect x="800" y="160" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 175px; margin-left: 801px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">5. Store</div>
            </div>
          </div>
        </foreignObject>
        <text x="840" y="179" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">5. Store</text>
      </switch>
    </g>
    
    <!-- Query Processing Section -->
    <rect x="40" y="380" width="1120" height="280" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1118px; height: 1px; padding-top: 387px; margin-left: 41px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Query Processing Pipeline</div>
            </div>
          </div>
        </foreignObject>
        <text x="600" y="407" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle" font-weight="bold">Query Processing Pipeline</text>
      </switch>
    </g>
    
    <!-- User Query -->
    <ellipse cx="120" cy="500" rx="40" ry="40" fill="#232F3D" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 550px; margin-left: 81px;">
            <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">User</div>
            </div>
          </div>
        </foreignObject>
        <text x="120" y="550" fill="#FFFFFF" font-family="Helvetica" font-size="16px" text-anchor="middle">User</text>
      </switch>
    </g>
    
    <!-- Chainlit Frontend -->
    <rect x="240" y="460" width="80" height="80" fill="#5A30B5" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 550px; margin-left: 241px;">
            <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Chainlit Frontend</div>
            </div>
          </div>
        </foreignObject>
        <text x="280" y="550" fill="#FFFFFF" font-family="Helvetica" font-size="16px" text-anchor="middle">Chainlit F...</text>
      </switch>
    </g>
    
    <!-- FastAPI Backend -->
    <rect x="400" y="460" width="80" height="80" fill="#D05C17" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 550px; margin-left: 401px;">
            <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FastAPI Backend</div>
            </div>
          </div>
        </foreignObject>
        <text x="440" y="550" fill="#FFFFFF" font-family="Helvetica" font-size="16px" text-anchor="middle">FastAPI Ba...</text>
      </switch>
    </g>
    
    <!-- Advanced Retrieval -->
    <rect x="560" y="460" width="80" height="80" fill="#3334B9" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 550px; margin-left: 561px;">
            <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Advanced Retrieval</div>
            </div>
          </div>
        </foreignObject>
        <text x="600" y="550" fill="#FFFFFF" font-family="Helvetica" font-size="16px" text-anchor="middle">Advanced R...</text>
      </switch>
    </g>
    
    <!-- Qdrant Vector Store (Query) -->
    <rect x="720" y="460" width="80" height="80" fill="#3334B9" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 550px; margin-left: 721px;">
            <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Qdrant Vector Store</div>
            </div>
          </div>
        </foreignObject>
        <text x="760" y="550" fill="#FFFFFF" font-family="Helvetica" font-size="16px" text-anchor="middle">Qdrant Vec...</text>
      </switch>
    </g>
    
    <!-- Prompt Templates -->
    <rect x="880" y="460" width="80" height="80" fill="#3334B9" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 550px; margin-left: 881px;">
            <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Prompt Templates</div>
            </div>
          </div>
        </foreignObject>
        <text x="920" y="550" fill="#FFFFFF" font-family="Helvetica" font-size="16px" text-anchor="middle">Prompt Tem...</text>
      </switch>
    </g>
    
    <!-- Bedrock LLM -->
    <rect x="1040" y="460" width="80" height="80" fill="#116D5B" stroke="#ffffff" stroke-width="2" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 550px; margin-left: 1041px;">
            <div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Bedrock LLM</div>
            </div>
          </div>
        </foreignObject>
        <text x="1080" y="550" fill="#FFFFFF" font-family="Helvetica" font-size="16px" text-anchor="middle">Bedrock LLM</text>
      </switch>
    </g>
    
    <!-- Query Flow -->
    <path d="M 160 500 L 240 500" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 320 500 L 400 500" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 480 500 L 560 500" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 640 500 L 720 500" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 800 500 L 880 500" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
    <path d="M 960 500 L 1040 500" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
    
    <!-- Response Flow -->
    <path d="M 1040 580 L 160 580" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" stroke-dasharray="9 9" pointer-events="stroke"/>
    
    <!-- Flow Labels -->
    <rect x="160" y="460" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 475px; margin-left: 161px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">1. Query</div>
            </div>
          </div>
        </foreignObject>
        <text x="200" y="479" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">1. Query</text>
      </switch>
    </g>
    
    <rect x="320" y="460" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 475px; margin-left: 321px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">2. API Request</div>
            </div>
          </div>
        </foreignObject>
        <text x="360" y="479" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">2. API Requ...</text>
      </switch>
    </g>
    
    <rect x="480" y="460" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 475px; margin-left: 481px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">3. Process</div>
            </div>
          </div>
        </foreignObject>
        <text x="520" y="479" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">3. Process</text>
      </switch>
    </g>
    
    <rect x="640" y="460" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 475px; margin-left: 641px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">4. Search</div>
            </div>
          </div>
        </foreignObject>
        <text x="680" y="479" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">4. Search</text>
      </switch>
    </g>
    
    <rect x="800" y="460" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 475px; margin-left: 801px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">5. Prepare</div>
            </div>
          </div>
        </foreignObject>
        <text x="840" y="479" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">5. Prepare</text>
      </switch>
    </g>
    
    <rect x="960" y="460" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 475px; margin-left: 961px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">6. Generate</div>
            </div>
          </div>
        </foreignObject>
        <text x="1000" y="479" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">6. Generate</text>
      </switch>
    </g>
    
    <rect x="560" y="580" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 595px; margin-left: 561px;">
            <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">7. Response Flow</div>
            </div>
          </div>
        </foreignObject>
        <text x="600" y="599" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">7. Response...</text>
      </switch>
    </g>
    
    <!-- Vector Store Connection -->
    <path d="M 920 240 L 920 280 L 760 280 L 760 460" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" stroke-dasharray="9 9" pointer-events="stroke"/>
    <g transform="translate(-0.5 -0.5)">
      <switch>
        <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
          <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 350px; margin-left: 760px;">
            <div data-drawio-colors="color: #000000; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
              <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap; font-weight: bold;">Shared Vector Store</div>
            </div>
          </div>
        </foreignObject>
        <text x="760" y="355" fill="#000000" font-family="Helvetica" font-size="16px" text-anchor="middle" font-weight="bold">Shared Vector Store</text>
      </switch>
    </g>
    
    <!-- Arrowheads for all flows -->
    <!-- Ingestion Flow Arrowheads -->
    <path d="M 240 200 L 230 195 L 230 205 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
    <path d="M 400 200 L 390 195 L 390 205 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
    <path d="M 560 200 L 550 195 L 550 205 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
    <path d="M 720 200 L 710 195 L 710 205 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
    <path d="M 880 200 L 870 195 L 870 205 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
    
    <!-- Query Flow Arrowheads -->
    <path d="M 240 500 L 230 495 L 230 505 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
    <path d="M 400 500 L 390 495 L 390 505 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
    <path d="M 560 500 L 550 495 L 550 505 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
    <path d="M 720 500 L 710 495 L 710 505 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
    <path d="M 880 500 L 870 495 L 870 505 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
    <path d="M 1040 500 L 1030 495 L 1030 505 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
    
    <!-- Response Flow Arrowhead -->
    <path d="M 160 580 L 170 575 L 170 585 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
    
    <!-- Vector Store Connection Arrowhead -->
    <path d="M 760 460 L 755 450 L 765 450 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
  </g>
</svg>