/* Custom CSS for RAG_Quadrant Chainlit Application */

/* Main theme colors */
:root {
  --primary-color: #FF9900;  /* AWS Orange */
  --secondary-color: #232F3E;  /* AWS Dark Blue */
  --accent-color: #146EB4;  /* AWS Light Blue */
  --success-color: #16A085;
  --warning-color: #F39C12;
  --error-color: #E74C3C;
  --background-light: #F8F9FA;
  --text-primary: #2C3E50;
  --text-secondary: #7F8C8D;
}

/* Header customization */
.chainlit-header {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-color) 100%);
  border-bottom: 3px solid var(--primary-color);
}

.chainlit-header .chainlit-logo {
  color: var(--primary-color);
  font-weight: bold;
}

/* Chat message styling */
.chainlit-message {
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chainlit-message.user {
  background: linear-gradient(135deg, var(--accent-color) 0%, #1E88E5 100%);
  color: white;
}

.chainlit-message.assistant {
  background: var(--background-light);
  border-left: 4px solid var(--primary-color);
}

.chainlit-message.system {
  background: linear-gradient(135deg, var(--warning-color) 0%, #F4D03F 100%);
  color: var(--secondary-color);
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
}

/* Input area styling */
.chainlit-input-container {
  background: white;
  border-radius: 24px;
  border: 2px solid var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.chainlit-input-container:focus-within {
  border-color: var(--accent-color);
  box-shadow: 0 4px 16px rgba(20, 110, 180, 0.2);
}

/* Button styling */
.chainlit-button {
  background: linear-gradient(135deg, var(--primary-color) 0%, #FF8C00 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
}

.chainlit-button:hover {
  background: linear-gradient(135deg, #FF8C00 0%, var(--primary-color) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 153, 0, 0.3);
}

/* Profile selector styling */
.chainlit-profile-selector {
  background: white;
  border: 2px solid var(--primary-color);
  border-radius: 12px;
  padding: 8px;
}

.chainlit-profile-option {
  border-radius: 8px;
  transition: all 0.2s ease;
}

.chainlit-profile-option:hover {
  background: var(--background-light);
  transform: translateX(4px);
}

.chainlit-profile-option.active {
  background: linear-gradient(135deg, var(--primary-color) 0%, #FF8C00 100%);
  color: white;
}

/* Loading animation */
.chainlit-loading {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.chainlit-loading::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid var(--primary-color);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Source citations styling */
.source-citation {
  background: var(--background-light);
  border-left: 3px solid var(--accent-color);
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
  font-size: 0.9em;
}

.source-citation strong {
  color: var(--accent-color);
}

/* Error message styling */
.error-message {
  background: linear-gradient(135deg, var(--error-color) 0%, #C0392B 100%);
  color: white;
  border-radius: 8px;
  padding: 16px;
  margin: 12px 0;
  border-left: 4px solid #A93226;
}

.warning-message {
  background: linear-gradient(135deg, var(--warning-color) 0%, #E67E22 100%);
  color: white;
  border-radius: 8px;
  padding: 16px;
  margin: 12px 0;
  border-left: 4px solid #D35400;
}

.success-message {
  background: linear-gradient(135deg, var(--success-color) 0%, #138D75 100%);
  color: white;
  border-radius: 8px;
  padding: 16px;
  margin: 12px 0;
  border-left: 4px solid #117A65;
}

/* Responsive design */
@media (max-width: 768px) {
  .chainlit-message {
    margin-bottom: 12px;
  }
  
  .chainlit-input-container {
    border-radius: 16px;
  }
  
  .source-citation {
    padding: 8px;
    font-size: 0.85em;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --background-light: #2C3E50;
    --text-primary: #ECF0F1;
    --text-secondary: #BDC3C7;
  }
  
  .chainlit-message.assistant {
    background: var(--secondary-color);
    color: var(--text-primary);
  }
  
  .source-citation {
    background: var(--secondary-color);
    color: var(--text-primary);
  }
}

/* Accessibility improvements */
.chainlit-button:focus,
.chainlit-input-container:focus-within {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* Animation for message appearance */
.chainlit-message {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
