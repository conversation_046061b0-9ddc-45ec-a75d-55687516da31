[Unit]
Description=RAG Chainlit Frontend Service
Documentation=https://docs.chainlit.io/
After=network.target rag-api.service
Wants=network.target rag-api.service

[Service]
Type=simple
User=raguser
Group=raguser
WorkingDirectory=/opt/chainlit_rag
ExecStart=/opt/chainlit_rag/venv/bin/chainlit run chainlit_app.py \
    --host 0.0.0.0 \
    --port 80 \
    --headless
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/chainlit_rag /var/log/rag-frontend /tmp
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
RemoveIPC=true
PrivateDevices=true
ProtectClock=true
ProtectKernelLogs=true
ProtectProc=invisible
ProcSubset=pid
RestrictNamespaces=true
LockPersonality=true
MemoryDenyWriteExecute=true
RestrictAddressFamilies=AF_UNIX AF_INET AF_INET6
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM

# Environment
Environment=PATH=/opt/chainlit_rag/venv/bin:/usr/local/bin:/usr/bin:/bin
EnvironmentFile=/opt/chainlit_rag/.env

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=rag-frontend

[Install]
WantedBy=multi-user.target
