import os
from typing import List, Dict, Optional
from langchain_aws import BedrockEmbeddings
from langchain.schema import Document
from langchain.vectorstores.base import VectorStore
from langchain_community.vectorstores.utils import maximal_marginal_relevance
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue, NamedVector
from embedding_utils import MultiModalEmbeddings
import logging

logger = logging.getLogger(__name__)

COLLECTION_NAME = "documents"


class QdrantVectorStoreWrapper(VectorStore):
    """
    LangChain-compatible wrapper for Qdrant vector store.
    Provides retriever interface while maintaining compatibility with existing code.
    """

    def __init__(self, client: QdrantClient, text_embeddings: BedrockEmbeddings, image_embeddings: MultiModalEmbeddings, collection_name: str = COLLECTION_NAME):
        self.client = client
        self._text_embeddings = text_embeddings
        self._image_embeddings = image_embeddings
        self.collection_name = collection_name

    @property
    def embeddings(self):
        # For backward compatibility, return text embeddings by default
        return self._text_embeddings

    @embeddings.setter
    def embeddings(self, value):
        self._text_embeddings = value

    def add_texts(self, texts: List[str], metadatas: Optional[List[Dict]] = None) -> List[str]:
        """Add texts to the vector store."""
        if metadatas is None:
            metadatas = [{} for _ in texts]

        points = []
        ids = []

        for i, (text, metadata) in enumerate(zip(texts, metadatas)):
            point_id = metadata.get('id', f"doc_{i}_{hash(text) % 1000000}")
            ids.append(point_id)

            # Create payload
            payload = {
                "content": text,
                "source": metadata.get("source", "unknown"),
                "id": point_id,
                **metadata
            }

            if metadata.get("is_image") and not text: # This is a raw image vector
                image_data = metadata.get("base64_image")
                if image_data:
                    image_vector = self._image_embeddings.embed_images([image_data])[0]
                    # Use named vectors
                    vector = {
                        "image": image_vector
                    }
                    points.append(PointStruct(id=point_id, vector=vector, payload=payload))
            else: # This is a text or image summary document
                text_vector = self._text_embeddings.embed_query(text)
                # Use named vectors
                vector = {
                    "text": text_vector
                }
                points.append(PointStruct(id=point_id, vector=vector, payload=payload))

        # Upsert points
        self.client.upsert(collection_name=self.collection_name, points=points)
        return ids

    def similarity_search(self, query: str, k: int = 4, filter: Optional[Dict] = None, **kwargs) -> List[Document]:
        """Perform similarity search and return documents."""
        return self.similarity_search_with_score(query, k, filter, **kwargs)

    def similarity_search_with_score(self, query: str, k: int = 4, filter: Optional[Dict] = None, **kwargs) -> List[Document]:
        """Perform similarity search and return documents with scores."""
        # Generate query vector for text
        query_vector = self._text_embeddings.embed_query(query)

        # Build filter if provided
        qdrant_filter = None
        if filter:
            conditions = []
            for key, value in filter.items():
                conditions.append(FieldCondition(key=key, match=MatchValue(value=value)))
            if conditions:
                qdrant_filter = Filter(must=conditions)

        # Perform search using the 'text' vector
        search_result = self.client.search(
            collection_name=self.collection_name,
            query_vector=NamedVector(name="text", vector=query_vector),
            limit=k,
            with_payload=True,
            query_filter=qdrant_filter
        )

        # Convert to LangChain documents
        documents = []
        for hit in search_result:
            payload = hit.payload or {}
            content = payload.get("content", "")
            metadata = {
                "source": payload.get("source", "unknown"),
                "score": float(hit.score),
                "id": payload.get("id", "unknown")
            }
            # Add other metadata fields
            for key, value in payload.items():
                if key not in ["content", "source", "id"]:
                    metadata[key] = value

            documents.append(Document(page_content=content, metadata=metadata))

        return documents

    def similarity_search_by_vector(self, embedding: List[float], k: int = 4, filter: Optional[Dict] = None, vector_name: str = "text") -> List[Document]:
        """Perform similarity search using a vector."""
        # Build filter if provided
        qdrant_filter = None
        if filter:
            conditions = []
            for key, value in filter.items():
                conditions.append(FieldCondition(key=key, match=MatchValue(value=value)))
            if conditions:
                qdrant_filter = Filter(must=conditions)

        # Perform search
        search_result = self.client.search(
            collection_name=self.collection_name,
            query_vector=NamedVector(name=vector_name, vector=embedding),
            limit=k,
            with_payload=True,
            query_filter=qdrant_filter
        )

        # Convert to LangChain documents
        documents = []
        for hit in search_result:
            payload = hit.payload or {}
            content = payload.get("content", "")
            metadata = {
                "source": payload.get("source", "unknown"),
                "score": float(hit.score),
                "id": payload.get("id", "unknown")
            }
            documents.append(Document(page_content=content, metadata=metadata))

        return documents

    def max_marginal_relevance_search(self, query: str, k: int = 4, fetch_k: int = 20, lambda_mult: float = 0.5, filter: Optional[Dict] = None) -> List[Document]:
        """Perform MMR search for diverse results."""
        # Get more documents than needed
        docs = self.similarity_search_with_score(query, fetch_k, filter)

        if not docs:
            return []

        # Extract embeddings and documents
        embeddings = []
        documents = []
        for doc in docs:
            # Re-embed the document content for MMR calculation
            embedding = self.embeddings.embed_query(doc.page_content)
            embeddings.append(embedding)
            documents.append(doc)

        # Get query embedding
        query_embedding = self.embeddings.embed_query(query)

        # Apply MMR
        mmr_indices = maximal_marginal_relevance(
            query_embedding, embeddings, lambda_mult=lambda_mult, k=k
        )

        return [documents[i] for i in mmr_indices]

    def delete(self, ids: Optional[List[str]] = None) -> Optional[bool]:
        """Delete documents by IDs."""
        if ids:
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=ids
            )
            return True
        return False

    @classmethod
    def from_texts(cls, texts: List[str], embedding: BedrockEmbeddings, metadatas: Optional[List[Dict]] = None, **kwargs) -> "QdrantVectorStoreWrapper":
        """Create vector store from texts."""
        # Initialize client
        qdrant_path = kwargs.get("qdrant_path", os.getenv("QDRANT_PATH", "./vector_store"))
        collection_name = kwargs.get("collection_name", COLLECTION_NAME)

        os.makedirs(qdrant_path, exist_ok=True)
        client = QdrantClient(path=qdrant_path)

        # Create collection
        client.recreate_collection(
            collection_name=collection_name,
            vectors_config=VectorParams(size=1024, distance=Distance.COSINE)  # Using 1024 as per current implementation
        )

        # Create wrapper
        wrapper = cls(client, embedding, collection_name)

        # Add texts
        wrapper.add_texts(texts, metadatas)

        return wrapper

def get_bedrock_embeddings():
    """Get Bedrock embeddings with IAM role authentication support."""
    # Ensure environment variables are loaded
    from dotenv import load_dotenv
    load_dotenv()
    
    model_id = os.getenv("BEDROCK_EMBEDDING_MODEL_ID")
    region_name = os.getenv("AWS_REGION")
    aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
    aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
    
    # Log the configuration (without sensitive data)
    logger.info(f"Initializing Bedrock embeddings with model_id: {model_id}, region: {region_name}")
    
    if not model_id:
        logger.error("BEDROCK_EMBEDDING_MODEL_ID environment variable is not set")
        # Provide a default model ID as fallback
        model_id = "amazon.titan-embed-text-v2:0"
        logger.info(f"Using default model_id: {model_id}")
    
    if not region_name:
        logger.error("AWS_REGION environment variable is not set")
        # Provide a default region as fallback
        region_name = "us-east-1"
        logger.info(f"Using default region: {region_name}")
    
    try:
        return BedrockEmbeddings(
            model_id=model_id,
            region_name=region_name,
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key
        )
    except Exception as e:
        logger.error(f"Failed to initialize Bedrock embeddings: {e}")
        raise

def build_vectorstore(docs):
    """
    Build vector store using the new LangChain-compatible wrapper.
    Maintains backward compatibility with existing code.
    """
    qdrant_path = os.getenv("QDRANT_PATH", "./vector_store")
    os.makedirs(qdrant_path, exist_ok=True)
    client = QdrantClient(path=qdrant_path)
    text_embeddings = get_bedrock_embeddings()
    image_embeddings = MultiModalEmbeddings()

    # Always recreate the collection to ensure correct vector size and named vectors
    client.recreate_collection(
        collection_name=COLLECTION_NAME,
        vectors_config={
            "text": VectorParams(size=1024, distance=Distance.COSINE),
            "image": VectorParams(size=512, distance=Distance.COSINE),
        }
    )

    # Create wrapper
    wrapper = QdrantVectorStoreWrapper(client, text_embeddings, image_embeddings, COLLECTION_NAME)

    # Extract texts and metadata from documents
    texts = [doc.page_content for doc in docs]
    metadatas = [doc.metadata for doc in docs]

    # Add documents to vector store
    wrapper.add_texts(texts, metadatas)

    logger.info(f"[Qdrant] Upserted {len(docs)} documents to vector store")

    # Return the raw client for backward compatibility
    return client

# Global client instance for singleton pattern
_global_client = None

def load_vectorstore():
    """
    Load vector store and return LangChain-compatible wrapper with singleton client.
    """
    global _global_client

    if _global_client is None:
        qdrant_path = os.getenv("QDRANT_PATH", "./vector_store")

        try:
            _global_client = QdrantClient(path=qdrant_path)
            # Check if collection exists
            try:
                _global_client.get_collection(collection_name=COLLECTION_NAME)
            except Exception as e:
                logger.warning(f"Collection '{COLLECTION_NAME}' not found. Attempting to create it.")
                try:
                    _global_client.recreate_collection(
                        collection_name=COLLECTION_NAME,
                        vectors_config=VectorParams(size=1024, distance=Distance.COSINE)
                    )
                    logger.info(f"Successfully created collection '{COLLECTION_NAME}'.")
                except Exception as create_error:
                    logger.error(f"Failed to create collection '{COLLECTION_NAME}': {create_error}")
                    return None

        except RuntimeError as e:
            if "already accessed by another instance" in str(e):
                logger.warning(f"Qdrant storage locked, using in-memory client: {e}")
                _global_client = QdrantClient(":memory:")
            else:
                raise e

    embeddings = get_bedrock_embeddings()

    # Return wrapper for LangChain compatibility
    return QdrantVectorStoreWrapper(_global_client, embeddings, COLLECTION_NAME)

def load_vectorstore_client():
    """
    Load raw Qdrant client for backward compatibility using singleton pattern.
    """
    global _global_client

    if _global_client is None:
        qdrant_path = os.getenv("QDRANT_PATH", "./vector_store")

        try:
            _global_client = QdrantClient(path=qdrant_path)
        except RuntimeError as e:
            if "already accessed by another instance" in str(e):
                logger.warning(f"Qdrant storage locked, using in-memory client: {e}")
                _global_client = QdrantClient(":memory:")
            else:
                raise e

    return _global_client
