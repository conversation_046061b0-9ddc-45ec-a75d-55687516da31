# RAG Application Systemd Services

This directory contains systemd service files for deploying your RAG (Retrieval-Augmented Generation) application as system services on Linux.

## Architecture Overview

Based on analysis of your codebase, your RAG application consists of:

1. **RAG API Service** (`rag-api.service`) - FastAPI backend on port 8080
2. **RAG Frontend Service** (`rag-frontend.service`) - Chainlit web interface on port 80 (HTTP)

**Note**: Your application uses **local file-based Qdrant** (stored in `./vector_store`), not a separate Qdrant server, so no separate Qdrant service is needed.

## Service Dependencies

```
Frontend (80) → API (8080) → Local Qdrant (./vector_store)
```

## Quick Installation

### 1. Install Services
```bash
# Make the script executable
chmod +x systemd-services/install-services.sh

# Install systemd services
sudo systemd-services/install-services.sh install
```

### 2. Configure Environment
Edit the generated environment file:
```bash
sudo nano /opt/chainlit_rag/.env
```

Update these critical values:
```env
# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-actual-access-key
AWS_SECRET_ACCESS_KEY=your-actual-secret-key
BEDROCK_EMBEDDING_MODEL_ID=amazon.titan-embed-text-v1
BEDROCK_LLM_PROFILE_ARN=your-actual-bedrock-profile-arn

# S3 Configuration
S3_BUCKET_NAME=your-actual-s3-bucket-name
```

### 3. Start Services
```bash
sudo systemd-services/install-services.sh start
```

## Service Management

### Basic Commands
```bash
# Start all services
sudo systemctl start rag-api rag-frontend

# Stop all services
sudo systemctl stop rag-frontend rag-api

# Restart services
sudo systemctl restart rag-api rag-frontend

# Check status
sudo systemctl status rag-api
sudo systemctl status rag-frontend

# Enable auto-start on boot
sudo systemctl enable rag-api rag-frontend

# View logs
sudo journalctl -u rag-api -f
sudo journalctl -u rag-frontend -f
```

### Using the Management Script
```bash
# Show service status
sudo systemd-services/install-services.sh status

# Start services in correct order
sudo systemd-services/install-services.sh start

# Stop all services
sudo systemd-services/install-services.sh stop

# Restart all services
sudo systemd-services/install-services.sh restart
```

## Service Details

### RAG API Service (`rag-api.service`)
- **Description**: FastAPI backend with document ingestion and querying
- **Port**: 8080
- **Command**: `gunicorn main:app --bind 0.0.0.0:8080 --workers 2 --worker-class uvicorn.workers.UvicornWorker`
- **Dependencies**: Network, local Qdrant vector store
- **Logs**: `/var/log/rag-api/` and `journalctl -u rag-api`

### RAG Frontend Service (`rag-frontend.service`)
- **Description**: Chainlit web interface (public access)
- **Port**: 80 (HTTP)
- **Command**: `chainlit run chainlit_app.py --host 0.0.0.0 --port 80 --headless`
- **Dependencies**: Network, RAG API service
- **Logs**: `/var/log/rag-frontend/` and `journalctl -u rag-frontend`
- **Note**: Requires sudo to bind to port 80

## File Locations

```
/opt/chainlit_rag/              # Application directory
├── .env                         # Environment configuration
├── main.py                      # FastAPI application
├── chainlit_app.py             # Chainlit frontend
├── vector_store/               # Local Qdrant storage
└── venv/                       # Python virtual environment

/etc/systemd/system/            # Systemd service files
├── rag-api.service
└── rag-frontend.service

/var/log/                       # Log directories
├── rag-api/
└── rag-frontend/
```

## Security Features

Both services include security hardening:
- Run as non-root user (`raguser`)
- Restricted file system access
- Limited system calls
- Memory protection
- Network restrictions

## Troubleshooting

### Service Won't Start
```bash
# Check service status
sudo systemctl status rag-api

# Check logs for errors
sudo journalctl -u rag-api --no-pager -l

# Test manual startup
sudo -u raguser /opt/chainlit_rag/venv/bin/python /opt/chainlit_rag/main.py
```

### Port Conflicts
```bash
# Check what's using the ports
sudo netstat -tulpn | grep -E ':(8080|80)'

# Kill conflicting processes if needed
sudo fuser -k 8080/tcp
sudo fuser -k 80/tcp
```

### Permission Issues
```bash
# Fix ownership
sudo chown -R raguser:raguser /opt/chainlit_rag
sudo chown -R raguser:raguser /var/log/rag-api
sudo chown -R raguser:raguser /var/log/rag-frontend
```

### Vector Store Issues
```bash
# Check vector store directory
ls -la /opt/chainlit_rag/vector_store/

# Reset vector store if corrupted
sudo rm -rf /opt/chainlit_rag/vector_store/*
# Then re-run document ingestion
```

## Health Checks

### API Health Check
```bash
curl -f http://localhost:8080/health
```

### Frontend Health Check
```bash
curl -f http://localhost:80
# OR simply:
curl -f http://localhost/
```

### Full System Check
```bash
# Check all services
sudo systemctl is-active rag-api rag-frontend

# Check ports
sudo netstat -tulpn | grep -E ':(8080|80)'
```

## Uninstallation

```bash
# Stop and disable services
sudo systemctl stop rag-frontend rag-api
sudo systemctl disable rag-frontend rag-api

# Remove service files
sudo systemd-services/install-services.sh uninstall

# Optional: Remove application directory
sudo rm -rf /opt/chainlit_rag
```

## Notes

- The application uses local file-based Qdrant, not a server instance
- Services start in dependency order: API first, then Frontend
- Both services share the same Python virtual environment
- Environment variables are loaded from `/opt/chainlit_rag/.env`
- Logs are available both in files and via journalctl
