#!/bin/bash
# RAG Application Systemd Services Installation Script
# This script installs and configures systemd services for the RAG application

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_DIR="/opt/chainlit_rag"
LOG_DIR="/var/log"
SERVICES=("rag-api" "rag-frontend")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; exit 1; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
    fi
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    # Create log directories
    mkdir -p "${LOG_DIR}/rag-api" "${LOG_DIR}/rag-frontend"

    # Create vector store directory for local Qdrant
    mkdir -p "$APP_DIR/vector_store"
    
    # Set proper ownership
    if id "raguser" &>/dev/null; then
        chown -R raguser:raguser "${LOG_DIR}/rag-api" "${LOG_DIR}/rag-frontend"
        chown -R raguser:raguser "$APP_DIR" 2>/dev/null || warn "App directory not found"
    else
        warn "raguser not found - please create the user first"
    fi
    
    log "Directories created successfully"
}

# Install systemd service files
install_service_files() {
    log "Installing systemd service files..."
    
    for service in "${SERVICES[@]}"; do
        local service_file="${service}.service"
        local source_file="${SCRIPT_DIR}/${service_file}"
        local target_file="/etc/systemd/system/${service_file}"
        
        if [[ -f "$source_file" ]]; then
            cp "$source_file" "$target_file"
            chmod 644 "$target_file"
            log "Installed ${service_file}"
        else
            error "Service file ${service_file} not found in ${SCRIPT_DIR}"
        fi
    done
    
    log "All service files installed"
}

# Configure systemd
configure_systemd() {
    log "Configuring systemd..."
    
    # Reload systemd daemon
    systemctl daemon-reload
    
    # Enable services
    for service in "${SERVICES[@]}"; do
        systemctl enable "${service}.service"
        log "Enabled ${service}.service"
    done
    
    log "Systemd configuration completed"
}

# Create environment file if it doesn't exist
create_env_config() {
    local env_file="$APP_DIR/.env"

    if [[ ! -f "$env_file" ]]; then
        log "Creating environment configuration..."

        cat > "$env_file" << 'EOF'
# AWS Configuration - IAM Role Authentication (Recommended for EC2)
AWS_REGION=us-east-1

# For IAM role authentication, DO NOT set these credentials:
# AWS_ACCESS_KEY_ID=your-access-key-here
# AWS_SECRET_ACCESS_KEY=your-secret-key-here

# For local development or non-EC2 environments, uncomment and set:
# AWS_ACCESS_KEY_ID=your-access-key-here
# AWS_SECRET_ACCESS_KEY=your-secret-key-here

BEDROCK_EMBEDDING_MODEL_ID=amazon.titan-embed-text-v1
BEDROCK_LLM_PROFILE_ARN=your-bedrock-profile-arn

# S3 Configuration - UPDATE THIS VALUE
S3_BUCKET_NAME=your-s3-bucket-name

# Local Qdrant Configuration
QDRANT_PATH=./vector_store

# Application Configuration
RAG_API_HOST=0.0.0.0
RAG_API_PORT=8080
API_BASE_URL=http://localhost:8080
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=80
EOF

        chown raguser:raguser "$env_file" 2>/dev/null || true
        log "Environment configuration created"
        warn "Please update $env_file with your actual AWS credentials"
    else
        info "Environment configuration already exists"
    fi
}

# Verify installation
verify_installation() {
    log "Verifying installation..."
    
    # Check if service files exist
    for service in "${SERVICES[@]}"; do
        local service_file="/etc/systemd/system/${service}.service"
        if [[ -f "$service_file" ]]; then
            log "✓ ${service}.service file exists"
        else
            error "✗ ${service}.service file missing"
        fi
    done
    
    # Check if services are enabled
    for service in "${SERVICES[@]}"; do
        if systemctl is-enabled --quiet "${service}.service"; then
            log "✓ ${service}.service is enabled"
        else
            warn "✗ ${service}.service is not enabled"
        fi
    done
    
    log "Installation verification completed"
}

# Start services in correct order
start_services() {
    log "Starting services in dependency order..."

    # Start API service first
    log "Starting RAG API..."
    systemctl start rag-api.service
    sleep 15

    # Start Frontend service
    log "Starting RAG Frontend..."
    systemctl start rag-frontend.service
    sleep 10
    
    # Check service status
    for service in "${SERVICES[@]}"; do
        if systemctl is-active --quiet "${service}.service"; then
            log "✓ ${service}.service is running"
        else
            warn "✗ ${service}.service is not running"
        fi
    done
    
    log "Service startup completed"
}

# Show service status
show_status() {
    log "Service Status:"
    echo ""
    
    for service in "${SERVICES[@]}"; do
        echo -e "${BLUE}=== ${service}.service ===${NC}"
        systemctl status "${service}.service" --no-pager -l || true
        echo ""
    done
}

# Show help
show_help() {
    echo "RAG Application Systemd Services Installation Script"
    echo ""
    echo "Usage: $0 <command>"
    echo ""
    echo "Commands:"
    echo "  install     Install and configure systemd services"
    echo "  start       Start all services in correct order"
    echo "  stop        Stop all services"
    echo "  restart     Restart all services"
    echo "  status      Show service status"
    echo "  enable      Enable services to start on boot"
    echo "  disable     Disable services from starting on boot"

    echo "  uninstall   Remove systemd service files"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  sudo $0 install    # Install services"
    echo "  sudo $0 start      # Start all services"

    echo "  sudo $0 status     # Check service status"
}

# Main function
main() {
    local command="${1:-help}"
    
    case "$command" in
        "install")
            check_root
            create_directories
            create_env_config
            install_service_files
            configure_systemd
            verify_installation
            log "Installation completed successfully!"
            log "Run 'sudo $0 start' to start the services"
            ;;
        "start")
            check_root
            start_services
            ;;
        "stop")
            check_root
            log "Stopping services..."
            for service in "${SERVICES[@]}"; do
                systemctl stop "${service}.service" || true
            done
            log "All services stopped"
            ;;
        "restart")
            check_root
            log "Restarting services..."
            $0 stop
            sleep 5
            $0 start
            ;;
        "status")
            show_status
            ;;
        "enable")
            check_root
            for service in "${SERVICES[@]}"; do
                systemctl enable "${service}.service"
                log "Enabled ${service}.service"
            done
            ;;
        "disable")
            check_root
            for service in "${SERVICES[@]}"; do
                systemctl disable "${service}.service"
                log "Disabled ${service}.service"
            done
            ;;
        "uninstall")
            check_root
            log "Uninstalling systemd services..."
            for service in "${SERVICES[@]}"; do
                systemctl stop "${service}.service" || true
                systemctl disable "${service}.service" || true
                rm -f "/etc/systemd/system/${service}.service"
                log "Removed ${service}.service"
            done
            systemctl daemon-reload
            log "Uninstallation completed"
            ;;

        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
