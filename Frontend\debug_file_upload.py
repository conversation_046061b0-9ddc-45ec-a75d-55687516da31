#!/usr/bin/env python3
"""
Debug script to understand Chainlit file upload structure.
"""

import chainlit as cl
import os

@cl.on_chat_start
async def start():
    await cl.Message(
        content="🔍 **File Upload Debug**\n\nUpload any image file to see its structure.",
        author="Debug Assistant"
    ).send()

@cl.on_message
async def main(message: cl.Message):
    if message.content.strip().lower() == "/upload":
        # Request file upload
        files = await cl.AskFileMessage(
            content="Upload any image file for debugging",
            accept=["image/png", "image/jpeg", "image/jpg"],
            max_size_mb=10,
            timeout=60,
        ).send()
        
        if not files:
            await cl.Message(
                content="❌ No file uploaded",
                author="Debug"
            ).send()
            return
        
        file = files[0]
        
        # Debug file object
        debug_info = f"""
## 🔍 **File Object Debug Info**

**Type**: {type(file)}
**Available attributes**: {[attr for attr in dir(file) if not attr.startswith('_')]}

**File properties**:
- **name**: {getattr(file, 'name', 'N/A')}
- **size**: {getattr(file, 'size', 'N/A')}
- **type**: {getattr(file, 'type', 'N/A')}
- **path**: {getattr(file, 'path', 'N/A')}

**Has content attribute**: {hasattr(file, 'content')}
**Has path attribute**: {hasattr(file, 'path')}
**Has file attribute**: {hasattr(file, 'file')}
"""
        
        await cl.Message(
            content=debug_info,
            author="Debug"
        ).send()
        
        # Try to access content
        try:
            if hasattr(file, 'content'):
                content_size = len(file.content) if file.content else 0
                await cl.Message(
                    content=f"✅ **Content accessed via 'content' attribute**: {content_size} bytes",
                    author="Debug"
                ).send()
            elif hasattr(file, 'path') and file.path:
                with open(file.path, 'rb') as f:
                    content = f.read()
                    content_size = len(content)
                await cl.Message(
                    content=f"✅ **Content accessed via 'path' attribute**: {content_size} bytes",
                    author="Debug"
                ).send()
            else:
                await cl.Message(
                    content="❌ **Cannot access file content**",
                    author="Debug"
                ).send()
        except Exception as e:
            await cl.Message(
                content=f"❌ **Error accessing content**: {str(e)}",
                author="Debug"
            ).send()
    
    else:
        await cl.Message(
            content="Type `/upload` to test file upload debugging.",
            author="Debug Assistant"
        ).send()

if __name__ == "__main__":
    print("Run with: chainlit run debug_file_upload.py")
