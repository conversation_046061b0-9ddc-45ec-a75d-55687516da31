#!/usr/bin/env python3
"""
Test script to check if there's an issue with string formatting or invisible characters.
"""

import requests

def test_string_formatting():
    """Test the exact string formatting used in the frontend."""
    
    # Get a real response from the backend
    image_path = "test_diagrams/aws_architecture_complex.png"
    with open(image_path, 'rb') as f:
        image_bytes = f.read()
    
    files = {'file': ('test_image.png', image_bytes, 'image/png')}
    response = requests.post('http://localhost:8888/analyze/image/gemini', files=files, timeout=120)
    
    if response.status_code == 200:
        analysis_result = response.json()
        
        # Extract the analysis exactly as the frontend does
        extracted_text = analysis_result.get("extracted_text", "").strip()
        extracted_text_section = ""
        if extracted_text:
            extracted_text_section = f"""
## 📝 **Extracted Text**

```
{extracted_text[:500]}
```
{'' if len(extracted_text) <= 500 else '*(text truncated)*'}

"""
        
        analysis = analysis_result.get("analysis", "No analysis available")
        model_id = analysis_result.get("model_id", "Unknown model")
        
        print(f"Analysis type: {type(analysis)}")
        print(f"Analysis length: {len(analysis)}")
        print(f"Analysis bool: {bool(analysis)}")
        print(f"Analysis isinstance str: {isinstance(analysis, str)}")
        print(f"Analysis repr: {repr(analysis[:100])}")
        
        # Check for invisible characters
        analysis_bytes = analysis.encode('utf-8')
        print(f"Analysis bytes length: {len(analysis_bytes)}")
        print(f"Analysis bytes preview: {analysis_bytes[:100]}")
        
        # Test the validation logic
        if not analysis or not isinstance(analysis, str):
            print("❌ VALIDATION FAILED!")
            analysis = "No analysis content received from the AI model."
        else:
            print("✅ VALIDATION PASSED!")
        
        # Clean the analysis content
        analysis_clean = analysis.replace('\r\n', '\n').replace('\r', '\n')
        
        print(f"Cleaned analysis length: {len(analysis_clean)}")
        print(f"Cleaned analysis preview: {repr(analysis_clean[:100])}")
        
        # Test the exact string formatting
        result_content = f"""# 🔍 Image Analysis Results

## 🤖 AI Analysis

{analysis_clean}

{extracted_text_section}---
*Analysis performed by: {model_id}*"""
        
        print(f"\n=== FINAL RESULT CONTENT ===")
        print(f"Result content length: {len(result_content)}")
        print(f"Result content preview:")
        print(result_content[:500])
        print("...")
        
        # Check if the analysis is actually in the result
        if analysis_clean in result_content:
            print("✅ Analysis is present in result content")
        else:
            print("❌ Analysis is NOT present in result content")
        
        # Check for the fallback message
        if "No analysis content received from the AI model." in result_content:
            print("❌ Fallback message found in result content")
        else:
            print("✅ No fallback message in result content")
            
    else:
        print(f"❌ Backend request failed: {response.status_code}")

if __name__ == "__main__":
    test_string_formatting()
