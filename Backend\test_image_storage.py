"""
Tests for the image storage service.
"""
import os
import io
import time
import pytest
import shutil
import tempfile
from fastapi import UploadFile
from PIL import Image

from image_analysis.storage import TemporaryImageStore, AnalysisResultStore, SecureFileHandler
from image_analysis.models import ImageAnalysisRequest, ImageAnalysisResult, ImageType, AnalysisStatus, Analysis


def create_test_image(format="PNG", size=(100, 100), color=(255, 0, 0)):
    """Create a test image for storage."""
    img = Image.new("RGB", size, color)
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format=format)
    img_byte_arr.seek(0)
    return img_byte_arr


class TestSecureFileHandler:
    """Tests for the SecureFileHandler class."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.handler = SecureFileHandler()
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_encrypt_decrypt_file(self):
        """Test file encryption and decryption."""
        # Create a test file
        source_path = os.path.join(self.temp_dir, "test.txt")
        with open(source_path, "w") as f:
            f.write("Test content")
        
        # Encrypt the file
        encrypted_path = os.path.join(self.temp_dir, "test.enc")
        assert self.handler.encrypt_file(source_path, encrypted_path) is True
        
        # Verify encrypted file exists and is different from source
        assert os.path.exists(encrypted_path)
        with open(encrypted_path, "rb") as f:
            encrypted_content = f.read()
        with open(source_path, "rb") as f:
            original_content = f.read()
        assert encrypted_content != original_content
        
        # Decrypt the file
        decrypted_path = os.path.join(self.temp_dir, "test_decrypted.txt")
        assert self.handler.decrypt_file(encrypted_path, decrypted_path) is True
        
        # Verify decrypted content matches original
        with open(decrypted_path, "r") as f:
            decrypted_content = f.read()
        assert decrypted_content == "Test content"
    
    def test_secure_token(self):
        """Test secure token generation and validation."""
        # Generate a token
        data = "test_data"
        token = self.handler.generate_secure_token(data, 3600)
        
        # Validate the token
        validated_data = self.handler.validate_secure_token(token)
        assert validated_data == data
        
        # Test expired token
        expired_token = self.handler.generate_secure_token(data, -10)  # Expired 10 seconds ago
        assert self.handler.validate_secure_token(expired_token) is None


class TestTemporaryImageStore:
    """Tests for the TemporaryImageStore class."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.store = TemporaryImageStore(base_dir=self.temp_dir, retention_hours=1)
        self.secure_store = TemporaryImageStore(
            base_dir=os.path.join(self.temp_dir, "secure"),
            retention_hours=1,
            secure_storage=True
        )
    
    def teardown_method(self):
        """Clean up test environment."""
        # Stop cleanup threads
        self.store.cleanup_thread.stop()
        self.secure_store.cleanup_thread.stop()
        shutil.rmtree(self.temp_dir)
    
    @pytest.mark.asyncio
    async def test_store_image(self):
        """Test storing an image."""
        # Create a test image
        img_bytes = create_test_image()
        file = UploadFile(filename="test.png", file=img_bytes)
        file.content_type = "image/png"
        
        # Store the image
        file_path = await self.store.store_image(file)
        
        # Verify the file exists
        assert os.path.exists(file_path)
        assert file_path.startswith(self.temp_dir)
    
    @pytest.mark.asyncio
    async def test_store_image_secure(self):
        """Test storing an image with secure storage."""
        # Create a test image
        img_bytes = create_test_image()
        file = UploadFile(filename="test.png", file=img_bytes)
        file.content_type = "image/png"
        
        # Store the image
        file_path = await self.secure_store.store_image(file)
        
        # Verify the file exists and is in the encrypted directory
        assert os.path.exists(file_path)
        assert file_path.startswith(os.path.join(self.temp_dir, "secure", "encrypted"))
        assert file_path.endswith(".enc")
    
    def test_delete_image(self):
        """Test deleting an image."""
        # Create a test file
        file_path = os.path.join(self.temp_dir, "test_delete.png")
        with open(file_path, "w") as f:
            f.write("Test content")
        
        # Delete the file
        assert self.store.delete_image(file_path) is True
        
        # Verify the file no longer exists
        assert not os.path.exists(file_path)
    
    def test_cleanup_old_images(self):
        """Test cleaning up old images."""
        # Create test files with different modification times
        # Recent file (should not be deleted)
        recent_file = os.path.join(self.temp_dir, "recent.png")
        with open(recent_file, "w") as f:
            f.write("Recent file")
        
        # Old file (should be deleted)
        old_file = os.path.join(self.temp_dir, "old.png")
        with open(old_file, "w") as f:
            f.write("Old file")
        
        # Set old file's modification time to 2 hours ago
        old_time = time.time() - 7200  # 2 hours ago
        os.utime(old_file, (old_time, old_time))
        
        # Run cleanup
        deleted_count = self.store.cleanup_old_images()
        
        # Verify only the old file was deleted
        assert deleted_count == 1
        assert os.path.exists(recent_file)
        assert not os.path.exists(old_file)
    
    @pytest.mark.asyncio
    async def test_secure_url_generation(self):
        """Test secure URL generation and validation."""
        # Skip if not using secure storage
        if not self.secure_store.secure_handler:
            pytest.skip("Secure storage not enabled")
        
        # Create and store a test image
        img_bytes = create_test_image()
        file = UploadFile(filename="test.png", file=img_bytes)
        file.content_type = "image/png"
        file_path = await self.secure_store.store_image(file)
        
        # Generate a secure URL
        token = self.secure_store.generate_secure_url(file_path)
        assert token is not None
        
        # Get the image by token
        retrieved_path = self.secure_store.get_image_by_token(token)
        assert retrieved_path is not None
        assert os.path.exists(retrieved_path)


class TestAnalysisResultStore:
    """Tests for the AnalysisResultStore class."""
    
    def setup_method(self):
        """Set up test environment."""
        self.store = AnalysisResultStore(retention_hours=1)
        
        # Create test request
        self.request = ImageAnalysisRequest(
            original_filename="test.png",
            content_type="image/png",
            file_size=1024,
            storage_path="/path/to/image.png"
        )
        
        # Create test result
        self.result = ImageAnalysisResult(
            request_id=self.request.id,
            analysis_type=ImageType.ARCHITECTURE,
            confidence=0.95,
            image_url="/url/to/image.png",
            text_content="Sample text content",
            analysis=Analysis(
                summary="Sample analysis summary",
                details=[],
                recommendations=[]
            )
        )
    
    def test_store_request(self):
        """Test storing an analysis request."""
        request_id = self.store.store_request(self.request)
        assert request_id == self.request.id
        assert self.store.get_request(request_id) == self.request
    
    def test_store_result(self):
        """Test storing an analysis result."""
        result_id = self.store.store_result(self.result)
        assert result_id == self.result.id
        assert self.store.get_result(result_id) == self.result
    
    def test_get_result_by_request(self):
        """Test getting a result by request ID."""
        # Store request and result
        self.store.store_request(self.request)
        self.store.store_result(self.result)
        
        # Get result by request ID
        result = self.store.get_result_by_request(self.request.id)
        assert result == self.result
    
    def test_delete_request_and_result(self):
        """Test deleting a request and its associated result."""
        # Store request and result
        self.store.store_request(self.request)
        self.store.store_result(self.result)
        
        # Delete request and result
        assert self.store.delete_request_and_result(self.request.id) is True
        
        # Verify both are deleted
        assert self.store.get_request(self.request.id) is None
        assert self.store.get_result_by_request(self.request.id) is None
    
    def test_cleanup_old_results(self):
        """Test cleaning up old results."""
        # Create an old request (2 hours ago)
        old_request = ImageAnalysisRequest(
            original_filename="old.png",
            content_type="image/png",
            file_size=1024,
            storage_path="/path/to/old.png"
        )
        old_request.timestamp = old_request.timestamp - timedelta(hours=2)
        
        # Create an old result
        old_result = ImageAnalysisResult(
            request_id=old_request.id,
            analysis_type=ImageType.ERROR,
            confidence=0.9,
            image_url="/url/to/old.png",
            analysis=Analysis(summary="Old analysis", details=[], recommendations=[])
        )
        old_result.timestamp = old_result.timestamp - timedelta(hours=2)
        
        # Store both recent and old items
        self.store.store_request(self.request)
        self.store.store_result(self.result)
        self.store.store_request(old_request)
        self.store.store_result(old_result)
        
        # Run cleanup
        deleted_count = self.store.cleanup_old_results()
        
        # Verify only old items were deleted
        assert deleted_count == 1
        assert self.store.get_request(self.request.id) is not None
        assert self.store.get_result(self.result.id) is not None
        assert self.store.get_request(old_request.id) is None
        assert self.store.get_result(old_result.id) is None