#!/usr/bin/env python3
"""
Test script for image analysis functionality.
This script tests the image analysis pipeline without starting the full FastAPI server.
"""

import os
import sys
import base64
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Backend'))

def create_test_image():
    """Create a simple test image with text for testing."""
    # Create a simple test image
    img = Image.new('RGB', (400, 300), color='white')
    draw = ImageDraw.Draw(img)
    
    # Try to use a default font, fallback to basic if not available
    try:
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        font = ImageFont.load_default()
    
    # Draw some text and shapes to simulate an architecture diagram
    draw.text((50, 50), "AWS Architecture Diagram", fill='black', font=font)
    draw.rectangle([50, 100, 150, 150], outline='blue', width=2)
    draw.text((60, 115), "EC2", fill='blue', font=font)
    
    draw.rectangle([200, 100, 300, 150], outline='green', width=2)
    draw.text((210, 115), "RDS", fill='green', font=font)
    
    # Draw an arrow
    draw.line([150, 125, 200, 125], fill='red', width=3)
    draw.polygon([(195, 120), (200, 125), (195, 130)], fill='red')
    
    draw.text((50, 200), "Test Architecture", fill='black', font=font)
    
    # Convert to bytes
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    return buffer.getvalue()

def test_aws_bedrock_analysis():
    """Test AWS Bedrock image analysis."""
    print("=" * 60)
    print("TESTING AWS BEDROCK IMAGE ANALYSIS")
    print("=" * 60)
    
    try:
        from aws_utils import analyze_image_with_bedrock
        
        # Create test image
        print("Creating test image...")
        image_bytes = create_test_image()
        print(f"Test image created: {len(image_bytes)} bytes")
        
        # Test the analysis
        print("Analyzing image with AWS Bedrock...")
        result = analyze_image_with_bedrock(image_bytes)
        
        print(f"Status: {result.get('status', 'unknown')}")
        if result.get('status') == 'success':
            print(f"Model ID: {result.get('model_id', 'unknown')}")
            print(f"Analysis: {result.get('analysis', 'No analysis')[:200]}...")
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")
            
        return result
        
    except Exception as e:
        print(f"Error testing AWS Bedrock: {e}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "error": str(e)}

def test_openrouter_analysis():
    """Test OpenRouter image analysis."""
    print("\n" + "=" * 60)
    print("TESTING OPENROUTER IMAGE ANALYSIS")
    print("=" * 60)
    
    try:
        from openrouter_utils import analyze_image_with_openrouter
        
        # Create test image
        print("Creating test image...")
        image_bytes = create_test_image()
        print(f"Test image created: {len(image_bytes)} bytes")
        
        # Test the analysis
        print("Analyzing image with OpenRouter...")
        result = analyze_image_with_openrouter(image_bytes)
        
        print(f"Status: {result.get('status', 'unknown')}")
        if result.get('status') == 'success':
            print(f"Model ID: {result.get('model_id', 'unknown')}")
            print(f"Analysis: {result.get('analysis', 'No analysis')[:200]}...")
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")
            
        return result
        
    except Exception as e:
        print(f"Error testing OpenRouter: {e}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "error": str(e)}

def test_model_configuration():
    """Test the current model configuration."""
    print("\n" + "=" * 60)
    print("TESTING MODEL CONFIGURATION")
    print("=" * 60)
    
    # Check environment variables
    print("Environment Variables:")
    print(f"  AWS_REGION: {os.getenv('AWS_REGION', 'Not set')}")
    print(f"  AWS_ACCESS_KEY_ID: {'Set' if os.getenv('AWS_ACCESS_KEY_ID') else 'Not set'}")
    print(f"  AWS_SECRET_ACCESS_KEY: {'Set' if os.getenv('AWS_SECRET_ACCESS_KEY') else 'Not set'}")
    print(f"  BEDROCK_VISION_MODEL_ID: {os.getenv('BEDROCK_VISION_MODEL_ID', 'Not set (will use default)')}")
    print(f"  OPENROUTER_API_KEY: {'Set' if os.getenv('OPENROUTER_API_KEY') else 'Not set'}")
    print(f"  OPENROUTER_MODEL_ID: {os.getenv('OPENROUTER_MODEL_ID', 'Not set (will use default)')}")

def main():
    """Main test function."""
    print("RAG QUADRANT - IMAGE ANALYSIS TEST")
    print("=" * 60)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Test configuration
    test_model_configuration()
    
    # Test AWS Bedrock
    bedrock_result = test_aws_bedrock_analysis()
    
    # Test OpenRouter
    openrouter_result = test_openrouter_analysis()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"AWS Bedrock: {bedrock_result.get('status', 'unknown')}")
    print(f"OpenRouter: {openrouter_result.get('status', 'unknown')}")
    
    if bedrock_result.get('status') == 'success':
        print("✅ AWS Bedrock image analysis is working!")
    else:
        print("❌ AWS Bedrock image analysis failed")
        
    if openrouter_result.get('status') == 'success':
        print("✅ OpenRouter image analysis is working!")
    else:
        print("❌ OpenRouter image analysis failed")

if __name__ == "__main__":
    main()
